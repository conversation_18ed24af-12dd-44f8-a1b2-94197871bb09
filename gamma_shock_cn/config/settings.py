#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 配置管理模块

本模块包含系统的所有配置参数，包括：
- 监控标的配置
- 技术指标参数
- 信号阈值设置
- 邮件通知配置
- AI分析配置
- 数据源配置

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import os
from typing import Dict, List, Any
from dataclasses import dataclass
from pathlib import Path

# 加载环境变量
try:
    from dotenv import load_dotenv
    # 查找.env文件
    env_path = Path(__file__).parent.parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    # 如果没有安装python-dotenv，跳过
    pass


@dataclass
class MonitoringConfig:
    """监控标的配置"""
    # 主要监控标的
    PRIMARY_TARGETS = {
        'CSI1000': {
            'name': '中证1000股指期货',
            'symbol': '399852',  # 中证1000指数
            'weight': 0.4,
            'description': '小盘成长股代表'
        },
        'CSI500': {
            'name': '中证500ETF', 
            'symbol': '510500',  # 中证500ETF
            'weight': 0.35,
            'description': '中盘价值股代表'
        },
        'ChiNext': {
            'name': '创业板ETF',
            'symbol': '159915',  # 创业板ETF
            'weight': 0.25,
            'description': '创新成长股代表'
        }
    }
    
    # 辅助监控标的（用于市场情绪判断）
    AUXILIARY_TARGETS = {
        'SSE50': '510050',    # 上证50ETF
        'CSI300': '510300',   # 沪深300ETF
        'SZSE100': '159901'   # 深证100ETF
    }


@dataclass
class TechnicalConfig:
    """技术指标配置"""
    # EMA参数
    EMA_PERIODS = [5, 10, 20, 60]
    
    # KDJ参数
    KDJ_PERIOD = 9
    KDJ_M1 = 3
    KDJ_M2 = 3
    
    # MACD参数
    MACD_FAST = 12
    MACD_SLOW = 26
    MACD_SIGNAL = 9
    
    # 威廉指标变种参数
    WILLIAMS_PERIOD = 14
    WILLIAMS_SMOOTH = 3
    
    # 布林带参数
    BOLLINGER_PERIOD = 20
    BOLLINGER_STD = 2
    
    # RSI参数
    RSI_PERIOD = 14
    
    # 成交量指标参数
    VOLUME_MA_PERIOD = 20
    VOLUME_RATIO_THRESHOLD = 1.5


@dataclass
class SignalConfig:
    """信号配置"""
    # 六级信号分类阈值
    SIGNAL_THRESHOLDS = {
        'LEVEL_1': {'score': 90, 'name': '强烈看多', 'action': '重仓买入'},
        'LEVEL_2': {'score': 70, 'name': '看多', 'action': '适量买入'},
        'LEVEL_3': {'score': 50, 'name': '偏多', 'action': '小仓试探'},
        'LEVEL_4': {'score': -50, 'name': '偏空', 'action': '减仓观望'},
        'LEVEL_5': {'score': -70, 'name': '看空', 'action': '清仓离场'},
        'LEVEL_6': {'score': -90, 'name': '强烈看空', 'action': '空仓等待'}
    }
    
    # 信号确认机制
    CONFIRMATION_RULES = {
        'min_indicators': 3,      # 最少确认指标数量
        'consensus_threshold': 0.6,  # 一致性阈值
        'time_window': 5,         # 时间窗口（分钟）
        'volume_confirmation': True,  # 是否需要成交量确认
    }
    
    # 信号增强机制
    ENHANCEMENT_RULES = {
        'trend_alignment': 1.2,   # 趋势一致性加权
        'volume_surge': 1.15,     # 放量突破加权
        'multi_timeframe': 1.1,   # 多周期共振加权
        'market_sentiment': 1.05, # 市场情绪加权
    }


@dataclass
class EmailConfig:
    """邮件配置"""
    # SMTP服务器配置
    SMTP_SERVER = os.getenv('SMTP_SERVER', 'smtp.qq.com')
    SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
    SMTP_USE_TLS = True
    
    # 发送者信息
    SENDER_EMAIL = os.getenv('SENDER_EMAIL', '')
    SENDER_PASSWORD = os.getenv('SENDER_PASSWORD', '')
    SENDER_NAME = 'Gamma Shock 策略系统'
    
    # 接收者列表
    RECIPIENTS = os.getenv('EMAIL_RECIPIENTS', '').split(',') if os.getenv('EMAIL_RECIPIENTS') else []
    
    # 邮件模板配置
    EMAIL_TEMPLATES = {
        'signal_alert': {
            'subject': '[Gamma Shock] {signal_level} - {target_name}',
            'priority': 'high'
        },
        'daily_report': {
            'subject': '[Gamma Shock] 每日策略报告 - {date}',
            'priority': 'normal'
        },
        'system_error': {
            'subject': '[Gamma Shock] 系统异常告警',
            'priority': 'urgent'
        }
    }


@dataclass
class AIConfig:
    """AI分析配置"""
    # DeepSeek API配置
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', '')
    DEEPSEEK_BASE_URL = 'https://api.deepseek.com/v1'
    DEEPSEEK_MODEL = 'deepseek-chat'
    
    # AI分析参数
    AI_ANALYSIS_CONFIG = {
        'max_tokens': 1000,
        'temperature': 0.3,
        'timeout': 30,
        'retry_times': 3,
        'retry_delay': 1
    }
    
    # AI增强功能开关
    AI_FEATURES = {
        'signal_confirmation': True,   # 信号二次确认
        'market_analysis': True,       # 市场分析
        'risk_assessment': True,       # 风险评估
        'strategy_optimization': False # 策略优化（暂未开放）
    }


@dataclass
class DataConfig:
    """数据配置"""
    # 数据源配置
    DATA_SOURCE = 'akshare'  # 主要数据源
    
    # 数据获取参数
    DATA_FETCH_CONFIG = {
        'period': '1min',        # 数据周期
        'limit': 240,           # 获取条数（4小时数据）
        'timeout': 10,          # 超时时间
        'retry_times': 3,       # 重试次数
        'cache_duration': 60    # 缓存时长（秒）
    }
    
    # 数据存储配置
    DATA_STORAGE = {
        'enable_cache': True,
        'cache_path': './data/cache',
        'database_path': './data/gamma_shock.db',
        'log_path': './logs',
        'backup_days': 7,
        'auto_cleanup': True,
        'cleanup_days': 30,
        'auto_backup': True
    }


@dataclass
class SystemConfig:
    """系统配置"""
    # 运行模式
    DEBUG_MODE = os.getenv('DEBUG_MODE', 'False').lower() == 'true'
    PRODUCTION_MODE = not DEBUG_MODE
    
    # 调度配置
    SCHEDULE_CONFIG = {
        'market_hours_only': True,    # 仅在交易时间运行
        'check_interval': 60,         # 检查间隔（秒）
        'signal_cooldown': 300,       # 信号冷却时间（秒）
        'max_signals_per_hour': 10    # 每小时最大信号数
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO' if PRODUCTION_MODE else 'DEBUG',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_handler': True,
        'console_handler': True,
        'max_file_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5
    }
    
    # 性能配置
    PERFORMANCE_CONFIG = {
        'max_workers': 4,           # 最大工作线程数
        'connection_pool_size': 10, # 连接池大小
        'request_timeout': 30,      # 请求超时时间
        'memory_limit': 512         # 内存限制（MB）
    }


class Settings:
    """配置管理类"""
    
    def __init__(self):
        self.monitoring = MonitoringConfig()
        self.technical = TechnicalConfig()
        self.signal = SignalConfig()
        self.email = EmailConfig()
        self.ai = AIConfig()
        self.data = DataConfig()
        self.system = SystemConfig()
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置完整性"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 检查必要的环境变量
        required_env_vars = [
            'SENDER_EMAIL',
            'SENDER_PASSWORD', 
            'EMAIL_RECIPIENTS',
            'DEEPSEEK_API_KEY'
        ]
        
        for var in required_env_vars:
            if not os.getenv(var):
                validation_result['errors'].append(f'缺少必要的环境变量: {var}')
                validation_result['valid'] = False
        
        # 检查邮件配置
        if not self.email.RECIPIENTS:
            validation_result['warnings'].append('未配置邮件接收者')
        
        # 检查AI配置
        if not self.ai.DEEPSEEK_API_KEY:
            validation_result['warnings'].append('未配置DeepSeek API密钥，AI功能将不可用')
        
        return validation_result
    
    def get_target_symbols(self) -> List[str]:
        """获取所有监控标的代码"""
        symbols = []
        symbols.extend([target['symbol'] for target in self.monitoring.PRIMARY_TARGETS.values()])
        symbols.extend(list(self.monitoring.AUXILIARY_TARGETS.values()))
        return symbols
    
    def get_signal_level(self, score: float) -> Dict[str, Any]:
        """根据分数获取信号等级"""
        for level, config in self.signal.SIGNAL_THRESHOLDS.items():
            if score >= config['score']:
                return {
                    'level': level,
                    'name': config['name'],
                    'action': config['action'],
                    'score': score
                }
        
        # 默认返回最低等级
        return {
            'level': 'LEVEL_6',
            'name': '强烈看空',
            'action': '空仓等待',
            'score': score
        }


# 全局配置实例
settings = Settings()


if __name__ == '__main__':
    # 配置验证测试
    result = settings.validate_config()
    print("配置验证结果:")
    print(f"有效性: {result['valid']}")
    if result['errors']:
        print("错误:")
        for error in result['errors']:
            print(f"  - {error}")
    if result['warnings']:
        print("警告:")
        for warning in result['warnings']:
            print(f"  - {warning}")
    
    print("\n监控标的:")
    for symbol in settings.get_target_symbols():
        print(f"  - {symbol}")