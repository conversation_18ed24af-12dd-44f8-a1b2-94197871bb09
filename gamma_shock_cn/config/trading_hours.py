#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 交易时间管理模块

本模块负责管理A股市场的交易时间，包括：
- 交易日判断
- 交易时段检查
- 节假日处理
- 集合竞价时间管理
- 休市时间处理

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import pytz
import logging


@dataclass
class TradingSession:
    """交易时段数据类"""
    name: str
    start_time: datetime.time
    end_time: datetime.time
    description: str
    is_active: bool = True


class TradingHoursManager:
    """交易时间管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.timezone = pytz.timezone('Asia/Shanghai')
        
        # 定义交易时段
        self.trading_sessions = {
            'call_auction_morning': TradingSession(
                name='早盘集合竞价',
                start_time=datetime.time(9, 15),
                end_time=datetime.time(9, 25),
                description='早盘集合竞价时间'
            ),
            'morning_trading': TradingSession(
                name='早盘连续交易',
                start_time=datetime.time(9, 30),
                end_time=datetime.time(11, 30),
                description='早盘连续交易时间'
            ),
            'lunch_break': TradingSession(
                name='午间休市',
                start_time=datetime.time(11, 30),
                end_time=datetime.time(13, 0),
                description='午间休市时间',
                is_active=False
            ),
            'afternoon_trading': TradingSession(
                name='午盘连续交易',
                start_time=datetime.time(13, 0),
                end_time=datetime.time(15, 0),
                description='午盘连续交易时间'
            ),
            'call_auction_afternoon': TradingSession(
                name='尾盘集合竞价',
                start_time=datetime.time(14, 57),
                end_time=datetime.time(15, 0),
                description='尾盘集合竞价时间'
            )
        }
        
        # 2024年A股休市安排
        self.holidays_2024 = [
            # 元旦
            datetime.date(2024, 1, 1),
            # 春节
            datetime.date(2024, 2, 10),
            datetime.date(2024, 2, 11),
            datetime.date(2024, 2, 12),
            datetime.date(2024, 2, 13),
            datetime.date(2024, 2, 14),
            datetime.date(2024, 2, 15),
            datetime.date(2024, 2, 16),
            datetime.date(2024, 2, 17),
            # 清明节
            datetime.date(2024, 4, 4),
            datetime.date(2024, 4, 5),
            datetime.date(2024, 4, 6),
            # 劳动节
            datetime.date(2024, 5, 1),
            datetime.date(2024, 5, 2),
            datetime.date(2024, 5, 3),
            datetime.date(2024, 5, 4),
            datetime.date(2024, 5, 5),
            # 端午节
            datetime.date(2024, 6, 10),
            # 中秋节
            datetime.date(2024, 9, 15),
            datetime.date(2024, 9, 16),
            datetime.date(2024, 9, 17),
            # 国庆节
            datetime.date(2024, 10, 1),
            datetime.date(2024, 10, 2),
            datetime.date(2024, 10, 3),
            datetime.date(2024, 10, 4),
            datetime.date(2024, 10, 5),
            datetime.date(2024, 10, 6),
            datetime.date(2024, 10, 7),
        ]
        
        # 2025年A股休市安排（部分）
        self.holidays_2025 = [
            # 元旦
            datetime.date(2025, 1, 1),
            # 春节（预计）
            datetime.date(2025, 1, 28),
            datetime.date(2025, 1, 29),
            datetime.date(2025, 1, 30),
            datetime.date(2025, 1, 31),
            datetime.date(2025, 2, 1),
            datetime.date(2025, 2, 2),
            datetime.date(2025, 2, 3),
        ]
        
        # 合并所有节假日
        self.all_holidays = set(self.holidays_2024 + self.holidays_2025)
    
    def get_current_time(self) -> datetime.datetime:
        """获取当前北京时间"""
        return datetime.datetime.now(self.timezone)
    
    def is_trading_day(self, date: Optional[datetime.date] = None) -> bool:
        """判断是否为交易日"""
        if date is None:
            date = self.get_current_time().date()
        
        # 检查是否为周末
        if date.weekday() >= 5:  # 周六=5, 周日=6
            return False
        
        # 检查是否为节假日
        if date in self.all_holidays:
            return False
        
        return True
    
    def is_trading_time(self, dt: Optional[datetime.datetime] = None) -> bool:
        """判断是否为交易时间"""
        if dt is None:
            dt = self.get_current_time()
        
        # 首先检查是否为交易日
        if not self.is_trading_day(dt.date()):
            return False
        
        current_time = dt.time()
        
        # 检查是否在交易时段内
        morning_session = self.trading_sessions['morning_trading']
        afternoon_session = self.trading_sessions['afternoon_trading']
        
        is_morning = (morning_session.start_time <= current_time <= morning_session.end_time)
        is_afternoon = (afternoon_session.start_time <= current_time <= afternoon_session.end_time)
        
        return is_morning or is_afternoon
    
    def is_call_auction_time(self, dt: Optional[datetime.datetime] = None) -> bool:
        """判断是否为集合竞价时间"""
        if dt is None:
            dt = self.get_current_time()
        
        if not self.is_trading_day(dt.date()):
            return False
        
        current_time = dt.time()
        
        # 早盘集合竞价
        morning_auction = self.trading_sessions['call_auction_morning']
        if morning_auction.start_time <= current_time <= morning_auction.end_time:
            return True
        
        # 尾盘集合竞价
        afternoon_auction = self.trading_sessions['call_auction_afternoon']
        if afternoon_auction.start_time <= current_time <= afternoon_auction.end_time:
            return True
        
        return False
    
    def is_market_open(self, dt: Optional[datetime.datetime] = None) -> bool:
        """判断市场是否开放（包括集合竞价和连续交易）"""
        return self.is_trading_time(dt) or self.is_call_auction_time(dt)
    
    def get_current_session(self, dt: Optional[datetime.datetime] = None) -> Optional[TradingSession]:
        """获取当前交易时段"""
        if dt is None:
            dt = self.get_current_time()
        
        if not self.is_trading_day(dt.date()):
            return None
        
        current_time = dt.time()
        
        for session in self.trading_sessions.values():
            if session.start_time <= current_time <= session.end_time:
                return session
        
        return None
    
    def get_next_trading_time(self, dt: Optional[datetime.datetime] = None) -> datetime.datetime:
        """获取下一个交易时间"""
        if dt is None:
            dt = self.get_current_time()
        
        # 如果当前就是交易时间，返回当前时间
        if self.is_market_open(dt):
            return dt
        
        current_date = dt.date()
        current_time = dt.time()
        
        # 检查当天是否还有交易时段
        if self.is_trading_day(current_date):
            # 检查早盘集合竞价
            morning_auction = self.trading_sessions['call_auction_morning']
            if current_time < morning_auction.start_time:
                return datetime.datetime.combine(
                    current_date, 
                    morning_auction.start_time
                ).replace(tzinfo=self.timezone)
            
            # 检查早盘交易
            morning_trading = self.trading_sessions['morning_trading']
            if current_time < morning_trading.start_time:
                return datetime.datetime.combine(
                    current_date,
                    morning_trading.start_time
                ).replace(tzinfo=self.timezone)
            
            # 检查午盘交易
            afternoon_trading = self.trading_sessions['afternoon_trading']
            if current_time < afternoon_trading.start_time:
                return datetime.datetime.combine(
                    current_date,
                    afternoon_trading.start_time
                ).replace(tzinfo=self.timezone)
        
        # 寻找下一个交易日
        next_date = current_date + datetime.timedelta(days=1)
        while not self.is_trading_day(next_date):
            next_date += datetime.timedelta(days=1)
        
        # 返回下一个交易日的早盘集合竞价时间
        morning_auction = self.trading_sessions['call_auction_morning']
        return datetime.datetime.combine(
            next_date,
            morning_auction.start_time
        ).replace(tzinfo=self.timezone)
    
    def get_market_status(self, dt: Optional[datetime.datetime] = None) -> Dict[str, any]:
        """获取市场状态信息"""
        if dt is None:
            dt = self.get_current_time()
        
        current_session = self.get_current_session(dt)
        next_trading_time = self.get_next_trading_time(dt)
        
        status = {
            'current_time': dt,
            'is_trading_day': self.is_trading_day(dt.date()),
            'is_trading_time': self.is_trading_time(dt),
            'is_call_auction': self.is_call_auction_time(dt),
            'is_market_open': self.is_market_open(dt),
            'current_session': current_session.name if current_session else '休市',
            'next_trading_time': next_trading_time,
            'time_to_next_trading': next_trading_time - dt if next_trading_time > dt else datetime.timedelta(0)
        }
        
        return status
    
    def get_trading_calendar(self, start_date: datetime.date, end_date: datetime.date) -> List[datetime.date]:
        """获取指定日期范围内的交易日历"""
        trading_days = []
        current_date = start_date
        
        while current_date <= end_date:
            if self.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date += datetime.timedelta(days=1)
        
        return trading_days
    
    def get_remaining_trading_time_today(self, dt: Optional[datetime.datetime] = None) -> datetime.timedelta:
        """获取今日剩余交易时间"""
        if dt is None:
            dt = self.get_current_time()
        
        if not self.is_trading_day(dt.date()):
            return datetime.timedelta(0)
        
        current_time = dt.time()
        remaining_time = datetime.timedelta(0)
        
        # 计算早盘剩余时间
        morning_session = self.trading_sessions['morning_trading']
        if current_time < morning_session.end_time:
            if current_time >= morning_session.start_time:
                # 当前在早盘交易时间内
                morning_end = datetime.datetime.combine(dt.date(), morning_session.end_time)
                remaining_time += morning_end - dt
            else:
                # 早盘还未开始
                morning_start = datetime.datetime.combine(dt.date(), morning_session.start_time)
                morning_end = datetime.datetime.combine(dt.date(), morning_session.end_time)
                remaining_time += morning_end - morning_start
        
        # 计算午盘剩余时间
        afternoon_session = self.trading_sessions['afternoon_trading']
        if current_time < afternoon_session.end_time:
            if current_time >= afternoon_session.start_time:
                # 当前在午盘交易时间内
                afternoon_end = datetime.datetime.combine(dt.date(), afternoon_session.end_time)
                remaining_time += afternoon_end - dt
            else:
                # 午盘还未开始
                afternoon_start = datetime.datetime.combine(dt.date(), afternoon_session.start_time)
                afternoon_end = datetime.datetime.combine(dt.date(), afternoon_session.end_time)
                remaining_time += afternoon_end - afternoon_start
        
        return remaining_time
    
    def should_run_strategy(self, dt: Optional[datetime.datetime] = None) -> bool:
        """判断是否应该运行策略"""
        if dt is None:
            dt = self.get_current_time()
        
        # 只在交易时间运行策略
        return self.is_trading_time(dt)
    
    def get_market_phase(self, dt: Optional[datetime.datetime] = None) -> str:
        """获取市场阶段"""
        if dt is None:
            dt = self.get_current_time()
        
        if not self.is_trading_day(dt.date()):
            return 'closed'
        
        current_time = dt.time()
        
        # 早盘集合竞价
        if (self.trading_sessions['call_auction_morning'].start_time <= 
            current_time <= self.trading_sessions['call_auction_morning'].end_time):
            return 'morning_auction'
        
        # 早盘交易
        if (self.trading_sessions['morning_trading'].start_time <= 
            current_time <= self.trading_sessions['morning_trading'].end_time):
            return 'morning_trading'
        
        # 午间休市
        if (self.trading_sessions['lunch_break'].start_time <= 
            current_time <= self.trading_sessions['lunch_break'].end_time):
            return 'lunch_break'
        
        # 午盘交易
        if (self.trading_sessions['afternoon_trading'].start_time <= 
            current_time <= self.trading_sessions['afternoon_trading'].end_time):
            return 'afternoon_trading'
        
        # 尾盘集合竞价
        if (self.trading_sessions['call_auction_afternoon'].start_time <= 
            current_time <= self.trading_sessions['call_auction_afternoon'].end_time):
            return 'afternoon_auction'
        
        return 'closed'


# 全局交易时间管理器实例
trading_hours = TradingHoursManager()


if __name__ == '__main__':
    # 测试交易时间管理器
    import json
    
    print("=== 交易时间管理器测试 ===")
    
    # 获取当前市场状态
    status = trading_hours.get_market_status()
    print("\n当前市场状态:")
    for key, value in status.items():
        if isinstance(value, datetime.datetime):
            print(f"  {key}: {value.strftime('%Y-%m-%d %H:%M:%S')}")
        elif isinstance(value, datetime.timedelta):
            print(f"  {key}: {value}")
        else:
            print(f"  {key}: {value}")
    
    # 测试特定时间点
    test_times = [
        datetime.datetime(2024, 12, 19, 9, 20),   # 集合竞价
        datetime.datetime(2024, 12, 19, 10, 30),  # 早盘交易
        datetime.datetime(2024, 12, 19, 12, 0),   # 午间休市
        datetime.datetime(2024, 12, 19, 14, 30),  # 午盘交易
        datetime.datetime(2024, 12, 19, 16, 0),   # 收盘后
        datetime.datetime(2024, 12, 21, 10, 0),   # 周六
    ]
    
    print("\n时间点测试:")
    for test_time in test_times:
        phase = trading_hours.get_market_phase(test_time)
        is_open = trading_hours.is_market_open(test_time)
        print(f"  {test_time.strftime('%Y-%m-%d %H:%M')} - 阶段: {phase}, 开市: {is_open}")
    
    # 获取本月交易日历
    today = datetime.date.today()
    start_of_month = today.replace(day=1)
    if today.month == 12:
        end_of_month = today.replace(year=today.year + 1, month=1, day=1) - datetime.timedelta(days=1)
    else:
        end_of_month = today.replace(month=today.month + 1, day=1) - datetime.timedelta(days=1)
    
    trading_days = trading_hours.get_trading_calendar(start_of_month, end_of_month)
    print(f"\n本月交易日数量: {len(trading_days)}")
    print(f"本月交易日: {[day.strftime('%m-%d') for day in trading_days[:10]]}...")  # 只显示前10天