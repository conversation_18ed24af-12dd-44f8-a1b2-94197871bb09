# -*- coding: utf-8 -*-
"""
配置模块

包含系统配置、交易时间管理等功能
"""

from .settings import (
    Settings,
    MonitoringConfig,
    TechnicalConfig,
    SignalConfig,
    EmailConfig,
    AIConfig,
    DataConfig,
    SystemConfig
)
from .trading_hours import TradingHoursManager, TradingSession

# 创建全局配置实例
settings = Settings()

# 创建全局交易时间管理器实例
trading_hours = TradingHoursManager()

__version__ = "1.0.0"
__author__ = "Gamma Shock Team"

__all__ = [
    'Settings',
    'MonitoringConfig',
    'TechnicalConfig', 
    'SignalConfig',
    'EmailConfig',
    'AIConfig',
    'DataConfig',
    'SystemConfig',
    'TradingHoursManager',
    'TradingSession',
    'settings',
    'trading_hours'
]