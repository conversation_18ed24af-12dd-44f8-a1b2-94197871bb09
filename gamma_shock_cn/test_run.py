#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gamma Shock 测试运行脚本
忽略交易时间限制，强制运行一次监控
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量启用测试模式
os.environ['GAMMA_SHOCK_TEST_MODE'] = 'true'

from utils import setup_logging, get_logger
from main import GammaShockApp
from config import settings

async def main():
    """
    测试运行主函数
    """
    try:
        # 设置日志
        setup_logging(settings.system.LOGGING_CONFIG)
        logger = get_logger(__name__)
        
        logger.info("=" * 60)
        logger.info("Gamma Shock 测试模式启动")
        logger.info("忽略交易时间限制，强制执行监控")
        logger.info("=" * 60)
        
        # 创建应用实例
        app = GammaShockApp()
        
        # 初始化应用程序组件
        logger.info("初始化应用程序组件...")
        await app._initialize_components()
        
        # 手动设置测试模式
        if hasattr(app, 'market_monitor') and app.market_monitor:
            app.market_monitor.test_mode = True
            logger.info("已启用测试模式：忽略交易时间限制")
        
        # 运行一次监控循环
        logger.info("开始执行测试监控...")
        await app.run_once()
        
        logger.info("测试监控完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试运行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        logger.info("测试运行结束")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())