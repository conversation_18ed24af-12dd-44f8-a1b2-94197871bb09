#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助工具模块

本模块提供各种通用的辅助函数和工具类，包括数据处理、文件操作、时间处理、
数学计算、字符串处理、缓存管理等功能。

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

import os
import sys
import json
import pickle
import hashlib
import time
import re
import math
import random
import string
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union, Callable, Tuple, Set
from pathlib import Path
from functools import wraps, lru_cache
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from decimal import Decimal, ROUND_HALF_UP
import logging
import threading
from contextlib import contextmanager
import tempfile
import shutil
import gzip
import base64
from urllib.parse import urlparse, parse_qs
import warnings

# 尝试导入可选依赖
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from dateutil import parser as date_parser
    DATEUTIL_AVAILABLE = True
except ImportError:
    DATEUTIL_AVAILABLE = False


# ==================== 数据处理工具 ====================

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def is_number(value: Any) -> bool:
        """检查是否为数字"""
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_positive_number(value: Any) -> bool:
        """检查是否为正数"""
        try:
            return float(value) > 0
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_price(value: Any) -> bool:
        """检查是否为有效价格"""
        try:
            price = float(value)
            return price > 0 and price < 1e10  # 合理的价格范围
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_volume(value: Any) -> bool:
        """检查是否为有效成交量"""
        try:
            volume = int(value)
            return volume >= 0
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_date(value: Any) -> bool:
        """检查是否为有效日期"""
        if isinstance(value, datetime):
            return True
        
        if isinstance(value, str):
            try:
                if DATEUTIL_AVAILABLE:
                    date_parser.parse(value)
                else:
                    datetime.strptime(value, '%Y-%m-%d')
                return True
            except (ValueError, TypeError):
                return False
        
        return False
    
    @staticmethod
    def is_valid_symbol(symbol: str) -> bool:
        """检查是否为有效股票代码"""
        if not isinstance(symbol, str):
            return False
        
        # 去除空格
        symbol = symbol.strip().upper()
        
        # 检查长度
        if len(symbol) < 2 or len(symbol) > 10:
            return False
        
        # 检查字符（字母和数字）
        return symbol.replace('.', '').isalnum()
    
    @staticmethod
    def validate_market_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证市场数据"""
        errors = []
        
        required_fields = ['symbol', 'price', 'volume', 'timestamp']
        for field in required_fields:
            if field not in data:
                errors.append(f"缺少必需字段: {field}")
        
        if 'symbol' in data and not DataValidator.is_valid_symbol(data['symbol']):
            errors.append("无效的股票代码")
        
        if 'price' in data and not DataValidator.is_valid_price(data['price']):
            errors.append("无效的价格")
        
        if 'volume' in data and not DataValidator.is_valid_volume(data['volume']):
            errors.append("无效的成交量")
        
        if 'timestamp' in data and not DataValidator.is_valid_date(data['timestamp']):
            errors.append("无效的时间戳")
        
        return len(errors) == 0, errors


class DataCleaner:
    """数据清洗器"""
    
    @staticmethod
    def clean_numeric(value: Any, default: float = 0.0) -> float:
        """清洗数值数据"""
        if value is None or value == '':
            return default
        
        try:
            # 处理字符串中的特殊字符
            if isinstance(value, str):
                # 移除逗号、空格等
                value = value.replace(',', '').replace(' ', '')
                # 处理百分号
                if '%' in value:
                    value = value.replace('%', '')
                    return float(value) / 100
            
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def clean_string(value: Any, default: str = '') -> str:
        """清洗字符串数据"""
        if value is None:
            return default
        
        try:
            result = str(value).strip()
            # 移除多余的空格
            result = re.sub(r'\s+', ' ', result)
            return result
        except:
            return default
    
    @staticmethod
    def clean_symbol(symbol: str) -> str:
        """清洗股票代码"""
        if not symbol:
            return ''
        
        # 转换为大写并移除空格
        symbol = str(symbol).strip().upper()
        
        # 移除特殊字符（保留字母、数字和点）
        symbol = re.sub(r'[^A-Z0-9.]', '', symbol)
        
        return symbol
    
    @staticmethod
    def clean_percentage(value: Any) -> float:
        """清洗百分比数据"""
        if value is None or value == '':
            return 0.0
        
        try:
            if isinstance(value, str):
                # 移除百分号和空格
                value = value.replace('%', '').replace(' ', '')
                return float(value) / 100
            else:
                return float(value)
        except (ValueError, TypeError):
            return 0.0


# ==================== 时间处理工具 ====================

class TimeHelper:
    """时间处理辅助类"""
    
    # 中国时区
    CHINA_TZ = timezone(timedelta(hours=8))
    
    @staticmethod
    def now() -> datetime:
        """获取当前时间（中国时区）"""
        return datetime.now(TimeHelper.CHINA_TZ)
    
    @staticmethod
    def today() -> datetime:
        """获取今天开始时间"""
        now = TimeHelper.now()
        return now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    @staticmethod
    def parse_date(date_str: str, formats: Optional[List[str]] = None) -> Optional[datetime]:
        """解析日期字符串"""
        if not date_str:
            return None
        
        # 默认格式
        if formats is None:
            formats = [
                '%Y-%m-%d',
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d',
                '%Y/%m/%d %H:%M:%S',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y%m%d',
                '%Y-%m-%d %H:%M:%S.%f'
            ]
        
        # 尝试使用dateutil（如果可用）
        if DATEUTIL_AVAILABLE:
            try:
                return date_parser.parse(date_str)
            except:
                pass
        
        # 尝试各种格式
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        return None
    
    @staticmethod
    def format_date(dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """格式化日期"""
        if dt is None:
            return ''
        return dt.strftime(format_str)
    
    @staticmethod
    def is_trading_day(date: datetime) -> bool:
        """判断是否为交易日（简单版本，不考虑节假日）"""
        return date.weekday() < 5  # 周一到周五
    
    @staticmethod
    def is_trading_time(dt: Optional[datetime] = None) -> bool:
        """判断是否为交易时间"""
        if dt is None:
            dt = TimeHelper.now()
        
        # 检查是否为交易日
        if not TimeHelper.is_trading_day(dt):
            return False
        
        # 检查时间（A股交易时间：9:30-11:30, 13:00-15:00）
        time_str = dt.strftime('%H:%M')
        return ('09:30' <= time_str <= '11:30') or ('13:00' <= time_str <= '15:00')
    
    @staticmethod
    def get_trading_days(start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取指定期间的交易日"""
        trading_days = []
        current = start_date
        
        while current <= end_date:
            if TimeHelper.is_trading_day(current):
                trading_days.append(current)
            current += timedelta(days=1)
        
        return trading_days
    
    @staticmethod
    def get_next_trading_day(date: Optional[datetime] = None) -> datetime:
        """获取下一个交易日"""
        if date is None:
            date = TimeHelper.today()
        
        next_day = date + timedelta(days=1)
        while not TimeHelper.is_trading_day(next_day):
            next_day += timedelta(days=1)
        
        return next_day
    
    @staticmethod
    def get_previous_trading_day(date: Optional[datetime] = None) -> datetime:
        """获取上一个交易日"""
        if date is None:
            date = TimeHelper.today()
        
        prev_day = date - timedelta(days=1)
        while not TimeHelper.is_trading_day(prev_day):
            prev_day -= timedelta(days=1)
        
        return prev_day


# ==================== 数学计算工具 ====================

class MathHelper:
    """数学计算辅助类"""
    
    @staticmethod
    def safe_divide(a: float, b: float, default: float = 0.0) -> float:
        """安全除法"""
        try:
            if b == 0:
                return default
            return a / b
        except (TypeError, ZeroDivisionError):
            return default
    
    @staticmethod
    def percentage_change(old_value: float, new_value: float) -> float:
        """计算百分比变化"""
        if old_value == 0:
            return 0.0
        return ((new_value - old_value) / old_value) * 100
    
    @staticmethod
    def round_to_precision(value: float, precision: int = 2) -> float:
        """四舍五入到指定精度"""
        try:
            decimal_value = Decimal(str(value))
            rounded = decimal_value.quantize(
                Decimal('0.' + '0' * precision),
                rounding=ROUND_HALF_UP
            )
            return float(rounded)
        except:
            return round(value, precision)
    
    @staticmethod
    def clamp(value: float, min_val: float, max_val: float) -> float:
        """将值限制在指定范围内"""
        return max(min_val, min(value, max_val))
    
    @staticmethod
    def normalize(values: List[float], min_val: float = 0.0, max_val: float = 1.0) -> List[float]:
        """归一化数值列表"""
        if not values:
            return []
        
        min_value = min(values)
        max_value = max(values)
        
        if max_value == min_value:
            return [min_val] * len(values)
        
        range_val = max_value - min_value
        target_range = max_val - min_val
        
        return [
            min_val + ((v - min_value) / range_val) * target_range
            for v in values
        ]
    
    @staticmethod
    def moving_average(values: List[float], window: int) -> List[float]:
        """计算移动平均"""
        if len(values) < window:
            return values.copy()
        
        result = []
        for i in range(len(values)):
            if i < window - 1:
                result.append(values[i])
            else:
                avg = sum(values[i-window+1:i+1]) / window
                result.append(avg)
        
        return result
    
    @staticmethod
    def standard_deviation(values: List[float]) -> float:
        """计算标准差"""
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return math.sqrt(variance)
    
    @staticmethod
    def correlation(x: List[float], y: List[float]) -> float:
        """计算相关系数"""
        if len(x) != len(y) or len(x) < 2:
            return 0.0
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(xi ** 2 for xi in x)
        sum_y2 = sum(yi ** 2 for yi in y)
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = math.sqrt((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator


# ==================== 文件操作工具 ====================

class FileHelper:
    """文件操作辅助类"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def safe_read_json(file_path: Union[str, Path], default: Any = None) -> Any:
        """安全读取JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError, Exception):
            return default
    
    @staticmethod
    def safe_write_json(file_path: Union[str, Path], data: Any, indent: int = 2) -> bool:
        """安全写入JSON文件"""
        try:
            # 确保目录存在
            FileHelper.ensure_dir(Path(file_path).parent)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False, default=str)
            return True
        except Exception as e:
            logging.error(f"写入JSON文件失败: {e}")
            return False
    
    @staticmethod
    def safe_read_text(file_path: Union[str, Path], encoding: str = 'utf-8', default: str = '') -> str:
        """安全读取文本文件"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception:
            return default
    
    @staticmethod
    def safe_write_text(file_path: Union[str, Path], content: str, encoding: str = 'utf-8') -> bool:
        """安全写入文本文件"""
        try:
            FileHelper.ensure_dir(Path(file_path).parent)
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            logging.error(f"写入文本文件失败: {e}")
            return False
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """获取文件大小（字节）"""
        try:
            return Path(file_path).stat().st_size
        except:
            return 0
    
    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """获取文件哈希值"""
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception:
            return ''
    
    @staticmethod
    def backup_file(file_path: Union[str, Path], backup_dir: Optional[Union[str, Path]] = None) -> Optional[Path]:
        """备份文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return None
            
            if backup_dir is None:
                backup_dir = file_path.parent / 'backup'
            
            backup_dir = Path(backup_dir)
            FileHelper.ensure_dir(backup_dir)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            shutil.copy2(file_path, backup_path)
            return backup_path
            
        except Exception as e:
            logging.error(f"备份文件失败: {e}")
            return None
    
    @staticmethod
    def compress_file(file_path: Union[str, Path], output_path: Optional[Union[str, Path]] = None) -> Optional[Path]:
        """压缩文件"""
        try:
            file_path = Path(file_path)
            if output_path is None:
                output_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(output_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            return Path(output_path)
            
        except Exception as e:
            logging.error(f"压缩文件失败: {e}")
            return None


# ==================== 缓存管理工具 ====================

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存是否过期"""
        if key not in self.cache:
            return True
        
        cache_time = self.cache[key].get('timestamp', 0)
        return time.time() - cache_time > self.ttl
    
    def _cleanup(self):
        """清理过期和超出大小限制的缓存"""
        current_time = time.time()
        
        # 移除过期项
        expired_keys = [
            key for key in self.cache
            if current_time - self.cache[key].get('timestamp', 0) > self.ttl
        ]
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.access_times.pop(key, None)
        
        # 如果仍然超出大小限制，移除最少使用的项
        if len(self.cache) > self.max_size:
            # 按访问时间排序，移除最旧的项
            sorted_keys = sorted(self.access_times.items(), key=lambda x: x[1])
            keys_to_remove = sorted_keys[:len(self.cache) - self.max_size]
            
            for key, _ in keys_to_remove:
                self.cache.pop(key, None)
                self.access_times.pop(key, None)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self.lock:
            if self._is_expired(key):
                self.cache.pop(key, None)
                self.access_times.pop(key, None)
                return default
            
            self.access_times[key] = time.time()
            return self.cache[key]['value']
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self.lock:
            current_time = time.time()
            
            self.cache[key] = {
                'value': value,
                'timestamp': current_time
            }
            self.access_times[key] = current_time
            
            # 清理缓存
            if len(self.cache) > self.max_size:
                self._cleanup()
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                self.access_times.pop(key, None)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def keys(self) -> List[str]:
        """获取所有缓存键"""
        with self.lock:
            return list(self.cache.keys())
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            current_time = time.time()
            expired_count = sum(
                1 for cache_data in self.cache.values()
                if current_time - cache_data.get('timestamp', 0) > self.ttl
            )
            
            return {
                'total_items': len(self.cache),
                'expired_items': expired_count,
                'max_size': self.max_size,
                'ttl': self.ttl,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
            }


# 全局缓存实例
_global_cache = CacheManager()


def cached(ttl: int = 3600, key_func: Optional[Callable] = None):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            result = _global_cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            _global_cache.set(cache_key, result)
            
            return result
        
        return wrapper
    return decorator


# ==================== 字符串处理工具 ====================

class StringHelper:
    """字符串处理辅助类"""
    
    @staticmethod
    def generate_random_string(length: int = 8, chars: str = None) -> str:
        """生成随机字符串"""
        if chars is None:
            chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    @staticmethod
    def camel_to_snake(name: str) -> str:
        """驼峰命名转下划线命名"""
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    @staticmethod
    def snake_to_camel(name: str, first_upper: bool = False) -> str:
        """下划线命名转驼峰命名"""
        components = name.split('_')
        if first_upper:
            return ''.join(word.capitalize() for word in components)
        else:
            return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    @staticmethod
    def truncate(text: str, max_length: int, suffix: str = '...') -> str:
        """截断文本"""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def extract_numbers(text: str) -> List[float]:
        """从文本中提取数字"""
        pattern = r'-?\d+(?:\.\d+)?'
        matches = re.findall(pattern, text)
        return [float(match) for match in matches]
    
    @staticmethod
    def clean_whitespace(text: str) -> str:
        """清理空白字符"""
        # 移除首尾空白
        text = text.strip()
        # 将多个空白字符替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        return text
    
    @staticmethod
    def mask_sensitive_info(text: str, mask_char: str = '*') -> str:
        """遮蔽敏感信息"""
        # 遮蔽邮箱
        text = re.sub(r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                     lambda m: f"{m.group(1)[:2]}{mask_char * 3}@{m.group(2)}", text)
        
        # 遮蔽手机号
        text = re.sub(r'(\d{3})(\d{4})(\d{4})',
                     lambda m: f"{m.group(1)}{mask_char * 4}{m.group(3)}", text)
        
        # 遮蔽身份证号
        text = re.sub(r'(\d{6})(\d{8})(\d{4})',
                     lambda m: f"{m.group(1)}{mask_char * 8}{m.group(3)}", text)
        
        return text


# ==================== 性能监控工具 ====================

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = defaultdict(list)
        self.lock = threading.RLock()
    
    @contextmanager
    def measure(self, name: str):
        """测量执行时间的上下文管理器"""
        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            with self.lock:
                self.metrics[name].append(execution_time)
    
    def record_metric(self, name: str, value: float):
        """记录指标"""
        with self.lock:
            self.metrics[name].append(value)
    
    def get_stats(self, name: str) -> Dict[str, float]:
        """获取指标统计信息"""
        with self.lock:
            values = self.metrics.get(name, [])
            if not values:
                return {}
            
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'total': sum(values)
            }
    
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """获取所有指标统计信息"""
        with self.lock:
            return {name: self.get_stats(name) for name in self.metrics.keys()}
    
    def clear_metrics(self, name: Optional[str] = None):
        """清除指标"""
        with self.lock:
            if name:
                self.metrics.pop(name, None)
            else:
                self.metrics.clear()


# 全局性能监控器
_global_monitor = PerformanceMonitor()


def measure_time(name: str):
    """性能测量装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            with _global_monitor.measure(name or func.__name__):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# ==================== 配置管理工具 ====================

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        self.config_file = Path(config_file) if config_file else None
        self.config: Dict[str, Any] = {}
        self.lock = threading.RLock()
        
        if self.config_file and self.config_file.exists():
            self.load()
    
    def load(self, config_file: Optional[Union[str, Path]] = None) -> bool:
        """加载配置"""
        try:
            file_path = Path(config_file) if config_file else self.config_file
            if not file_path or not file_path.exists():
                return False
            
            with self.lock:
                self.config = FileHelper.safe_read_json(file_path, {})
                self.config_file = file_path
            
            return True
        except Exception as e:
            logging.error(f"加载配置失败: {e}")
            return False
    
    def save(self, config_file: Optional[Union[str, Path]] = None) -> bool:
        """保存配置"""
        try:
            file_path = Path(config_file) if config_file else self.config_file
            if not file_path:
                return False
            
            with self.lock:
                return FileHelper.safe_write_json(file_path, self.config)
        except Exception as e:
            logging.error(f"保存配置失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值（支持点号分隔的嵌套键）"""
        with self.lock:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值（支持点号分隔的嵌套键）"""
        with self.lock:
            keys = key.split('.')
            config = self.config
            
            for k in keys[:-1]:
                if k not in config or not isinstance(config[k], dict):
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
    
    def delete(self, key: str) -> bool:
        """删除配置项"""
        with self.lock:
            keys = key.split('.')
            config = self.config
            
            for k in keys[:-1]:
                if k not in config or not isinstance(config[k], dict):
                    return False
                config = config[k]
            
            if keys[-1] in config:
                del config[keys[-1]]
                return True
            
            return False
    
    def update(self, new_config: Dict[str, Any]) -> None:
        """更新配置"""
        with self.lock:
            self._deep_update(self.config, new_config)
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        with self.lock:
            return self.config.copy()


# ==================== 其他实用工具 ====================

def retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0, 
         exceptions: Tuple = (Exception,)):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            current_delay = delay
            
            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    attempt += 1
                    if attempt >= max_attempts:
                        raise e
                    
                    logging.warning(f"函数 {func.__name__} 第 {attempt} 次尝试失败: {e}")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            return None
        return wrapper
    return decorator


def singleton(cls):
    """单例装饰器"""
    instances = {}
    lock = threading.Lock()
    
    def get_instance(*args, **kwargs):
        if cls not in instances:
            with lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance


def deprecated(reason: str = ""):
    """废弃警告装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            warnings.warn(
                f"函数 {func.__name__} 已废弃. {reason}",
                DeprecationWarning,
                stacklevel=2
            )
            return func(*args, **kwargs)
        return wrapper
    return decorator


class Throttle:
    """限流器"""
    
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = deque()
        self.lock = threading.Lock()
    
    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            with self.lock:
                now = time.time()
                
                # 移除时间窗口外的调用记录
                while self.calls and self.calls[0] <= now - self.time_window:
                    self.calls.popleft()
                
                # 检查是否超过限制
                if len(self.calls) >= self.max_calls:
                    raise Exception(f"调用频率超限: {self.max_calls} 次/{self.time_window} 秒")
                
                # 记录本次调用
                self.calls.append(now)
                
                return func(*args, **kwargs)
        
        return wrapper


if __name__ == '__main__':
    # 测试代码
    print("=== 辅助工具模块测试 ===")
    
    # 测试数据验证
    print("\n1. 数据验证测试:")
    validator = DataValidator()
    print(f"是否为数字 '123.45': {validator.is_number('123.45')}")
    print(f"是否为有效价格 '100.50': {validator.is_valid_price('100.50')}")
    print(f"是否为有效股票代码 'AAPL': {validator.is_valid_symbol('AAPL')}")
    
    # 测试数据清洗
    print("\n2. 数据清洗测试:")
    cleaner = DataCleaner()
    print(f"清洗数值 '1,234.56': {cleaner.clean_numeric('1,234.56')}")
    print(f"清洗百分比 '12.5%': {cleaner.clean_percentage('12.5%')}")
    print(f"清洗股票代码 ' aapl ': '{cleaner.clean_symbol(' aapl ')}'")
    
    # 测试时间处理
    print("\n3. 时间处理测试:")
    time_helper = TimeHelper()
    now = time_helper.now()
    print(f"当前时间: {time_helper.format_date(now)}")
    print(f"是否为交易时间: {time_helper.is_trading_time()}")
    print(f"下一个交易日: {time_helper.format_date(time_helper.get_next_trading_day())}")
    
    # 测试数学计算
    print("\n4. 数学计算测试:")
    math_helper = MathHelper()
    print(f"百分比变化 (100 -> 110): {math_helper.percentage_change(100, 110):.2f}%")
    print(f"安全除法 (10 / 0): {math_helper.safe_divide(10, 0, -1)}")
    
    values = [1, 2, 3, 4, 5]
    print(f"移动平均 {values} (窗口=3): {math_helper.moving_average(values, 3)}")
    print(f"标准差 {values}: {math_helper.standard_deviation(values):.4f}")
    
    # 测试缓存
    print("\n5. 缓存测试:")
    cache = CacheManager(max_size=3, ttl=2)
    cache.set('key1', 'value1')
    cache.set('key2', 'value2')
    print(f"缓存大小: {cache.size()}")
    print(f"获取 key1: {cache.get('key1')}")
    
    # 测试性能监控
    print("\n6. 性能监控测试:")
    monitor = PerformanceMonitor()
    
    with monitor.measure('test_operation'):
        time.sleep(0.1)
    
    stats = monitor.get_stats('test_operation')
    print(f"测试操作统计: {stats}")
    
    # 测试装饰器
    print("\n7. 装饰器测试:")
    
    @measure_time('decorated_function')
    @cached(ttl=60)
    def slow_function(x):
        time.sleep(0.05)
        return x * 2
    
    result1 = slow_function(5)
    result2 = slow_function(5)  # 应该从缓存获取
    print(f"函数结果: {result1}, {result2}")
    
    decorated_stats = _global_monitor.get_stats('decorated_function')
    print(f"装饰函数统计: {decorated_stats}")
    
    # 测试字符串处理
    print("\n8. 字符串处理测试:")
    string_helper = StringHelper()
    print(f"随机字符串: {string_helper.generate_random_string(8)}")
    print(f"驼峰转下划线 'CamelCase': {string_helper.camel_to_snake('CamelCase')}")
    print(f"下划线转驼峰 'snake_case': {string_helper.snake_to_camel('snake_case')}")
    
    # 测试限流器
    print("\n9. 限流器测试:")
    
    @Throttle(max_calls=2, time_window=1.0)
    def limited_function():
        return "执行成功"
    
    try:
        print(limited_function())  # 第1次调用
        print(limited_function())  # 第2次调用
        print(limited_function())  # 第3次调用，应该失败
    except Exception as e:
        print(f"限流器生效: {e}")
    
    print("\n测试完成!")