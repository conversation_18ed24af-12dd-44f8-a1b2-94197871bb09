#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务调度器模块

本模块提供任务调度功能，支持定时任务、周期性任务和事件驱动任务的管理和执行。

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

import asyncio
import threading
import time
from datetime import datetime, timedelta, time as dt_time
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import defaultdict, deque
import json
from concurrent.futures import ThreadPoolExecutor, Future
import signal
import sys

# 尝试导入可选依赖
try:
    import schedule
    SCHEDULE_AVAILABLE = True
except ImportError:
    SCHEDULE_AVAILABLE = False


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"        # 等待执行
    RUNNING = "running"        # 正在执行
    COMPLETED = "completed"    # 执行完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"    # 已取消
    PAUSED = "paused"          # 已暂停


class TaskType(Enum):
    """任务类型枚举"""
    ONCE = "once"              # 一次性任务
    INTERVAL = "interval"      # 间隔任务
    CRON = "cron"              # Cron表达式任务
    DAILY = "daily"            # 每日任务
    WEEKLY = "weekly"          # 每周任务
    MONTHLY = "monthly"        # 每月任务
    MARKET_HOURS = "market_hours"  # 交易时间任务
    EVENT_DRIVEN = "event_driven"  # 事件驱动任务


class Priority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    result: Any = None
    error: Optional[Exception] = None
    execution_time: float = 0.0
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if self.end_time and self.start_time:
            self.execution_time = (self.end_time - self.start_time).total_seconds()


@dataclass
class Task:
    """任务定义"""
    task_id: str
    name: str
    func: Callable
    task_type: TaskType
    priority: Priority = Priority.NORMAL
    args: tuple = field(default_factory=tuple)
    kwargs: Dict[str, Any] = field(default_factory=dict)
    
    # 调度相关
    schedule_time: Optional[datetime] = None
    interval: Optional[timedelta] = None
    cron_expression: Optional[str] = None
    
    # 执行控制
    max_retries: int = 3
    retry_delay: timedelta = timedelta(seconds=30)
    timeout: Optional[timedelta] = None
    
    # 状态信息
    status: TaskStatus = TaskStatus.PENDING
    created_time: datetime = field(default_factory=datetime.now)
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    
    # 执行历史
    execution_history: List[TaskResult] = field(default_factory=list)
    
    # 依赖关系
    dependencies: List[str] = field(default_factory=list)
    
    # 其他配置
    enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """后处理初始化"""
        if not self.task_id:
            self.task_id = f"{self.name}_{int(time.time())}"
        
        # 计算下次执行时间
        self._calculate_next_run()
    
    def _calculate_next_run(self):
        """计算下次执行时间"""
        now = datetime.now()
        
        if self.task_type == TaskType.ONCE:
            self.next_run = self.schedule_time or now
        elif self.task_type == TaskType.INTERVAL:
            if self.interval:
                self.next_run = (self.last_run or now) + self.interval
            else:
                self.next_run = now
        elif self.task_type == TaskType.DAILY:
            # 每日任务，默认在指定时间执行
            if self.schedule_time:
                next_run = now.replace(
                    hour=self.schedule_time.hour,
                    minute=self.schedule_time.minute,
                    second=self.schedule_time.second,
                    microsecond=0
                )
                if next_run <= now:
                    next_run += timedelta(days=1)
                self.next_run = next_run
            else:
                self.next_run = now + timedelta(days=1)
        elif self.task_type == TaskType.WEEKLY:
            # 每周任务
            if self.schedule_time:
                days_ahead = self.schedule_time.weekday() - now.weekday()
                if days_ahead <= 0:  # 目标日期已过或是今天
                    days_ahead += 7
                self.next_run = now + timedelta(days=days_ahead)
            else:
                self.next_run = now + timedelta(weeks=1)
        elif self.task_type == TaskType.MARKET_HOURS:
            # 交易时间任务
            self.next_run = self._calculate_next_market_time()
        else:
            self.next_run = now
    
    def _calculate_next_market_time(self) -> datetime:
        """计算下次交易时间"""
        now = datetime.now()
        
        # A股交易时间：9:30-11:30, 13:00-15:00
        morning_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        morning_end = now.replace(hour=11, minute=30, second=0, microsecond=0)
        afternoon_start = now.replace(hour=13, minute=0, second=0, microsecond=0)
        afternoon_end = now.replace(hour=15, minute=0, second=0, microsecond=0)
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周末
            # 下周一早上开盘
            days_until_monday = 7 - now.weekday()
            next_monday = now + timedelta(days=days_until_monday)
            return next_monday.replace(hour=9, minute=30, second=0, microsecond=0)
        
        # 工作日
        if now < morning_start:
            return morning_start
        elif morning_start <= now < morning_end:
            return now + timedelta(minutes=1)  # 交易时间内，1分钟后执行
        elif morning_end <= now < afternoon_start:
            return afternoon_start
        elif afternoon_start <= now < afternoon_end:
            return now + timedelta(minutes=1)  # 交易时间内，1分钟后执行
        else:
            # 收盘后，下一个交易日开盘
            next_day = now + timedelta(days=1)
            return next_day.replace(hour=9, minute=30, second=0, microsecond=0)
    
    def should_run(self) -> bool:
        """判断是否应该执行"""
        if not self.enabled or self.status in [TaskStatus.RUNNING, TaskStatus.CANCELLED]:
            return False
        
        if not self.next_run:
            return False
        
        return datetime.now() >= self.next_run
    
    def update_next_run(self):
        """更新下次执行时间"""
        self.last_run = datetime.now()
        if self.task_type != TaskType.ONCE:
            self._calculate_next_run()
        else:
            self.next_run = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'task_type': self.task_type.value,
            'priority': self.priority.value,
            'status': self.status.value,
            'enabled': self.enabled,
            'created_time': self.created_time.isoformat(),
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'max_retries': self.max_retries,
            'execution_count': len(self.execution_history),
            'success_count': len([r for r in self.execution_history if r.status == TaskStatus.COMPLETED]),
            'metadata': self.metadata
        }


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, max_workers: int = 4, config: Optional[Dict[str, Any]] = None):
        self.max_workers = max_workers
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 任务管理
        self.tasks: Dict[str, Task] = {}
        self.running_tasks: Dict[str, Future] = {}
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 调度控制
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0,
            'total_execution_time': 0.0,
            'avg_execution_time': 0.0
        }
        
        # 事件回调
        self.event_handlers: Dict[str, List[Callable]] = defaultdict(list)
        
        # 任务队列（按优先级排序）
        self.task_queue: deque = deque()
        
        # 注册信号处理器
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，正在停止调度器...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def add_task(self, task: Task) -> bool:
        """添加任务"""
        try:
            if task.task_id in self.tasks:
                self.logger.warning(f"任务已存在: {task.task_id}")
                return False
            
            # 检查依赖关系
            for dep_id in task.dependencies:
                if dep_id not in self.tasks:
                    self.logger.error(f"依赖任务不存在: {dep_id}")
                    return False
            
            self.tasks[task.task_id] = task
            self.stats['total_tasks'] += 1
            
            self.logger.info(f"添加任务: {task.name} ({task.task_id})")
            self._trigger_event('task_added', task)
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
            return False
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        try:
            if task_id not in self.tasks:
                self.logger.warning(f"任务不存在: {task_id}")
                return False
            
            task = self.tasks[task_id]
            
            # 如果任务正在运行，先取消
            if task_id in self.running_tasks:
                self.cancel_task(task_id)
            
            del self.tasks[task_id]
            
            self.logger.info(f"移除任务: {task.name} ({task_id})")
            self._trigger_event('task_removed', task)
            
            return True
            
        except Exception as e:
            self.logger.error(f"移除任务失败: {e}")
            return False
    
    def start(self):
        """启动调度器"""
        if self.running:
            self.logger.warning("调度器已在运行")
            return
        
        self.running = True
        self.stop_event.clear()
        
        # 启动调度线程
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("任务调度器已启动")
        self._trigger_event('scheduler_started', None)
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.running = False
        self.stop_event.set()
        
        # 等待调度线程结束
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # 取消所有运行中的任务
        for task_id in list(self.running_tasks.keys()):
            self.cancel_task(task_id)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("任务调度器已停止")
        self._trigger_event('scheduler_stopped', None)
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.running and not self.stop_event.is_set():
            try:
                # 检查待执行任务
                self._check_pending_tasks()
                
                # 检查运行中任务
                self._check_running_tasks()
                
                # 清理完成的任务
                self._cleanup_completed_tasks()
                
                # 短暂休眠
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"调度器循环出错: {e}")
                time.sleep(5)
    
    def _check_pending_tasks(self):
        """检查待执行任务"""
        ready_tasks = []
        
        for task in self.tasks.values():
            if task.should_run() and self._check_dependencies(task):
                ready_tasks.append(task)
        
        # 按优先级排序
        ready_tasks.sort(key=lambda t: (-t.priority.value, t.next_run))
        
        # 执行任务（受线程池限制）
        for task in ready_tasks:
            if len(self.running_tasks) >= self.max_workers:
                break
            
            if task.task_id not in self.running_tasks:
                self._execute_task(task)
    
    def _check_dependencies(self, task: Task) -> bool:
        """检查任务依赖"""
        for dep_id in task.dependencies:
            if dep_id in self.tasks:
                dep_task = self.tasks[dep_id]
                if dep_task.status not in [TaskStatus.COMPLETED]:
                    return False
        return True
    
    def _execute_task(self, task: Task):
        """执行任务"""
        try:
            task.status = TaskStatus.RUNNING
            
            # 创建任务结果
            result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.RUNNING,
                start_time=datetime.now()
            )
            
            # 提交到线程池
            future = self.executor.submit(self._run_task_with_timeout, task, result)
            self.running_tasks[task.task_id] = future
            
            self.logger.info(f"开始执行任务: {task.name} ({task.task_id})")
            self._trigger_event('task_started', task)
            
        except Exception as e:
            self.logger.error(f"执行任务失败: {e}")
            task.status = TaskStatus.FAILED
    
    def _run_task_with_timeout(self, task: Task, result: TaskResult) -> TaskResult:
        """带超时的任务执行"""
        try:
            # 执行任务函数
            if task.timeout:
                # 使用超时执行
                import concurrent.futures
                with ThreadPoolExecutor(max_workers=1) as timeout_executor:
                    timeout_future = timeout_executor.submit(task.func, *task.args, **task.kwargs)
                    try:
                        task_result = timeout_future.result(timeout=task.timeout.total_seconds())
                        result.result = task_result
                        result.status = TaskStatus.COMPLETED
                    except concurrent.futures.TimeoutError:
                        timeout_future.cancel()
                        raise TimeoutError(f"任务执行超时: {task.timeout}")
            else:
                # 直接执行
                task_result = task.func(*task.args, **task.kwargs)
                result.result = task_result
                result.status = TaskStatus.COMPLETED
            
            result.end_time = datetime.now()
            task.status = TaskStatus.COMPLETED
            
            self.logger.info(f"任务执行成功: {task.name} ({task.task_id})")
            self._trigger_event('task_completed', task)
            
        except Exception as e:
            result.error = e
            result.status = TaskStatus.FAILED
            result.end_time = datetime.now()
            task.status = TaskStatus.FAILED
            
            self.logger.error(f"任务执行失败: {task.name} ({task.task_id}) - {e}")
            self._trigger_event('task_failed', task)
            
            # 重试逻辑
            if result.retry_count < task.max_retries:
                result.retry_count += 1
                task.status = TaskStatus.PENDING
                task.next_run = datetime.now() + task.retry_delay
                self.logger.info(f"任务将重试: {task.name}, 重试次数: {result.retry_count}")
        
        finally:
            # 记录执行历史
            task.execution_history.append(result)
            
            # 限制历史记录长度
            if len(task.execution_history) > 100:
                task.execution_history = task.execution_history[-50:]
            
            # 更新下次执行时间
            if task.status == TaskStatus.COMPLETED:
                task.update_next_run()
            
            return result
    
    def _check_running_tasks(self):
        """检查运行中的任务"""
        completed_tasks = []
        
        for task_id, future in self.running_tasks.items():
            if future.done():
                completed_tasks.append(task_id)
                
                try:
                    result = future.result()
                    self._update_stats(result)
                except Exception as e:
                    self.logger.error(f"获取任务结果失败: {task_id} - {e}")
        
        # 移除已完成的任务
        for task_id in completed_tasks:
            del self.running_tasks[task_id]
    
    def _cleanup_completed_tasks(self):
        """清理已完成的一次性任务"""
        to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task.task_type == TaskType.ONCE and 
                task.status == TaskStatus.COMPLETED and 
                task_id not in self.running_tasks):
                to_remove.append(task_id)
        
        for task_id in to_remove:
            self.remove_task(task_id)
    
    def _update_stats(self, result: TaskResult):
        """更新统计信息"""
        if result.status == TaskStatus.COMPLETED:
            self.stats['completed_tasks'] += 1
        elif result.status == TaskStatus.FAILED:
            self.stats['failed_tasks'] += 1
        elif result.status == TaskStatus.CANCELLED:
            self.stats['cancelled_tasks'] += 1
        
        if result.execution_time > 0:
            self.stats['total_execution_time'] += result.execution_time
            completed = self.stats['completed_tasks']
            if completed > 0:
                self.stats['avg_execution_time'] = self.stats['total_execution_time'] / completed
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            if task_id in self.running_tasks:
                future = self.running_tasks[task_id]
                future.cancel()
                del self.running_tasks[task_id]
            
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.status = TaskStatus.CANCELLED
                self.stats['cancelled_tasks'] += 1
                
                self.logger.info(f"任务已取消: {task.name} ({task_id})")
                self._trigger_event('task_cancelled', task)
            
            return True
            
        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return False
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status not in [TaskStatus.RUNNING, TaskStatus.CANCELLED]:
                task.status = TaskStatus.PAUSED
                task.enabled = False
                self.logger.info(f"任务已暂停: {task.name} ({task_id})")
                return True
        return False
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == TaskStatus.PAUSED:
                task.status = TaskStatus.PENDING
                task.enabled = True
                task._calculate_next_run()
                self.logger.info(f"任务已恢复: {task.name} ({task_id})")
                return True
        return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            status = task.to_dict()
            
            # 添加运行时信息
            status['is_running'] = task_id in self.running_tasks
            
            # 最近执行结果
            if task.execution_history:
                latest_result = task.execution_history[-1]
                status['latest_result'] = {
                    'status': latest_result.status.value,
                    'execution_time': latest_result.execution_time,
                    'error': str(latest_result.error) if latest_result.error else None
                }
            
            return status
        
        return None
    
    def list_tasks(self, status_filter: Optional[TaskStatus] = None) -> List[Dict[str, Any]]:
        """列出任务"""
        tasks = []
        
        for task in self.tasks.values():
            if status_filter is None or task.status == status_filter:
                tasks.append(task.to_dict())
        
        return sorted(tasks, key=lambda t: t['priority'], reverse=True)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats.update({
            'active_tasks': len(self.tasks),
            'running_tasks': len(self.running_tasks),
            'scheduler_running': self.running,
            'max_workers': self.max_workers,
            'uptime': time.time() - getattr(self, '_start_time', time.time())
        })
        
        return stats
    
    def add_event_handler(self, event_type: str, handler: Callable):
        """添加事件处理器"""
        self.event_handlers[event_type].append(handler)
    
    def remove_event_handler(self, event_type: str, handler: Callable):
        """移除事件处理器"""
        if event_type in self.event_handlers:
            try:
                self.event_handlers[event_type].remove(handler)
            except ValueError:
                pass
    
    def _trigger_event(self, event_type: str, task: Optional[Task]):
        """触发事件"""
        for handler in self.event_handlers.get(event_type, []):
            try:
                handler(task)
            except Exception as e:
                self.logger.error(f"事件处理器执行失败: {event_type} - {e}")
    
    # 便捷方法
    def schedule_once(self, name: str, func: Callable, 
                     schedule_time: Optional[datetime] = None,
                     args: tuple = (), kwargs: Dict[str, Any] = None,
                     priority: Priority = Priority.NORMAL) -> str:
        """调度一次性任务"""
        task = Task(
            task_id=f"{name}_{int(time.time())}",
            name=name,
            func=func,
            task_type=TaskType.ONCE,
            priority=priority,
            args=args,
            kwargs=kwargs or {},
            schedule_time=schedule_time or datetime.now()
        )
        
        if self.add_task(task):
            return task.task_id
        return ""
    
    def schedule_interval(self, name: str, func: Callable, 
                         interval: timedelta,
                         args: tuple = (), kwargs: Dict[str, Any] = None,
                         priority: Priority = Priority.NORMAL) -> str:
        """调度间隔任务"""
        task = Task(
            task_id=f"{name}_{int(time.time())}",
            name=name,
            func=func,
            task_type=TaskType.INTERVAL,
            priority=priority,
            args=args,
            kwargs=kwargs or {},
            interval=interval
        )
        
        if self.add_task(task):
            return task.task_id
        return ""
    
    def schedule_daily(self, name: str, func: Callable, 
                      time_of_day: dt_time,
                      args: tuple = (), kwargs: Dict[str, Any] = None,
                      priority: Priority = Priority.NORMAL) -> str:
        """调度每日任务"""
        schedule_time = datetime.combine(datetime.now().date(), time_of_day)
        
        task = Task(
            task_id=f"{name}_{int(time.time())}",
            name=name,
            func=func,
            task_type=TaskType.DAILY,
            priority=priority,
            args=args,
            kwargs=kwargs or {},
            schedule_time=schedule_time
        )
        
        if self.add_task(task):
            return task.task_id
        return ""
    
    def schedule_market_hours(self, name: str, func: Callable,
                             args: tuple = (), kwargs: Dict[str, Any] = None,
                             priority: Priority = Priority.NORMAL) -> str:
        """调度交易时间任务"""
        task = Task(
            task_id=f"{name}_{int(time.time())}",
            name=name,
            func=func,
            task_type=TaskType.MARKET_HOURS,
            priority=priority,
            args=args,
            kwargs=kwargs or {}
        )
        
        if self.add_task(task):
            return task.task_id
        return ""


# 全局调度器实例
_global_scheduler: Optional[TaskScheduler] = None


def get_scheduler() -> TaskScheduler:
    """获取全局调度器实例"""
    global _global_scheduler
    if _global_scheduler is None:
        _global_scheduler = TaskScheduler()
    return _global_scheduler


def start_scheduler():
    """启动全局调度器"""
    scheduler = get_scheduler()
    if not scheduler.running:
        scheduler.start()


def stop_scheduler():
    """停止全局调度器"""
    global _global_scheduler
    if _global_scheduler and _global_scheduler.running:
        _global_scheduler.stop()
        _global_scheduler = None


if __name__ == '__main__':
    # 测试代码
    import random
    
    def test_task(name: str, duration: float = 1.0):
        """测试任务函数"""
        print(f"执行任务: {name}")
        time.sleep(duration)
        result = random.randint(1, 100)
        print(f"任务 {name} 完成，结果: {result}")
        return result
    
    def failing_task():
        """会失败的任务"""
        print("执行会失败的任务")
        raise Exception("任务故意失败")
    
    # 创建调度器
    scheduler = TaskScheduler(max_workers=2)
    
    # 添加事件处理器
    def on_task_completed(task):
        print(f"事件: 任务完成 - {task.name}")
    
    scheduler.add_event_handler('task_completed', on_task_completed)
    
    print("=== 任务调度器测试 ===")
    
    # 添加各种类型的任务
    
    # 一次性任务
    task1_id = scheduler.schedule_once(
        "一次性任务", 
        test_task, 
        args=("Task1", 2.0),
        priority=Priority.HIGH
    )
    
    # 间隔任务
    task2_id = scheduler.schedule_interval(
        "间隔任务",
        test_task,
        interval=timedelta(seconds=5),
        args=("Task2", 1.0)
    )
    
    # 每日任务
    task3_id = scheduler.schedule_daily(
        "每日任务",
        test_task,
        time_of_day=dt_time(hour=datetime.now().hour, minute=datetime.now().minute + 1),
        args=("Task3", 0.5)
    )
    
    # 会失败的任务（测试重试）
    failing_task_obj = Task(
        task_id="failing_task",
        name="失败任务",
        func=failing_task,
        task_type=TaskType.ONCE,
        max_retries=2,
        retry_delay=timedelta(seconds=3)
    )
    scheduler.add_task(failing_task_obj)
    
    # 启动调度器
    scheduler.start()
    
    try:
        # 运行一段时间
        print("调度器运行中...")
        
        for i in range(30):
            time.sleep(1)
            
            # 每5秒显示一次状态
            if i % 5 == 0:
                print(f"\n=== 第 {i} 秒状态 ===")
                stats = scheduler.get_statistics()
                print(f"统计信息: {stats}")
                
                # 显示任务状态
                tasks = scheduler.list_tasks()
                for task_info in tasks:
                    print(f"任务: {task_info['name']} - {task_info['status']} - 下次执行: {task_info['next_run']}")
        
        # 测试任务控制
        print("\n=== 测试任务控制 ===")
        
        # 暂停间隔任务
        scheduler.pause_task(task2_id)
        print(f"暂停任务: {task2_id}")
        
        time.sleep(3)
        
        # 恢复间隔任务
        scheduler.resume_task(task2_id)
        print(f"恢复任务: {task2_id}")
        
        time.sleep(5)
        
        # 取消任务
        scheduler.cancel_task(task2_id)
        print(f"取消任务: {task2_id}")
        
        time.sleep(2)
        
    except KeyboardInterrupt:
        print("\n接收到中断信号")
    
    finally:
        # 停止调度器
        print("\n停止调度器...")
        scheduler.stop()
        
        # 最终统计
        print("\n=== 最终统计 ===")
        final_stats = scheduler.get_statistics()
        for key, value in final_stats.items():
            print(f"{key}: {value}")
        
        print("测试完成")