#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gamma Shock 工具模块

主要功能：
- 日志系统管理
- 通用工具函数
- 数据处理工具
- 时间处理工具
- 配置管理工具

作者: AI Assistant
创建时间: 2024-12-11
版本: 1.0.0
"""

from .logger import (
    setup_logging,
    get_logger,
    log_performance,
    log_async_performance,
    log_function_call,
    get_error_stats,
    reset_error_stats,
    with_log_context,
    GammaShockLoggerAdapter,
    ColoredFormatter,
    JSONFormatter,
    PerformanceFilter,
    ErrorTracker,
    # 预定义的日志记录器
    data_logger,
    indicator_logger,
    signal_logger,
    ai_logger,
    monitor_logger,
    notification_logger
)
from .scheduler import (
    TaskScheduler, Task, TaskStatus, TaskType, Priority, TaskResult,
    get_scheduler, start_scheduler, stop_scheduler
)
from .helpers import (
    DataValidator, DataCleaner, TimeHelper, MathHelper, FileHelper,
    <PERSON>ache<PERSON>anager, String<PERSON>elper, PerformanceMonitor, ConfigManager,
    cached, measure_time, retry, singleton, deprecated, Throttle
)

__all__ = [
    # 日志系统
    'setup_logging',
    'get_logger',
    'log_performance',
    'log_async_performance', 
    'log_function_call',
    'get_error_stats',
    'reset_error_stats',
    'with_log_context',
    'GammaShockLoggerAdapter',
    'ColoredFormatter',
    'JSONFormatter',
    'PerformanceFilter',
    'ErrorTracker',
    # 预定义日志记录器
    'data_logger',
    'indicator_logger',
    'signal_logger',
    'ai_logger',
    'monitor_logger',
    'notification_logger',
    
    # 任务调度
    'TaskScheduler',
    'Task',
    'TaskStatus',
    'TaskType', 
    'Priority',
    'TaskResult',
    'get_scheduler',
    'start_scheduler',
    'stop_scheduler',
    
    # 辅助工具类
    'DataValidator',
    'DataCleaner',
    'TimeHelper',
    'MathHelper',
    'FileHelper',
    'CacheManager',
    'StringHelper',
    'PerformanceMonitor',
    'ConfigManager',
    
    # 装饰器和工具函数
    'cached',
    'measure_time',
    'retry',
    'singleton',
    'deprecated',
    'Throttle'
]

__version__ = '1.0.0'
__author__ = 'AI Assistant'
__description__ = 'Gamma Shock 工具模块 - 提供日志、数据处理、时间管理等通用功能'