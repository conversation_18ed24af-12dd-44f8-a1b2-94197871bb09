#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块

主要功能：
- 统一日志配置
- 多种日志输出格式
- 日志轮转管理
- 性能监控日志
- 错误追踪

作者: AI Assistant
创建时间: 2024-12-11
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import json
import traceback
from functools import wraps
import time


class ColoredFormatter(logging.Formatter):
    """
    彩色日志格式化器
    """
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
    }
    RESET = '\033[0m'
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.RESET}"
        
        return super().format(record)


class JSONFormatter(logging.Formatter):
    """
    JSON格式日志格式化器
    """
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_data'):
            log_entry['extra'] = record.extra_data
        
        return json.dumps(log_entry, ensure_ascii=False)


class PerformanceFilter(logging.Filter):
    """
    性能监控过滤器
    """
    
    def filter(self, record):
        # 只记录性能相关的日志
        return hasattr(record, 'performance') or 'performance' in record.getMessage().lower()


class ErrorTracker:
    """
    错误追踪器
    """
    
    def __init__(self):
        self.error_counts = {}
        self.last_errors = []
        self.max_last_errors = 100
    
    def track_error(self, error_type: str, error_message: str, 
                   module: str = None, function: str = None):
        """
        追踪错误
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
            module: 模块名
            function: 函数名
        """
        # 统计错误次数
        key = f"{error_type}:{module}:{function}" if module and function else error_type
        self.error_counts[key] = self.error_counts.get(key, 0) + 1
        
        # 记录最近错误
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'type': error_type,
            'message': error_message,
            'module': module,
            'function': function,
            'count': self.error_counts[key]
        }
        
        self.last_errors.append(error_info)
        
        # 保持最近错误数量限制
        if len(self.last_errors) > self.max_last_errors:
            self.last_errors.pop(0)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """
        获取错误统计
        
        Returns:
            错误统计信息
        """
        return {
            'total_error_types': len(self.error_counts),
            'total_errors': sum(self.error_counts.values()),
            'error_counts': self.error_counts.copy(),
            'recent_errors': self.last_errors[-10:],  # 最近10个错误
            'most_frequent_errors': sorted(
                self.error_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]  # 最频繁的5个错误
        }


# 全局错误追踪器
error_tracker = ErrorTracker()


class GammaShockLoggerAdapter(logging.LoggerAdapter):
    """
    Gamma Shock 日志适配器
    """
    
    def process(self, msg, kwargs):
        # 添加上下文信息
        if 'extra' not in kwargs:
            kwargs['extra'] = {}
        
        # 添加模块信息
        if self.extra:
            kwargs['extra'].update(self.extra)
        
        return msg, kwargs
    
    def error_with_tracking(self, msg, *args, exc_info=None, **kwargs):
        """
        记录错误并追踪
        
        Args:
            msg: 错误消息
            exc_info: 异常信息
        """
        # 记录错误日志
        self.error(msg, *args, exc_info=exc_info, **kwargs)
        
        # 追踪错误
        try:
            error_type = "Unknown"
            if exc_info and exc_info[0]:
                error_type = exc_info[0].__name__
            
            module = self.extra.get('module') if self.extra else None
            function = self.extra.get('function') if self.extra else None
            
            error_tracker.track_error(
                error_type=error_type,
                error_message=str(msg),
                module=module,
                function=function
            )
        except Exception:
            # 避免日志记录本身出错
            pass


def setup_logging(config: Dict[str, Any]) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        config: 日志配置
        
    Returns:
        根日志记录器
    """
    # 获取配置参数
    level = config.get('level', 'INFO')
    format_type = config.get('format', 'standard')
    log_file = config.get('file')
    max_file_size = config.get('max_file_size', 10 * 1024 * 1024)  # 10MB
    backup_count = config.get('backup_count', 5)
    console_output = config.get('console', True)
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 设置日志格式
    if format_type == 'json':
        formatter = JSONFormatter()
    elif format_type == 'detailed':
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)-20s | %(funcName)-15s:%(lineno)-4d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    else:  # standard
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # 控制台输出
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        
        # 如果是标准格式且支持颜色，使用彩色格式化器
        if format_type == 'standard' and hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            console_formatter = ColoredFormatter(
                '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            console_handler.setFormatter(console_formatter)
        else:
            console_handler.setFormatter(formatter)
        
        root_logger.addHandler(console_handler)
    
    # 文件输出
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置特定模块的日志级别
    module_levels = config.get('module_levels', {})
    for module_name, module_level in module_levels.items():
        module_logger = logging.getLogger(module_name)
        module_logger.setLevel(getattr(logging, module_level.upper()))
    
    # 添加性能日志处理器（如果配置了）
    if config.get('performance_log'):
        perf_handler = logging.handlers.RotatingFileHandler(
            config['performance_log'],
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        perf_handler.addFilter(PerformanceFilter())
        perf_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(perf_handler)
    
    # 记录日志系统启动信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已初始化 - 级别: {level}, 格式: {format_type}")
    if log_file:
        logger.info(f"日志文件: {log_file}")
    
    return root_logger


def get_logger(name: str, **extra) -> GammaShockLoggerAdapter:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        **extra: 额外的上下文信息
        
    Returns:
        日志记录器适配器
    """
    logger = logging.getLogger(name)
    return GammaShockLoggerAdapter(logger, extra)


def log_performance(func):
    """
    性能监控装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__, function=func.__name__)
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            logger.info(
                f"Performance: {func.__name__} executed in {execution_time:.4f}s",
                extra={'performance': True, 'execution_time': execution_time}
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error_with_tracking(
                f"Performance: {func.__name__} failed after {execution_time:.4f}s: {e}",
                exc_info=True
            )
            raise
    
    return wrapper


async def log_async_performance(func):
    """
    异步性能监控装饰器
    
    Args:
        func: 被装饰的异步函数
        
    Returns:
        装饰后的异步函数
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__, function=func.__name__)
        
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            logger.info(
                f"Async Performance: {func.__name__} executed in {execution_time:.4f}s",
                extra={'performance': True, 'execution_time': execution_time}
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error_with_tracking(
                f"Async Performance: {func.__name__} failed after {execution_time:.4f}s: {e}",
                exc_info=True
            )
            raise
    
    return wrapper


def log_function_call(include_args=False, include_result=False):
    """
    函数调用日志装饰器
    
    Args:
        include_args: 是否包含参数
        include_result: 是否包含返回值
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(func.__module__, function=func.__name__)
            
            # 记录函数调用
            call_info = f"Calling {func.__name__}"
            if include_args:
                call_info += f" with args={args}, kwargs={kwargs}"
            
            logger.debug(call_info)
            
            try:
                result = func(*args, **kwargs)
                
                # 记录返回值
                if include_result:
                    logger.debug(f"{func.__name__} returned: {result}")
                else:
                    logger.debug(f"{func.__name__} completed successfully")
                
                return result
                
            except Exception as e:
                logger.error_with_tracking(
                    f"{func.__name__} raised {type(e).__name__}: {e}",
                    exc_info=True
                )
                raise
        
        return wrapper
    return decorator


def get_error_stats() -> Dict[str, Any]:
    """
    获取错误统计信息
    
    Returns:
        错误统计信息
    """
    return error_tracker.get_error_stats()


def reset_error_stats():
    """
    重置错误统计
    """
    global error_tracker
    error_tracker = ErrorTracker()


class LogContext:
    """
    日志上下文管理器
    """
    
    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.original_extra = getattr(logger, 'extra', {})
    
    def __enter__(self):
        # 添加上下文信息
        if hasattr(self.logger, 'extra'):
            self.logger.extra.update(self.context)
        else:
            self.logger.extra = self.context
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始上下文
        if hasattr(self.logger, 'extra'):
            self.logger.extra = self.original_extra


def with_log_context(logger: logging.Logger, **context):
    """
    创建日志上下文
    
    Args:
        logger: 日志记录器
        **context: 上下文信息
        
    Returns:
        日志上下文管理器
    """
    return LogContext(logger, **context)


# 预定义的日志记录器
data_logger = get_logger('gamma_shock.data', module='data')
indicator_logger = get_logger('gamma_shock.indicators', module='indicators')
signal_logger = get_logger('gamma_shock.signals', module='signals')
ai_logger = get_logger('gamma_shock.ai', module='ai')
monitor_logger = get_logger('gamma_shock.monitor', module='monitor')
notification_logger = get_logger('gamma_shock.notifications', module='notifications')


if __name__ == "__main__":
    # 测试日志系统
    test_config = {
        'level': 'DEBUG',
        'format': 'detailed',
        'console': True,
        'file': 'test_logs/gamma_shock.log'
    }
    
    setup_logging(test_config)
    
    # 测试各种日志级别
    logger = get_logger(__name__)
    
    logger.debug("这是调试信息")
    logger.info("这是普通信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    
    # 测试性能装饰器
    @log_performance
    def test_function():
        time.sleep(0.1)
        return "测试完成"
    
    result = test_function()
    
    # 测试错误追踪
    try:
        raise ValueError("测试错误")
    except Exception as e:
        logger.error_with_tracking("捕获到测试错误", exc_info=True)
    
    # 显示错误统计
    print("\n错误统计:")
    print(json.dumps(get_error_stats(), indent=2, ensure_ascii=False))