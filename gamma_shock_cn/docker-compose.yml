# Gamma Shock CN - Docker Compose配置
# 用于本地开发和生产环境部署

version: '3.8'

services:
  # 主应用服务
  gamma-shock:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gamma-shock-app
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=********************************************/gamma_shock
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./backups:/app/backups
      - ./config:/app/config
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - postgres
    networks:
      - gamma-network
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: gamma-shock-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - gamma-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: gamma-shock-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=gamma_shock
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres123}
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deploy/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - gamma-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: gamma-shock-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx.conf:/etc/nginx/nginx.conf
      - ./deploy/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - gamma-shock
    networks:
      - gamma-network
    profiles:
      - production

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: gamma-shock-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./deploy/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - gamma-network
    profiles:
      - monitoring

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: gamma-shock-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deploy/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - gamma-network
    profiles:
      - monitoring

  # 日志收集服务 - ELK Stack (可选)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: gamma-shock-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - gamma-network
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: gamma-shock-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - gamma-network
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: gamma-shock-logstash
    restart: unless-stopped
    volumes:
      - ./deploy/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/app/logs:ro
    depends_on:
      - elasticsearch
    networks:
      - gamma-network
    profiles:
      - logging

# 网络配置
networks:
  gamma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local