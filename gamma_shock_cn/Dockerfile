# Gamma Shock CN - Docker配置文件
# 用于构建生产环境容器镜像

# 使用官方Python 3.11运行时作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    TZ=Asia/Shanghai

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    wget \
    git \
    vim \
    tzdata \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 创建非root用户
RUN useradd --create-home --shell /bin/bash gamma && \
    chown -R gamma:gamma /app

# 复制应用代码
COPY --chown=gamma:gamma . .

# 创建必要的目录
RUN mkdir -p /app/logs /app/data/cache /app/backups && \
    chown -R gamma:gamma /app/logs /app/data/cache /app/backups

# 切换到非root用户
USER gamma

# 暴露端口（如果有Web界面）
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 设置启动命令
CMD ["python", "run.py"]