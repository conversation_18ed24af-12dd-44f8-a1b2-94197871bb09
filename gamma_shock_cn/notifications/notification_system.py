#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知系统模块

主要功能：
- 邮件通知
- 微信通知（可扩展）
- 短信通知（可扩展）
- 通知模板管理
- 通知历史记录

作者: AI Assistant
创建时间: 2024-12-11
"""

import smtplib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from dataclasses import dataclass, asdict
import json
import os
from pathlib import Path
import asyncio
from abc import ABC, abstractmethod

# 导入项目模块
from strategy.signal_generator import TradingSignal, SignalLevel, SignalType
from config.settings import settings

logger = logging.getLogger(__name__)

@dataclass
class NotificationMessage:
    """通知消息数据结构"""
    id: str
    title: str
    content: str
    notification_type: str  # 'email', 'wechat', 'sms'
    priority: str  # 'low', 'medium', 'high', 'urgent'
    recipients: List[str]
    timestamp: datetime
    symbol: Optional[str] = None
    signal_data: Optional[Dict[str, Any]] = None
    attachments: Optional[List[str]] = None
    sent: bool = False
    sent_time: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

class NotificationProvider(ABC):
    """通知提供者抽象基类"""
    
    @abstractmethod
    async def send_notification(self, message: NotificationMessage) -> bool:
        """发送通知"""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """验证配置"""
        pass

class EmailNotificationProvider(NotificationProvider):
    """邮件通知提供者"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化邮件通知提供者
        
        Args:
            config: 邮件配置
        """
        self.config = config
        # 从配置字典获取配置信息
        self.smtp_server = config.get('smtp_server', 'smtp.qq.com')
        self.smtp_port = config.get('smtp_port', 587)
        self.username = config.get('sender_email', '')
        self.password = config.get('sender_password', '')
        self.from_email = config.get('sender_email', self.username)
        self.use_tls = config.get('use_tls', True)
        
        logger.info(f"邮件通知提供者初始化: {self.smtp_server}:{self.smtp_port}")
    
    def validate_config(self) -> bool:
        """
        验证邮件配置
        
        Returns:
            配置是否有效
        """
        # 检查必要的配置字段
        if not self.config.get('sender_email'):
            logger.error("邮件配置缺少必要字段: sender_email")
            return False
        if not self.config.get('sender_password'):
            logger.error("邮件配置缺少必要字段: sender_password")
            return False
        return True
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        """
        发送邮件通知
        
        Args:
            message: 通知消息
            
        Returns:
            是否发送成功
        """
        try:
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(message.recipients)
            msg['Subject'] = message.title
            
            # 添加邮件正文
            body = self._format_email_body(message)
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # 添加附件
            if message.attachments:
                for attachment_path in message.attachments:
                    if os.path.exists(attachment_path):
                        self._add_attachment(msg, attachment_path)
            
            # 发送邮件
            await self._send_email(msg)
            
            logger.info(f"邮件发送成功: {message.title} -> {message.recipients}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            message.error_message = str(e)
            return False
    
    def _format_email_body(self, message: NotificationMessage) -> str:
        """
        格式化邮件正文
        
        Args:
            message: 通知消息
            
        Returns:
            格式化后的HTML正文
        """
        # 基础HTML模板
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 15px; border-radius: 5px; }
                .content { margin: 20px 0; }
                .signal-info { background-color: #e8f4fd; padding: 10px; border-radius: 5px; margin: 10px 0; }
                .high-priority { border-left: 5px solid #ff4444; }
                .medium-priority { border-left: 5px solid #ffaa00; }
                .low-priority { border-left: 5px solid #00aa00; }
                .footer { font-size: 12px; color: #666; margin-top: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚀 Gamma Shock 交易信号通知</h2>
                <p><strong>时间:</strong> {timestamp}</p>
                <p><strong>优先级:</strong> {priority}</p>
                {symbol_info}
            </div>
            
            <div class="content {priority_class}">
                {content}
                {signal_details}
            </div>
            
            <div class="footer">
                <p>此邮件由 Gamma Shock 自动交易系统生成</p>
                <p>如有疑问，请联系系统管理员</p>
            </div>
        </body>
        </html>
        """
        
        # 确定优先级样式
        priority_class = f"{message.priority}-priority"
        
        # 格式化标的信息
        symbol_info = ""
        if message.symbol:
            symbol_info = f"<p><strong>标的:</strong> {message.symbol}</p>"
        
        # 格式化信号详情
        signal_details = ""
        if message.signal_data:
            signal_details = self._format_signal_details(message.signal_data)
        
        # 替换模板变量
        html_body = html_template.format(
            timestamp=message.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            priority=message.priority.upper(),
            symbol_info=symbol_info,
            content=message.content,
            signal_details=signal_details,
            priority_class=priority_class
        )
        
        return html_body
    
    def _format_signal_details(self, signal_data: Dict[str, Any]) -> str:
        """
        格式化信号详情
        
        Args:
            signal_data: 信号数据
            
        Returns:
            格式化后的HTML
        """
        if not signal_data:
            return ""
        
        html = '<div class="signal-info"><h3>📊 信号详情</h3>'
        
        # 如果是单个信号
        if 'signal_type' in signal_data:
            html += self._format_single_signal(signal_data)
        # 如果是多个信号
        elif 'signals' in signal_data:
            html += '<table><tr><th>信号类型</th><th>等级</th><th>置信度</th><th>原因</th></tr>'
            for signal in signal_data['signals']:
                html += self._format_signal_row(signal)
            html += '</table>'
        
        # 添加市场数据
        if 'market_data' in signal_data:
            html += self._format_market_data(signal_data['market_data'])
        
        # 添加AI分析
        if 'ai_analysis' in signal_data:
            html += self._format_ai_analysis(signal_data['ai_analysis'])
        
        html += '</div>'
        return html
    
    def _format_single_signal(self, signal: Dict[str, Any]) -> str:
        """
        格式化单个信号
        
        Args:
            signal: 信号数据
            
        Returns:
            格式化后的HTML
        """
        signal_type = signal.get('signal_type', 'unknown')
        level = signal.get('level', 0)
        confidence = signal.get('confidence', 0)
        reason = signal.get('reason', '无')
        
        # 信号类型图标
        type_icons = {
            'buy': '📈',
            'sell': '📉',
            'hold': '⏸️',
            'warning': '⚠️'
        }
        icon = type_icons.get(signal_type, '📊')
        
        html = f"""
        <p><strong>{icon} 信号类型:</strong> {signal_type.upper()}</p>
        <p><strong>🎯 信号等级:</strong> {level}/5</p>
        <p><strong>🎲 置信度:</strong> {confidence:.1f}%</p>
        <p><strong>💡 触发原因:</strong> {reason}</p>
        """
        
        return html
    
    def _format_signal_row(self, signal: Dict[str, Any]) -> str:
        """
        格式化信号表格行
        
        Args:
            signal: 信号数据
            
        Returns:
            表格行HTML
        """
        signal_type = signal.get('signal_type', 'unknown')
        level = signal.get('level', 0)
        confidence = signal.get('confidence', 0)
        reason = signal.get('reason', '无')[:50]  # 限制长度
        
        return f"""
        <tr>
            <td>{signal_type.upper()}</td>
            <td>{level}/5</td>
            <td>{confidence:.1f}%</td>
            <td>{reason}</td>
        </tr>
        """
    
    def _format_market_data(self, market_data: Dict[str, Any]) -> str:
        """
        格式化市场数据
        
        Args:
            market_data: 市场数据
            
        Returns:
            格式化后的HTML
        """
        html = '<h4>📈 市场数据</h4>'
        
        price_info = market_data.get('price_info', {})
        if price_info:
            current_price = price_info.get('current_price', 0)
            change_pct = price_info.get('change_pct', 0)
            volume = price_info.get('volume', 0)
            
            change_color = 'red' if change_pct < 0 else 'green'
            
            html += f"""
            <p><strong>当前价格:</strong> {current_price:.2f}</p>
            <p><strong>涨跌幅:</strong> <span style="color: {change_color}">{change_pct:+.2f}%</span></p>
            <p><strong>成交量:</strong> {volume:,.0f}</p>
            """
        
        return html
    
    def _format_ai_analysis(self, ai_analysis: Dict[str, Any]) -> str:
        """
        格式化AI分析
        
        Args:
            ai_analysis: AI分析数据
            
        Returns:
            格式化后的HTML
        """
        html = '<h4>🤖 AI分析</h4>'
        
        confirmed = ai_analysis.get('confirmed', False)
        confidence = ai_analysis.get('confidence', 0)
        assessment = ai_analysis.get('ai_assessment', 'neutral')
        recommendation = ai_analysis.get('ai_recommendation', 'hold')
        
        confirm_icon = '✅' if confirmed else '❌'
        
        html += f"""
        <p><strong>{confirm_icon} AI确认:</strong> {'是' if confirmed else '否'}</p>
        <p><strong>🎯 AI置信度:</strong> {confidence:.1f}%</p>
        <p><strong>📊 市场评估:</strong> {assessment}</p>
        <p><strong>💡 AI建议:</strong> {recommendation}</p>
        """
        
        # 添加关键因素
        key_factors = ai_analysis.get('key_factors', [])
        if key_factors:
            html += '<p><strong>🔑 关键因素:</strong></p><ul>'
            for factor in key_factors[:5]:  # 最多显示5个
                html += f'<li>{factor}</li>'
            html += '</ul>'
        
        return html
    
    def _add_attachment(self, msg: MIMEMultipart, file_path: str):
        """
        添加邮件附件
        
        Args:
            msg: 邮件消息对象
            file_path: 附件文件路径
        """
        try:
            with open(file_path, 'rb') as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            
            filename = os.path.basename(file_path)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            msg.attach(part)
            
        except Exception as e:
            logger.error(f"添加附件失败 ({file_path}): {e}")
    
    async def _send_email(self, msg: MIMEMultipart):
        """
        发送邮件
        
        Args:
            msg: 邮件消息对象
        """
        # 在线程池中执行阻塞的SMTP操作
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._send_email_sync, msg)
    
    def _send_email_sync(self, msg: MIMEMultipart):
        """
        同步发送邮件
        
        Args:
            msg: 邮件消息对象
        """
        server = None
        try:
            # 连接SMTP服务器
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            if self.use_tls:
                server.starttls()
            
            # 登录
            server.login(self.username, self.password)
            
            # 发送邮件
            text = msg.as_string()
            server.sendmail(self.from_email, msg['To'].split(', '), text)
            
        finally:
            if server:
                server.quit()

class WeChatNotificationProvider(NotificationProvider):
    """微信通知提供者（占位符，可扩展）"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        logger.info("微信通知提供者初始化（暂未实现）")
    
    def validate_config(self) -> bool:
        return True
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        logger.warning("微信通知功能暂未实现")
        return False

class SMSNotificationProvider(NotificationProvider):
    """短信通知提供者（占位符，可扩展）"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        logger.info("短信通知提供者初始化（暂未实现）")
    
    def validate_config(self) -> bool:
        return True
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        logger.warning("短信通知功能暂未实现")
        return False

class NotificationSystem:
    """
    通知系统
    
    负责管理各种通知提供者，发送通知消息
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化通知系统
        
        Args:
            config: 配置参数
        """
        # 默认配置
        default_config = {
            'email': {
                'enabled': True,
                'smtp_server': settings.email.SMTP_SERVER,
                'smtp_port': settings.email.SMTP_PORT,
                'username': settings.email.SENDER_EMAIL,
                'password': settings.email.SENDER_PASSWORD,
                'from_email': settings.email.SENDER_EMAIL,
                'use_tls': settings.email.SMTP_USE_TLS
            },
            'max_history_size': 1000
        }
        self.config = config or default_config
        
        # 初始化通知提供者
        self.providers = {}
        self._init_providers()
        
        # 通知历史
        self.notification_history = []
        self.max_history_size = self.config.get('max_history_size', 1000)
        
        # 发送统计
        self.send_stats = {
            'total_sent': 0,
            'successful_sent': 0,
            'failed_sent': 0,
            'by_type': {},
            'by_priority': {}
        }
        
        logger.info("通知系统初始化完成")
    
    def _init_providers(self):
        """
        初始化通知提供者
        """
        # 邮件通知提供者
        email_config = self.config.get('email', {})
        if email_config.get('enabled', False):
            email_provider = EmailNotificationProvider(email_config)
            if email_provider.validate_config():
                self.providers['email'] = email_provider
                logger.info("邮件通知提供者已启用")
            else:
                logger.error("邮件通知提供者配置无效")
        
        # 微信通知提供者
        wechat_config = self.config.get('wechat', {})
        if wechat_config.get('enabled', False):
            wechat_provider = WeChatNotificationProvider(wechat_config)
            if wechat_provider.validate_config():
                self.providers['wechat'] = wechat_provider
                logger.info("微信通知提供者已启用")
        
        # 短信通知提供者
        sms_config = self.config.get('sms', {})
        if sms_config.get('enabled', False):
            sms_provider = SMSNotificationProvider(sms_config)
            if sms_provider.validate_config():
                self.providers['sms'] = sms_provider
                logger.info("短信通知提供者已启用")
        
        if not self.providers:
            logger.warning("没有可用的通知提供者")
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        """
        发送通知
        
        Args:
            message: 通知消息
            
        Returns:
            是否发送成功
        """
        try:
            # 检查通知类型是否支持
            if message.notification_type not in self.providers:
                logger.error(f"不支持的通知类型: {message.notification_type}")
                message.error_message = f"不支持的通知类型: {message.notification_type}"
                self._record_notification(message, False)
                return False
            
            # 获取对应的提供者
            provider = self.providers[message.notification_type]
            
            # 发送通知
            success = await provider.send_notification(message)
            
            # 更新消息状态
            message.sent = success
            if success:
                message.sent_time = datetime.now()
            else:
                message.retry_count += 1
            
            # 记录通知
            self._record_notification(message, success)
            
            return success
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            message.error_message = str(e)
            message.retry_count += 1
            self._record_notification(message, False)
            return False
    
    async def send_signal_notification(self, symbol: str, signals: List[TradingSignal],
                                     ai_confirmation: Optional[Dict[str, Any]] = None,
                                     market_data: Optional[Dict[str, Any]] = None,
                                     notification_types: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        发送交易信号通知
        
        Args:
            symbol: 标的代码
            signals: 交易信号列表
            ai_confirmation: AI确认结果
            market_data: 市场数据
            notification_types: 通知类型列表
            
        Returns:
            各通知类型的发送结果
        """
        if not signals:
            return {}
        
        # 确定通知类型
        if notification_types is None:
            notification_types = list(self.providers.keys())
        
        # 确定优先级
        max_level = max(signal.level.value for signal in signals)
        if max_level >= 4:
            priority = 'urgent'
        elif max_level >= 3:
            priority = 'high'
        elif max_level >= 2:
            priority = 'medium'
        else:
            priority = 'low'
        
        # 生成通知标题
        signal_types = list(set(signal.signal_type.value for signal in signals))
        title = f"🚨 {symbol} 交易信号: {', '.join(signal_types).upper()}"
        
        # 生成通知内容
        content = self._generate_signal_content(symbol, signals, ai_confirmation)
        
        # 准备信号数据
        signal_data = {
            'signals': [asdict(signal) for signal in signals],
            'ai_analysis': ai_confirmation,
            'market_data': market_data
        }
        
        # 获取收件人列表
        recipients = self._get_recipients(priority)
        
        # 发送通知
        results = {}
        for notification_type in notification_types:
            if notification_type in self.providers:
                message = NotificationMessage(
                    id=f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{notification_type}",
                    title=title,
                    content=content,
                    notification_type=notification_type,
                    priority=priority,
                    recipients=recipients.get(notification_type, []),
                    timestamp=datetime.now(),
                    symbol=symbol,
                    signal_data=signal_data
                )
                
                success = await self.send_notification(message)
                results[notification_type] = success
        
        return results
    
    async def send_risk_notification(self, symbol: str, risk_assessment: Dict[str, Any],
                                   notification_types: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        发送风险通知
        
        Args:
            symbol: 标的代码
            risk_assessment: 风险评估结果
            notification_types: 通知类型列表
            
        Returns:
            各通知类型的发送结果
        """
        risk_level = risk_assessment.get('level', 'unknown')
        
        # 只有高风险才发送通知
        if risk_level != 'high':
            return {}
        
        # 确定通知类型
        if notification_types is None:
            notification_types = list(self.providers.keys())
        
        # 生成通知内容
        title = f"⚠️ {symbol} 高风险警告"
        content = self._generate_risk_content(symbol, risk_assessment)
        
        # 获取收件人列表
        recipients = self._get_recipients('urgent')
        
        # 发送通知
        results = {}
        for notification_type in notification_types:
            if notification_type in self.providers:
                message = NotificationMessage(
                    id=f"{symbol}_risk_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{notification_type}",
                    title=title,
                    content=content,
                    notification_type=notification_type,
                    priority='urgent',
                    recipients=recipients.get(notification_type, []),
                    timestamp=datetime.now(),
                    symbol=symbol
                )
                
                success = await self.send_notification(message)
                results[notification_type] = success
        
        return results
    
    async def send_system_notification(self, title: str, content: str,
                                     priority: str = 'medium',
                                     notification_types: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        发送系统通知
        
        Args:
            title: 通知标题
            content: 通知内容
            priority: 优先级
            notification_types: 通知类型列表
            
        Returns:
            各通知类型的发送结果
        """
        # 确定通知类型
        if notification_types is None:
            notification_types = list(self.providers.keys())
        
        # 获取收件人列表
        recipients = self._get_recipients(priority)
        
        # 发送通知
        results = {}
        for notification_type in notification_types:
            if notification_type in self.providers:
                message = NotificationMessage(
                    id=f"system_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{notification_type}",
                    title=f"🔧 {title}",
                    content=content,
                    notification_type=notification_type,
                    priority=priority,
                    recipients=recipients.get(notification_type, []),
                    timestamp=datetime.now()
                )
                
                success = await self.send_notification(message)
                results[notification_type] = success
        
        return results
    
    def _generate_signal_content(self, symbol: str, signals: List[TradingSignal],
                               ai_confirmation: Optional[Dict[str, Any]]) -> str:
        """
        生成信号通知内容
        
        Args:
            symbol: 标的代码
            signals: 交易信号列表
            ai_confirmation: AI确认结果
            
        Returns:
            通知内容
        """
        content_lines = []
        
        # 信号概述
        content_lines.append(f"检测到 {len(signals)} 个交易信号:")
        content_lines.append("")
        
        # 信号详情
        for i, signal in enumerate(signals, 1):
            content_lines.append(f"{i}. {signal.signal_type.value.upper()} 信号")
            content_lines.append(f"   等级: {signal.level.value}/5")
            content_lines.append(f"   置信度: {signal.confidence:.1f}%")
            content_lines.append(f"   原因: {signal.reason}")
            content_lines.append("")
        
        # AI确认
        if ai_confirmation:
            confirmed = ai_confirmation.get('confirmed', False)
            confidence = ai_confirmation.get('confidence', 0)
            content_lines.append(f"🤖 AI确认: {'✅ 是' if confirmed else '❌ 否'}")
            content_lines.append(f"🎯 AI置信度: {confidence:.1f}%")
            
            assessment = ai_confirmation.get('ai_assessment')
            if assessment:
                content_lines.append(f"📊 市场评估: {assessment}")
            
            recommendation = ai_confirmation.get('ai_recommendation')
            if recommendation:
                content_lines.append(f"💡 AI建议: {recommendation}")
            
            content_lines.append("")
        
        # 风险提示
        content_lines.append("⚠️ 风险提示: 交易有风险，投资需谨慎")
        content_lines.append("📊 请结合其他分析工具综合判断")
        
        return "\n".join(content_lines)
    
    def _generate_risk_content(self, symbol: str, risk_assessment: Dict[str, Any]) -> str:
        """
        生成风险通知内容
        
        Args:
            symbol: 标的代码
            risk_assessment: 风险评估结果
            
        Returns:
            通知内容
        """
        content_lines = []
        
        risk_level = risk_assessment.get('level', 'unknown')
        risk_score = risk_assessment.get('score', 0)
        risk_factors = risk_assessment.get('factors', [])
        
        content_lines.append(f"检测到 {symbol} 存在高风险:")
        content_lines.append("")
        content_lines.append(f"🎯 风险等级: {risk_level.upper()}")
        content_lines.append(f"📊 风险评分: {risk_score}/100")
        content_lines.append("")
        
        if risk_factors:
            content_lines.append("🔍 风险因素:")
            for factor in risk_factors:
                content_lines.append(f"  • {factor}")
            content_lines.append("")
        
        content_lines.append("⚠️ 建议:")
        content_lines.append("  • 密切关注市场变化")
        content_lines.append("  • 考虑降低仓位")
        content_lines.append("  • 设置止损点")
        
        return "\n".join(content_lines)
    
    def _get_recipients(self, priority: str) -> Dict[str, List[str]]:
        """
        获取收件人列表
        
        Args:
            priority: 优先级
            
        Returns:
            各通知类型的收件人列表
        """
        recipients = {}
        
        # 从配置中获取收件人
        for notification_type, provider_config in self.config.items():
            if isinstance(provider_config, dict) and 'recipients' in provider_config:
                type_recipients = provider_config['recipients']
                
                # 根据优先级过滤收件人
                if priority == 'urgent':
                    recipients[notification_type] = type_recipients.get('urgent', 
                                                   type_recipients.get('all', []))
                elif priority == 'high':
                    urgent_list = type_recipients.get('urgent', [])
                    high_list = type_recipients.get('high', [])
                    recipients[notification_type] = list(set(urgent_list + high_list))
                else:
                    recipients[notification_type] = type_recipients.get('all', [])
        
        return recipients
    
    def _record_notification(self, message: NotificationMessage, success: bool):
        """
        记录通知
        
        Args:
            message: 通知消息
            success: 是否成功
        """
        # 添加到历史记录
        self.notification_history.append(message)
        
        # 限制历史记录大小
        if len(self.notification_history) > self.max_history_size:
            self.notification_history = self.notification_history[-self.max_history_size:]
        
        # 更新统计信息
        self.send_stats['total_sent'] += 1
        if success:
            self.send_stats['successful_sent'] += 1
        else:
            self.send_stats['failed_sent'] += 1
        
        # 按类型统计
        type_stats = self.send_stats['by_type'].get(message.notification_type, {'sent': 0, 'success': 0})
        type_stats['sent'] += 1
        if success:
            type_stats['success'] += 1
        self.send_stats['by_type'][message.notification_type] = type_stats
        
        # 按优先级统计
        priority_stats = self.send_stats['by_priority'].get(message.priority, {'sent': 0, 'success': 0})
        priority_stats['sent'] += 1
        if success:
            priority_stats['success'] += 1
        self.send_stats['by_priority'][message.priority] = priority_stats
    
    async def retry_failed_notifications(self, max_retries: Optional[int] = None) -> int:
        """
        重试失败的通知
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            重试成功的数量
        """
        if max_retries is None:
            max_retries = 3
        
        retry_count = 0
        
        for message in self.notification_history:
            if (not message.sent and 
                message.retry_count < max_retries and 
                message.retry_count < message.max_retries):
                
                logger.info(f"重试发送通知: {message.id}")
                success = await self.send_notification(message)
                if success:
                    retry_count += 1
        
        return retry_count
    
    def get_notification_stats(self) -> Dict[str, Any]:
        """
        获取通知统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'send_stats': self.send_stats.copy(),
            'providers': list(self.providers.keys()),
            'history_size': len(self.notification_history),
            'failed_notifications': len([m for m in self.notification_history if not m.sent])
        }
    
    def get_recent_notifications(self, limit: int = 10) -> List[NotificationMessage]:
        """
        获取最近的通知
        
        Args:
            limit: 返回数量限制
            
        Returns:
            通知消息列表
        """
        # 按时间排序
        sorted_notifications = sorted(
            self.notification_history, 
            key=lambda x: x.timestamp, 
            reverse=True
        )
        
        return sorted_notifications[:limit]


# 便捷函数
def create_notification_system(config: Optional[Dict[str, Any]] = None) -> NotificationSystem:
    """
    创建通知系统
    
    Args:
        config: 配置参数
        
    Returns:
        通知系统实例
    """
    return NotificationSystem(config)


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_notification_system():
        print("测试通知系统...")
        
        # 创建通知系统
        notification_system = create_notification_system()
        
        # 获取统计信息
        stats = notification_system.get_notification_stats()
        print(f"通知系统状态: {stats}")
        
        # 测试系统通知
        print("\n发送测试系统通知...")
        results = await notification_system.send_system_notification(
            title="系统测试",
            content="这是一个测试通知，用于验证通知系统功能。",
            priority="low"
        )
        print(f"发送结果: {results}")
        
        # 获取最近通知
        recent = notification_system.get_recent_notifications(limit=3)
        print(f"\n最近通知数量: {len(recent)}")
        
        for notification in recent:
            print(f"- {notification.title} ({notification.notification_type})")
    
    # 运行测试
    asyncio.run(test_notification_system())