#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标计算模块

实现项目所需的所有技术指标计算，包括：
- EMA均线系统 (8/21/55/125)
- KDJ随机指标 (14周期)
- MACD指标 (12/26/9)
- 成交量分析
- 布林带指标

作者: AI Assistant
创建时间: 2024-12-11
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """
    技术指标计算类
    
    提供统一的技术指标计算接口，支持多种常用技术指标的计算
    """
    
    @staticmethod
    def calculate_ema_system(df: pd.DataFrame, periods: list = [8, 21, 55, 125]) -> pd.DataFrame:
        """
        计算EMA均线系统
        
        Args:
            df: 包含OHLCV数据的DataFrame
            periods: EMA周期列表，默认[8, 21, 55, 125]
            
        Returns:
            添加了EMA列的DataFrame
        """
        try:
            result_df = df.copy()
            
            for period in periods:
                col_name = f'EMA{period}'
                result_df[col_name] = result_df['close'].ewm(span=period, adjust=False).mean()
                logger.debug(f"计算完成 {col_name}")
            
            return result_df
            
        except Exception as e:
            logger.error(f"计算EMA均线系统时出错: {e}")
            raise
    
    @staticmethod
    def calculate_kdj(df: pd.DataFrame, period: int = 14, k_period: int = 3, d_period: int = 3) -> pd.DataFrame:
        """
        计算KDJ随机指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            period: RSV计算周期，默认14
            k_period: K值平滑周期，默认3
            d_period: D值平滑周期，默认3
            
        Returns:
            添加了K、D、J列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 计算RSV
            low_list = result_df['low'].rolling(window=period, min_periods=1).min()
            high_list = result_df['high'].rolling(window=period, min_periods=1).max()
            
            # 避免除零错误
            rsv = np.where(
                high_list != low_list,
                (result_df['close'] - low_list) / (high_list - low_list) * 100,
                50  # 当高低价相等时，设置RSV为50
            )
            
            # 计算K、D、J值
            result_df['K'] = pd.Series(rsv).ewm(com=k_period-1, adjust=False).mean()
            result_df['D'] = result_df['K'].ewm(com=d_period-1, adjust=False).mean()
            result_df['J'] = 3 * result_df['K'] - 2 * result_df['D']
            
            logger.debug("KDJ指标计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算KDJ指标时出错: {e}")
            raise
    
    @staticmethod
    def calculate_macd(df: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> pd.DataFrame:
        """
        计算MACD指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            fast_period: 快线周期，默认12
            slow_period: 慢线周期，默认26
            signal_period: 信号线周期，默认9
            
        Returns:
            添加了DIF、DEA、MACD列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 计算快慢EMA
            ema_fast = result_df['close'].ewm(span=fast_period, adjust=False).mean()
            ema_slow = result_df['close'].ewm(span=slow_period, adjust=False).mean()
            
            # 计算DIF线
            result_df['DIF'] = ema_fast - ema_slow
            
            # 计算DEA线（信号线）
            result_df['DEA'] = result_df['DIF'].ewm(span=signal_period, adjust=False).mean()
            
            # 计算MACD柱状图
            result_df['MACD'] = (result_df['DIF'] - result_df['DEA']) * 2
            
            logger.debug("MACD指标计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算MACD指标时出错: {e}")
            raise
    
    @staticmethod
    def calculate_volume_analysis(df: pd.DataFrame, period: int = 10) -> pd.DataFrame:
        """
        计算成交量分析指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            period: 成交量均值计算周期，默认10
            
        Returns:
            添加了成交量分析列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 计算成交量均值
            result_df['VOL_MA'] = result_df['volume'].rolling(window=period, min_periods=1).mean()
            
            # 计算成交量比率
            result_df['VOL_RATIO'] = result_df['volume'] / result_df['VOL_MA']
            
            # 成交量异常标记（大于1.3倍均值）
            result_df['VOL_ABNORMAL'] = result_df['VOL_RATIO'] > 1.3
            
            logger.debug("成交量分析计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算成交量分析时出错: {e}")
            raise
    
    @staticmethod
    def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: float = 2.0) -> pd.DataFrame:
        """
        计算布林带指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            period: 移动平均周期，默认20
            std_dev: 标准差倍数，默认2.0
            
        Returns:
            添加了布林带上轨、中轨、下轨的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 计算中轨（移动平均）
            result_df['BB_MIDDLE'] = result_df['close'].rolling(window=period, min_periods=1).mean()
            
            # 计算标准差
            std = result_df['close'].rolling(window=period, min_periods=1).std()
            
            # 计算上轨和下轨
            result_df['BB_UPPER'] = result_df['BB_MIDDLE'] + (std * std_dev)
            result_df['BB_LOWER'] = result_df['BB_MIDDLE'] - (std * std_dev)
            
            # 计算布林带宽度
            result_df['BB_WIDTH'] = (result_df['BB_UPPER'] - result_df['BB_LOWER']) / result_df['BB_MIDDLE']
            
            # 计算价格在布林带中的位置
            result_df['BB_POSITION'] = (result_df['close'] - result_df['BB_LOWER']) / (result_df['BB_UPPER'] - result_df['BB_LOWER'])
            
            logger.debug("布林带指标计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算布林带指标时出错: {e}")
            raise
    
    @staticmethod
    def calculate_iv_statistics(iv_data: list, period: int = 30) -> Dict[str, float]:
        """
        计算波动率统计数据
        
        Args:
            iv_data: 波动率数据列表
            period: 统计周期，默认30天
            
        Returns:
            包含均值、标准差、百分位等统计信息的字典
        """
        try:
            if not iv_data or len(iv_data) == 0:
                logger.warning("波动率数据为空")
                return {
                    'mean': 0.0,
                    'std': 0.0,
                    'current': 0.0,
                    'percentile_25': 0.0,
                    'percentile_50': 0.0,
                    'percentile_75': 0.0,
                    'percentile_90': 0.0
                }
            
            # 取最近period天的数据
            recent_data = iv_data[-period:] if len(iv_data) >= period else iv_data
            
            iv_array = np.array(recent_data)
            current_iv = iv_data[-1] if iv_data else 0.0
            
            stats = {
                'mean': float(np.mean(iv_array)),
                'std': float(np.std(iv_array)),
                'current': float(current_iv),
                'percentile_25': float(np.percentile(iv_array, 25)),
                'percentile_50': float(np.percentile(iv_array, 50)),
                'percentile_75': float(np.percentile(iv_array, 75)),
                'percentile_90': float(np.percentile(iv_array, 90))
            }
            
            logger.debug(f"波动率统计计算完成: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"计算波动率统计时出错: {e}")
            raise
    
    @staticmethod
    def calculate_pcr_ratio(put_volume: float, call_volume: float) -> float:
        """
        计算沽购比（Put/Call Ratio）
        
        Args:
            put_volume: 认沽期权成交量
            call_volume: 认购期权成交量
            
        Returns:
            沽购比值
        """
        try:
            if call_volume == 0:
                logger.warning("认购期权成交量为0，无法计算PCR")
                return 0.0
            
            pcr = put_volume / call_volume
            logger.debug(f"PCR计算完成: {pcr:.4f}")
            return pcr
            
        except Exception as e:
            logger.error(f"计算PCR时出错: {e}")
            return 0.0
    
    @classmethod
    def calculate_all_indicators(cls, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有技术指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含所有技术指标的DataFrame
        """
        try:
            logger.info("开始计算所有技术指标")
            
            # 确保数据完整性
            if df.empty or len(df) < 2:
                logger.warning("数据不足，无法计算技术指标")
                return df
            
            result_df = df.copy()
            
            # 计算各项技术指标
            result_df = cls.calculate_ema_system(result_df)
            result_df = cls.calculate_kdj(result_df)
            result_df = cls.calculate_macd(result_df)
            result_df = cls.calculate_volume_analysis(result_df)
            result_df = cls.calculate_bollinger_bands(result_df)
            
            logger.info("所有技术指标计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算所有技术指标时出错: {e}")
            raise


def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    便捷函数：计算所有技术指标
    
    Args:
        df: 包含OHLCV数据的DataFrame
        
    Returns:
        包含所有技术指标的DataFrame
    """
    return TechnicalIndicators.calculate_all_indicators(df)


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 创建测试数据
    test_data = {
        'datetime': pd.date_range('2024-01-01', periods=100, freq='D'),
        'open': np.random.randn(100).cumsum() + 100,
        'high': np.random.randn(100).cumsum() + 105,
        'low': np.random.randn(100).cumsum() + 95,
        'close': np.random.randn(100).cumsum() + 100,
        'volume': np.random.randint(1000000, 10000000, 100)
    }
    
    test_df = pd.DataFrame(test_data)
    test_df['high'] = test_df[['open', 'close']].max(axis=1) + abs(np.random.randn(100))
    test_df['low'] = test_df[['open', 'close']].min(axis=1) - abs(np.random.randn(100))
    
    print("测试技术指标计算...")
    result = calculate_technical_indicators(test_df)
    print(f"计算完成，数据形状: {result.shape}")
    print(f"包含列: {list(result.columns)}")
    print("\n最后5行数据:")
    print(result.tail())