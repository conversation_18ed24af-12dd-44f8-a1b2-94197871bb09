#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标模块

包含所有技术指标计算功能：
- 基础技术指标（EMA、KDJ、MACD等）
- 威廉指标变种
- 波动率分析
- 期权情绪分析

作者: AI Assistant
创建时间: 2024-12-11
"""

from .technical_indicators import (
    TechnicalIndicators,
    calculate_technical_indicators
)

# 为了保持向后兼容性，创建别名
calculate_all_indicators = TechnicalIndicators.calculate_all_indicators

from .williams_variant import (
    WilliamsVariant,
    calculate_williams_variant
)

from .volatility_analyzer import (
    VolatilityAnalyzer,
    calculate_volatility_indicators
)

from .option_sentiment import (
    OptionSentimentAnalyzer,
    analyze_option_sentiment
)

__all__ = [
    # 基础技术指标
    'TechnicalIndicators',
    'calculate_all_indicators',
    
    # 威廉指标变种
    'WilliamsVariant',
    'calculate_williams_variant',
    
    # 波动率分析
    'VolatilityAnalyzer',
    'calculate_volatility_indicators',
    
    # 期权情绪分析
    'OptionSentimentAnalyzer',
    'analyze_option_sentiment'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'AI Assistant'
__description__ = 'Gamma Shock CN - 技术指标分析模块'