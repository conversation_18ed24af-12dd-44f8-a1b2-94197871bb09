#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
威廉指标变种模块

实现项目核心创新的威廉指标变种，包括：
- ZLS长期线（21日EMA平滑）
- CZX短期线（5日EMA平滑）
- C信号（超卖反弹）
- P信号（超买回调）

作者: AI Assistant
创建时间: 2024-12-11
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class WilliamsVariant:
    """
    威廉指标变种计算类
    
    核心创新指标，提供更敏感的超买超卖信号
    """
    
    def __init__(self, n_period: int = 19, zls_period: int = 21, czx_period: int = 5):
        """
        初始化威廉指标变种
        
        Args:
            n_period: 威廉指标基础计算周期，默认19
            zls_period: ZLS长期线EMA周期，默认21
            czx_period: CZX短期线EMA周期，默认5
        """
        self.n_period = n_period
        self.zls_period = zls_period
        self.czx_period = czx_period
        
        # 信号阈值
        self.c_signal_threshold = 0.1  # C信号阈值：ZLS < 0.1
        self.p_signal_threshold = 0.25  # P信号阈值：ZLS > 0.25
        
        logger.info(f"威廉指标变种初始化完成 - N:{n_period}, ZLS:{zls_period}, CZX:{czx_period}")
    
    def calculate_williams_base(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算威廉指标基础值
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            添加了威廉基础计算列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 计算N周期内的最高价和最低价
            result_df['VAR1'] = result_df['high'].rolling(window=self.n_period, min_periods=1).max()
            result_df['VAR2'] = result_df['low'].rolling(window=self.n_period, min_periods=1).min()
            
            # 计算威廉指标基础值
            # 避免除零错误
            denominator = result_df['VAR1'] - result_df['VAR2']
            result_df['WILLIAMS_BASE'] = np.where(
                denominator != 0,
                (result_df['close'] - result_df['VAR2']) / denominator,
                0.5  # 当分母为0时，设置为中性值
            )
            
            logger.debug("威廉指标基础值计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算威廉指标基础值时出错: {e}")
            raise
    
    def calculate_zls_czx(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算ZLS和CZX线
        
        Args:
            df: 包含威廉基础值的DataFrame
            
        Returns:
            添加了ZLS、CZX、HLB列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 确保威廉基础值存在
            if 'WILLIAMS_BASE' not in result_df.columns:
                result_df = self.calculate_williams_base(result_df)
            
            # ZLS: 21日EMA平滑后减0.5 (长期线)
            zls_ema = result_df['WILLIAMS_BASE'].ewm(span=self.zls_period, adjust=False).mean()
            result_df['ZLS'] = zls_ema - 0.5
            
            # CZX: 5日EMA平滑后减0.5 (短期线)
            czx_ema = result_df['WILLIAMS_BASE'].ewm(span=self.czx_period, adjust=False).mean()
            result_df['CZX'] = czx_ema - 0.5
            
            # HLB: 两线差值
            result_df['HLB'] = result_df['CZX'] - result_df['ZLS']
            
            logger.debug("ZLS和CZX线计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算ZLS和CZX线时出错: {e}")
            raise
    
    def detect_c_signal(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        检测C信号（超卖反弹信号）
        
        C信号条件：CZX上穿ZLS且ZLS < 0.1
        
        Args:
            df: 包含ZLS、CZX数据的DataFrame
            
        Returns:
            添加了C信号检测列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 确保ZLS和CZX存在
            if 'ZLS' not in result_df.columns or 'CZX' not in result_df.columns:
                result_df = self.calculate_zls_czx(result_df)
            
            # 检测CZX上穿ZLS
            czx_cross_up = (
                (result_df['CZX'] > result_df['ZLS']) & 
                (result_df['CZX'].shift(1) <= result_df['ZLS'].shift(1))
            )
            
            # 检测ZLS < 阈值（超卖状态）
            zls_oversold = result_df['ZLS'] < self.c_signal_threshold
            
            # C信号：同时满足两个条件
            result_df['C_SIGNAL'] = czx_cross_up & zls_oversold
            
            # 记录信号强度
            result_df['C_SIGNAL_STRENGTH'] = np.where(
                result_df['C_SIGNAL'],
                1.0 - result_df['ZLS'],  # ZLS越小，信号越强
                0.0
            )
            
            logger.debug(f"C信号检测完成，发现 {result_df['C_SIGNAL'].sum()} 个信号")
            return result_df
            
        except Exception as e:
            logger.error(f"检测C信号时出错: {e}")
            raise
    
    def detect_p_signal(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        检测P信号（超买回调信号）
        
        P信号条件：ZLS上穿CZX且ZLS > 0.25
        
        Args:
            df: 包含ZLS、CZX数据的DataFrame
            
        Returns:
            添加了P信号检测列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 确保ZLS和CZX存在
            if 'ZLS' not in result_df.columns or 'CZX' not in result_df.columns:
                result_df = self.calculate_zls_czx(result_df)
            
            # 检测ZLS上穿CZX
            zls_cross_up = (
                (result_df['ZLS'] > result_df['CZX']) & 
                (result_df['ZLS'].shift(1) <= result_df['CZX'].shift(1))
            )
            
            # 检测ZLS > 阈值（超买状态）
            zls_overbought = result_df['ZLS'] > self.p_signal_threshold
            
            # P信号：同时满足两个条件
            result_df['P_SIGNAL'] = zls_cross_up & zls_overbought
            
            # 记录信号强度
            result_df['P_SIGNAL_STRENGTH'] = np.where(
                result_df['P_SIGNAL'],
                result_df['ZLS'] - 0.25,  # ZLS越大，信号越强
                0.0
            )
            
            logger.debug(f"P信号检测完成，发现 {result_df['P_SIGNAL'].sum()} 个信号")
            return result_df
            
        except Exception as e:
            logger.error(f"检测P信号时出错: {e}")
            raise
    
    def calculate_all_williams_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有威廉指标变种
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含所有威廉指标的DataFrame
        """
        try:
            logger.info("开始计算威廉指标变种")
            
            if df.empty or len(df) < self.n_period:
                logger.warning(f"数据不足，需要至少 {self.n_period} 条记录")
                return df
            
            result_df = df.copy()
            
            # 依次计算各项指标
            result_df = self.calculate_williams_base(result_df)
            result_df = self.calculate_zls_czx(result_df)
            result_df = self.detect_c_signal(result_df)
            result_df = self.detect_p_signal(result_df)
            
            logger.info("威廉指标变种计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算威廉指标变种时出错: {e}")
            raise
    
    def get_latest_signals(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        获取最新的威廉信号
        
        Args:
            df: 包含威廉指标的DataFrame
            
        Returns:
            包含最新信号信息的字典
        """
        try:
            if df.empty:
                return {
                    'c_signal': False,
                    'p_signal': False,
                    'c_strength': 0.0,
                    'p_strength': 0.0,
                    'zls_value': 0.0,
                    'czx_value': 0.0,
                    'hlb_value': 0.0
                }
            
            latest = df.iloc[-1]
            
            signals = {
                'c_signal': bool(latest.get('C_SIGNAL', False)),
                'p_signal': bool(latest.get('P_SIGNAL', False)),
                'c_strength': float(latest.get('C_SIGNAL_STRENGTH', 0.0)),
                'p_strength': float(latest.get('P_SIGNAL_STRENGTH', 0.0)),
                'zls_value': float(latest.get('ZLS', 0.0)),
                'czx_value': float(latest.get('CZX', 0.0)),
                'hlb_value': float(latest.get('HLB', 0.0))
            }
            
            logger.debug(f"最新威廉信号: {signals}")
            return signals
            
        except Exception as e:
            logger.error(f"获取最新威廉信号时出错: {e}")
            return {
                'c_signal': False,
                'p_signal': False,
                'c_strength': 0.0,
                'p_strength': 0.0,
                'zls_value': 0.0,
                'czx_value': 0.0,
                'hlb_value': 0.0
            }
    
    def analyze_williams_trend(self, df: pd.DataFrame, lookback: int = 5) -> Dict[str, any]:
        """
        分析威廉指标趋势
        
        Args:
            df: 包含威廉指标的DataFrame
            lookback: 回看周期，默认5
            
        Returns:
            威廉指标趋势分析结果
        """
        try:
            if len(df) < lookback + 1:
                logger.warning(f"数据不足，无法进行趋势分析")
                return {
                    'zls_trend': 'unknown',
                    'czx_trend': 'unknown',
                    'divergence': False,
                    'trend_strength': 0.0
                }
            
            recent_data = df.tail(lookback + 1)
            
            # ZLS趋势分析
            zls_values = recent_data['ZLS'].values
            zls_trend = 'up' if zls_values[-1] > zls_values[0] else 'down'
            
            # CZX趋势分析
            czx_values = recent_data['CZX'].values
            czx_trend = 'up' if czx_values[-1] > czx_values[0] else 'down'
            
            # 背离检测
            divergence = zls_trend != czx_trend
            
            # 趋势强度
            zls_change = abs(zls_values[-1] - zls_values[0])
            czx_change = abs(czx_values[-1] - czx_values[0])
            trend_strength = (zls_change + czx_change) / 2
            
            analysis = {
                'zls_trend': zls_trend,
                'czx_trend': czx_trend,
                'divergence': divergence,
                'trend_strength': trend_strength,
                'zls_change': zls_change,
                'czx_change': czx_change
            }
            
            logger.debug(f"威廉趋势分析: {analysis}")
            return analysis
            
        except Exception as e:
            logger.error(f"分析威廉趋势时出错: {e}")
            return {
                'zls_trend': 'unknown',
                'czx_trend': 'unknown',
                'divergence': False,
                'trend_strength': 0.0
            }


def calculate_williams_variant(df: pd.DataFrame, n_period: int = 19, 
                             zls_period: int = 21, czx_period: int = 5) -> pd.DataFrame:
    """
    便捷函数：计算威廉指标变种
    
    Args:
        df: 包含OHLCV数据的DataFrame
        n_period: 威廉指标基础计算周期
        zls_period: ZLS长期线EMA周期
        czx_period: CZX短期线EMA周期
        
    Returns:
        包含威廉指标变种的DataFrame
    """
    williams = WilliamsVariant(n_period, zls_period, czx_period)
    return williams.calculate_all_williams_indicators(df)


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 创建测试数据
    np.random.seed(42)
    test_data = {
        'datetime': pd.date_range('2024-01-01', periods=100, freq='D'),
        'open': np.random.randn(100).cumsum() + 100,
        'close': np.random.randn(100).cumsum() + 100,
        'volume': np.random.randint(1000000, 10000000, 100)
    }
    
    test_df = pd.DataFrame(test_data)
    test_df['high'] = test_df[['open', 'close']].max(axis=1) + abs(np.random.randn(100))
    test_df['low'] = test_df[['open', 'close']].min(axis=1) - abs(np.random.randn(100))
    
    print("测试威廉指标变种计算...")
    
    williams = WilliamsVariant()
    result = williams.calculate_all_williams_indicators(test_df)
    
    print(f"计算完成，数据形状: {result.shape}")
    print(f"包含列: {list(result.columns)}")
    
    # 检查信号
    c_signals = result['C_SIGNAL'].sum()
    p_signals = result['P_SIGNAL'].sum()
    print(f"\n发现 C信号: {c_signals} 个")
    print(f"发现 P信号: {p_signals} 个")
    
    # 最新信号
    latest_signals = williams.get_latest_signals(result)
    print(f"\n最新信号: {latest_signals}")
    
    # 趋势分析
    trend_analysis = williams.analyze_williams_trend(result)
    print(f"\n趋势分析: {trend_analysis}")
    
    print("\n最后5行威廉指标数据:")
    williams_cols = ['ZLS', 'CZX', 'HLB', 'C_SIGNAL', 'P_SIGNAL']
    print(result[williams_cols].tail())