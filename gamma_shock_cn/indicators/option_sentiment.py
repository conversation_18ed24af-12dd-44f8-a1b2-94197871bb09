#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期权情绪分析模块

实现期权情绪相关的分析功能，包括：
- Put/Call Ratio (PCR) 计算
- 期权持仓分析
- 期权情绪指标
- 期权偏度分析
- 期权流量分析

作者: AI Assistant
创建时间: 2024-12-11
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class OptionSentimentAnalyzer:
    """
    期权情绪分析器
    
    提供全面的期权情绪分析功能，支持多种期权情绪指标计算和分析
    """
    
    def __init__(self):
        """
        初始化期权情绪分析器
        """
        # PCR阈值设置
        self.pcr_high_threshold = 1.2  # 高PCR阈值
        self.pcr_low_threshold = 0.8   # 低PCR阈值
        
        # 期权情绪等级
        self.sentiment_levels = {
            'extremely_bearish': 1.5,
            'bearish': 1.2,
            'neutral': 1.0,
            'bullish': 0.8,
            'extremely_bullish': 0.6
        }
        
        logger.info("期权情绪分析器初始化完成")
    
    def calculate_pcr(self, put_volume: Union[float, int], call_volume: Union[float, int], 
                     put_oi: Optional[Union[float, int]] = None, 
                     call_oi: Optional[Union[float, int]] = None) -> Dict[str, float]:
        """
        计算Put/Call Ratio
        
        Args:
            put_volume: 看跌期权成交量
            call_volume: 看涨期权成交量
            put_oi: 看跌期权持仓量（可选）
            call_oi: 看涨期权持仓量（可选）
            
        Returns:
            PCR指标字典
        """
        try:
            pcr_data = {}
            
            # 成交量PCR
            if call_volume > 0:
                pcr_volume = put_volume / call_volume
                pcr_data['pcr_volume'] = float(pcr_volume)
            else:
                pcr_data['pcr_volume'] = 0.0
                logger.warning("看涨期权成交量为0，无法计算成交量PCR")
            
            # 持仓量PCR
            if put_oi is not None and call_oi is not None and call_oi > 0:
                pcr_oi = put_oi / call_oi
                pcr_data['pcr_oi'] = float(pcr_oi)
            else:
                pcr_data['pcr_oi'] = None
            
            # 综合PCR（如果有持仓数据，则取平均值）
            if pcr_data['pcr_oi'] is not None:
                pcr_data['pcr_combined'] = (pcr_data['pcr_volume'] + pcr_data['pcr_oi']) / 2
            else:
                pcr_data['pcr_combined'] = pcr_data['pcr_volume']
            
            # 情绪分类
            pcr_value = pcr_data['pcr_combined']
            if pcr_value >= self.sentiment_levels['extremely_bearish']:
                sentiment = 'extremely_bearish'
            elif pcr_value >= self.sentiment_levels['bearish']:
                sentiment = 'bearish'
            elif pcr_value >= self.sentiment_levels['bullish']:
                sentiment = 'neutral'
            elif pcr_value >= self.sentiment_levels['extremely_bullish']:
                sentiment = 'bullish'
            else:
                sentiment = 'extremely_bullish'
            
            pcr_data['sentiment'] = sentiment
            
            logger.debug(f"PCR计算完成: {pcr_data}")
            return pcr_data
            
        except Exception as e:
            logger.error(f"计算PCR时出错: {e}")
            return {
                'pcr_volume': 0.0,
                'pcr_oi': None,
                'pcr_combined': 0.0,
                'sentiment': 'neutral'
            }
    
    def analyze_pcr_trend(self, pcr_history: List[float], period: int = 5) -> Dict[str, any]:
        """
        分析PCR趋势
        
        Args:
            pcr_history: PCR历史数据列表
            period: 分析周期，默认5天
            
        Returns:
            PCR趋势分析结果
        """
        try:
            if len(pcr_history) < period:
                logger.warning(f"PCR历史数据不足{period}天，无法进行趋势分析")
                return {
                    'trend': 'unknown',
                    'trend_strength': 0.0,
                    'momentum': 0.0,
                    'volatility': 0.0,
                    'mean_reversion_signal': False
                }
            
            recent_pcr = np.array(pcr_history[-period:])
            
            # 趋势方向
            if recent_pcr[-1] > recent_pcr[0]:
                trend = 'rising'
            elif recent_pcr[-1] < recent_pcr[0]:
                trend = 'falling'
            else:
                trend = 'flat'
            
            # 趋势强度（线性回归斜率）
            x = np.arange(len(recent_pcr))
            slope = np.polyfit(x, recent_pcr, 1)[0]
            trend_strength = abs(slope)
            
            # 动量（最近3天vs前面的平均值）
            if len(recent_pcr) >= 3:
                recent_avg = np.mean(recent_pcr[-3:])
                earlier_avg = np.mean(recent_pcr[:-3]) if len(recent_pcr) > 3 else recent_avg
                momentum = (recent_avg - earlier_avg) / earlier_avg if earlier_avg != 0 else 0
            else:
                momentum = 0.0
            
            # PCR波动率
            pcr_volatility = np.std(recent_pcr)
            
            # 均值回归信号
            pcr_mean = np.mean(pcr_history)
            current_pcr = pcr_history[-1]
            z_score = (current_pcr - pcr_mean) / np.std(pcr_history) if np.std(pcr_history) > 0 else 0
            mean_reversion_signal = abs(z_score) > 1.5  # 超过1.5个标准差
            
            trend_analysis = {
                'trend': trend,
                'trend_strength': float(trend_strength),
                'momentum': float(momentum),
                'volatility': float(pcr_volatility),
                'mean_reversion_signal': mean_reversion_signal,
                'z_score': float(z_score),
                'current_vs_mean': 'above' if current_pcr > pcr_mean else 'below'
            }
            
            logger.debug(f"PCR趋势分析: {trend_analysis}")
            return trend_analysis
            
        except Exception as e:
            logger.error(f"分析PCR趋势时出错: {e}")
            return {
                'trend': 'unknown',
                'trend_strength': 0.0,
                'momentum': 0.0,
                'volatility': 0.0,
                'mean_reversion_signal': False
            }
    
    def calculate_option_flow(self, option_data: pd.DataFrame) -> Dict[str, any]:
        """
        计算期权流量指标
        
        Args:
            option_data: 期权数据DataFrame，包含成交量、持仓量等信息
            
        Returns:
            期权流量分析结果
        """
        try:
            if option_data.empty:
                logger.warning("期权数据为空")
                return {
                    'total_volume': 0,
                    'call_volume': 0,
                    'put_volume': 0,
                    'volume_ratio': 0.0,
                    'large_trade_ratio': 0.0,
                    'flow_direction': 'neutral'
                }
            
            # 基础流量统计
            total_volume = option_data.get('volume', pd.Series()).sum()
            
            # 分离看涨和看跌期权
            if 'option_type' in option_data.columns:
                call_data = option_data[option_data['option_type'] == 'call']
                put_data = option_data[option_data['option_type'] == 'put']
                
                call_volume = call_data.get('volume', pd.Series()).sum()
                put_volume = put_data.get('volume', pd.Series()).sum()
            else:
                # 如果没有期权类型列，尝试从其他列推断
                call_volume = option_data.get('call_volume', pd.Series()).sum()
                put_volume = option_data.get('put_volume', pd.Series()).sum()
            
            # 成交量比率
            volume_ratio = call_volume / put_volume if put_volume > 0 else float('inf')
            
            # 大单交易比例（假设单笔成交量>1000为大单）
            if 'volume' in option_data.columns:
                large_trades = option_data[option_data['volume'] > 1000]
                large_trade_volume = large_trades['volume'].sum()
                large_trade_ratio = large_trade_volume / total_volume if total_volume > 0 else 0
            else:
                large_trade_ratio = 0.0
            
            # 流量方向判断
            if volume_ratio > 1.2:
                flow_direction = 'bullish'
            elif volume_ratio < 0.8:
                flow_direction = 'bearish'
            else:
                flow_direction = 'neutral'
            
            flow_analysis = {
                'total_volume': int(total_volume),
                'call_volume': int(call_volume),
                'put_volume': int(put_volume),
                'volume_ratio': float(volume_ratio),
                'large_trade_ratio': float(large_trade_ratio),
                'flow_direction': flow_direction,
                'volume_imbalance': abs(call_volume - put_volume) / (call_volume + put_volume) if (call_volume + put_volume) > 0 else 0
            }
            
            logger.debug(f"期权流量分析: {flow_analysis}")
            return flow_analysis
            
        except Exception as e:
            logger.error(f"计算期权流量时出错: {e}")
            return {
                'total_volume': 0,
                'call_volume': 0,
                'put_volume': 0,
                'volume_ratio': 0.0,
                'large_trade_ratio': 0.0,
                'flow_direction': 'neutral'
            }
    
    def analyze_option_skew(self, iv_data: Dict[str, List[float]]) -> Dict[str, float]:
        """
        分析期权偏度
        
        Args:
            iv_data: 不同行权价的隐含波动率数据，格式：{'strike_price': [iv_values]}
            
        Returns:
            期权偏度分析结果
        """
        try:
            if not iv_data or len(iv_data) < 3:
                logger.warning("IV数据不足，无法计算偏度")
                return {
                    'skew': 0.0,
                    'skew_direction': 'neutral',
                    'atm_iv': 0.0,
                    'otm_put_iv': 0.0,
                    'otm_call_iv': 0.0,
                    'put_call_skew': 0.0
                }
            
            # 将数据转换为DataFrame便于处理
            skew_data = []
            for strike, iv_list in iv_data.items():
                if iv_list:
                    skew_data.append({
                        'strike': float(strike),
                        'iv': np.mean(iv_list)  # 取平均IV
                    })
            
            if len(skew_data) < 3:
                logger.warning("有效IV数据点不足")
                return {
                    'skew': 0.0,
                    'skew_direction': 'neutral',
                    'atm_iv': 0.0,
                    'otm_put_iv': 0.0,
                    'otm_call_iv': 0.0,
                    'put_call_skew': 0.0
                }
            
            skew_df = pd.DataFrame(skew_data).sort_values('strike')
            
            # 找到ATM期权（假设中间的行权价为ATM）
            mid_idx = len(skew_df) // 2
            atm_iv = skew_df.iloc[mid_idx]['iv']
            
            # OTM Put（低行权价）和OTM Call（高行权价）
            otm_put_iv = skew_df.iloc[0]['iv'] if len(skew_df) > 0 else atm_iv
            otm_call_iv = skew_df.iloc[-1]['iv'] if len(skew_df) > 0 else atm_iv
            
            # 计算偏度
            # 正偏度：OTM Put IV > OTM Call IV（看跌偏度）
            # 负偏度：OTM Call IV > OTM Put IV（看涨偏度）
            put_call_skew = otm_put_iv - otm_call_iv
            
            # 标准化偏度（相对于ATM IV）
            normalized_skew = put_call_skew / atm_iv if atm_iv > 0 else 0
            
            # 偏度方向
            if normalized_skew > 0.05:  # 5%以上差异
                skew_direction = 'put_skew'  # 看跌偏度
            elif normalized_skew < -0.05:
                skew_direction = 'call_skew'  # 看涨偏度
            else:
                skew_direction = 'neutral'
            
            skew_analysis = {
                'skew': float(normalized_skew),
                'skew_direction': skew_direction,
                'atm_iv': float(atm_iv),
                'otm_put_iv': float(otm_put_iv),
                'otm_call_iv': float(otm_call_iv),
                'put_call_skew': float(put_call_skew),
                'skew_strength': abs(normalized_skew)
            }
            
            logger.debug(f"期权偏度分析: {skew_analysis}")
            return skew_analysis
            
        except Exception as e:
            logger.error(f"分析期权偏度时出错: {e}")
            return {
                'skew': 0.0,
                'skew_direction': 'neutral',
                'atm_iv': 0.0,
                'otm_put_iv': 0.0,
                'otm_call_iv': 0.0,
                'put_call_skew': 0.0
            }
    
    def generate_sentiment_signals(self, pcr_data: Dict[str, float], 
                                 flow_data: Dict[str, any],
                                 skew_data: Dict[str, float]) -> List[Dict[str, any]]:
        """
        基于期权情绪生成交易信号
        
        Args:
            pcr_data: PCR数据
            flow_data: 期权流量数据
            skew_data: 期权偏度数据
            
        Returns:
            期权情绪信号列表
        """
        try:
            signals = []
            
            pcr_value = pcr_data.get('pcr_combined', 1.0)
            sentiment = pcr_data.get('sentiment', 'neutral')
            flow_direction = flow_data.get('flow_direction', 'neutral')
            skew_direction = skew_data.get('skew_direction', 'neutral')
            
            # 高PCR策略（看跌情绪过度）
            if pcr_value > self.pcr_high_threshold:
                # 多重确认：PCR高 + 看跌偏度 + 看跌流量
                if skew_direction == 'put_skew' and flow_direction == 'bearish':
                    signals.append({
                        'level': 5,
                        'type': 'high_pcr_contrarian',
                        'strategy': 'buy_call',  # 反向操作
                        'reason': f'PCR过高({pcr_value:.2f})，看跌情绪过度，反向买入看涨期权',
                        'sentiment': 'contrarian_bullish',
                        'signal_strength': min(2.0, pcr_value / self.pcr_high_threshold)
                    })
                
                # 单一确认：仅PCR高
                elif sentiment in ['extremely_bearish', 'bearish']:
                    signals.append({
                        'level': 3,
                        'type': 'high_pcr_simple',
                        'strategy': 'buy_call',
                        'reason': f'PCR高({pcr_value:.2f})，市场看跌情绪浓厚，考虑反向操作',
                        'sentiment': 'contrarian_bullish',
                        'signal_strength': min(1.5, pcr_value / self.pcr_high_threshold)
                    })
            
            # 低PCR策略（看涨情绪过度）
            elif pcr_value < self.pcr_low_threshold:
                # 多重确认：PCR低 + 看涨偏度 + 看涨流量
                if skew_direction == 'call_skew' and flow_direction == 'bullish':
                    signals.append({
                        'level': 5,
                        'type': 'low_pcr_contrarian',
                        'strategy': 'buy_put',  # 反向操作
                        'reason': f'PCR过低({pcr_value:.2f})，看涨情绪过度，反向买入看跌期权',
                        'sentiment': 'contrarian_bearish',
                        'signal_strength': min(2.0, self.pcr_low_threshold / pcr_value)
                    })
                
                # 单一确认：仅PCR低
                elif sentiment in ['extremely_bullish', 'bullish']:
                    signals.append({
                        'level': 3,
                        'type': 'low_pcr_simple',
                        'strategy': 'buy_put',
                        'reason': f'PCR低({pcr_value:.2f})，市场看涨情绪浓厚，考虑反向操作',
                        'sentiment': 'contrarian_bearish',
                        'signal_strength': min(1.5, self.pcr_low_threshold / pcr_value)
                    })
            
            # 期权流量异常信号
            volume_imbalance = flow_data.get('volume_imbalance', 0)
            if volume_imbalance > 0.6:  # 成交量严重不平衡
                if flow_direction == 'bullish':
                    signals.append({
                        'level': 4,
                        'type': 'volume_imbalance',
                        'strategy': 'follow_flow',
                        'reason': f'看涨期权成交量异常放大，成交量不平衡度{volume_imbalance:.2f}',
                        'sentiment': 'flow_bullish',
                        'signal_strength': min(2.0, volume_imbalance)
                    })
                elif flow_direction == 'bearish':
                    signals.append({
                        'level': 4,
                        'type': 'volume_imbalance',
                        'strategy': 'follow_flow',
                        'reason': f'看跌期权成交量异常放大，成交量不平衡度{volume_imbalance:.2f}',
                        'sentiment': 'flow_bearish',
                        'signal_strength': min(2.0, volume_imbalance)
                    })
            
            logger.debug(f"期权情绪信号生成完成，发现 {len(signals)} 个信号")
            return signals
            
        except Exception as e:
            logger.error(f"生成期权情绪信号时出错: {e}")
            return []
    
    def calculate_sentiment_score(self, pcr_data: Dict[str, float], 
                                flow_data: Dict[str, any],
                                skew_data: Dict[str, float]) -> Dict[str, any]:
        """
        计算综合期权情绪得分
        
        Args:
            pcr_data: PCR数据
            flow_data: 期权流量数据
            skew_data: 期权偏度数据
            
        Returns:
            综合情绪得分
        """
        try:
            # PCR得分（0-100，50为中性）
            pcr_value = pcr_data.get('pcr_combined', 1.0)
            if pcr_value > 1.5:
                pcr_score = 20  # 极度看跌
            elif pcr_value > 1.2:
                pcr_score = 35  # 看跌
            elif pcr_value > 0.8:
                pcr_score = 50  # 中性
            elif pcr_value > 0.6:
                pcr_score = 65  # 看涨
            else:
                pcr_score = 80  # 极度看涨
            
            # 流量得分
            volume_ratio = flow_data.get('volume_ratio', 1.0)
            if volume_ratio > 1.5:
                flow_score = 70  # 看涨流量
            elif volume_ratio > 1.2:
                flow_score = 60
            elif volume_ratio > 0.8:
                flow_score = 50  # 中性
            elif volume_ratio > 0.6:
                flow_score = 40
            else:
                flow_score = 30  # 看跌流量
            
            # 偏度得分
            skew = skew_data.get('skew', 0.0)
            if skew > 0.1:
                skew_score = 30  # 强看跌偏度
            elif skew > 0.05:
                skew_score = 40  # 看跌偏度
            elif skew > -0.05:
                skew_score = 50  # 中性
            elif skew > -0.1:
                skew_score = 60  # 看涨偏度
            else:
                skew_score = 70  # 强看涨偏度
            
            # 加权综合得分
            composite_score = (pcr_score * 0.5 + flow_score * 0.3 + skew_score * 0.2)
            
            # 情绪等级
            if composite_score >= 70:
                sentiment_level = 'bullish'
            elif composite_score >= 55:
                sentiment_level = 'slightly_bullish'
            elif composite_score >= 45:
                sentiment_level = 'neutral'
            elif composite_score >= 30:
                sentiment_level = 'slightly_bearish'
            else:
                sentiment_level = 'bearish'
            
            sentiment_analysis = {
                'composite_score': float(composite_score),
                'sentiment_level': sentiment_level,
                'pcr_score': float(pcr_score),
                'flow_score': float(flow_score),
                'skew_score': float(skew_score),
                'confidence': min(100, abs(composite_score - 50) * 2)  # 距离中性越远，置信度越高
            }
            
            logger.debug(f"期权情绪得分: {sentiment_analysis}")
            return sentiment_analysis
            
        except Exception as e:
            logger.error(f"计算期权情绪得分时出错: {e}")
            return {
                'composite_score': 50.0,
                'sentiment_level': 'neutral',
                'pcr_score': 50.0,
                'flow_score': 50.0,
                'skew_score': 50.0,
                'confidence': 0.0
            }


def analyze_option_sentiment(put_volume: float, call_volume: float,
                           put_oi: Optional[float] = None, call_oi: Optional[float] = None,
                           option_data: Optional[pd.DataFrame] = None,
                           iv_data: Optional[Dict[str, List[float]]] = None,
                           pcr_history: Optional[List[float]] = None) -> Dict[str, any]:
    """
    便捷函数：综合期权情绪分析
    
    Args:
        put_volume: 看跌期权成交量
        call_volume: 看涨期权成交量
        put_oi: 看跌期权持仓量（可选）
        call_oi: 看涨期权持仓量（可选）
        option_data: 期权数据DataFrame（可选）
        iv_data: IV数据字典（可选）
        pcr_history: PCR历史数据（可选）
        
    Returns:
        综合期权情绪分析结果
    """
    analyzer = OptionSentimentAnalyzer()
    
    # 计算PCR
    pcr_data = analyzer.calculate_pcr(put_volume, call_volume, put_oi, call_oi)
    
    # PCR趋势分析
    pcr_trend = analyzer.analyze_pcr_trend(pcr_history) if pcr_history else {}
    
    # 期权流量分析
    flow_data = analyzer.calculate_option_flow(option_data) if option_data is not None else {}
    
    # 期权偏度分析
    skew_data = analyzer.analyze_option_skew(iv_data) if iv_data else {}
    
    # 生成信号
    signals = analyzer.generate_sentiment_signals(pcr_data, flow_data, skew_data)
    
    # 综合情绪得分
    sentiment_score = analyzer.calculate_sentiment_score(pcr_data, flow_data, skew_data)
    
    return {
        'pcr_data': pcr_data,
        'pcr_trend': pcr_trend,
        'flow_data': flow_data,
        'skew_data': skew_data,
        'signals': signals,
        'sentiment_score': sentiment_score
    }


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    print("测试期权情绪分析...")
    
    # 创建测试数据
    test_put_volume = 1200
    test_call_volume = 1000
    test_put_oi = 5000
    test_call_oi = 4500
    
    # 创建期权数据
    test_option_data = pd.DataFrame({
        'option_type': ['call', 'call', 'put', 'put', 'call'],
        'volume': [500, 300, 600, 400, 200],
        'strike': [100, 105, 95, 90, 110]
    })
    
    # 创建IV数据
    test_iv_data = {
        '95': [0.25, 0.26, 0.24],
        '100': [0.20, 0.21, 0.19],
        '105': [0.18, 0.19, 0.17]
    }
    
    # 创建PCR历史数据
    test_pcr_history = [1.1, 1.2, 1.3, 1.1, 1.0, 0.9, 1.2]
    
    # 执行分析
    result = analyze_option_sentiment(
        put_volume=test_put_volume,
        call_volume=test_call_volume,
        put_oi=test_put_oi,
        call_oi=test_call_oi,
        option_data=test_option_data,
        iv_data=test_iv_data,
        pcr_history=test_pcr_history
    )
    
    print(f"\nPCR数据: {result['pcr_data']}")
    print(f"\nPCR趋势: {result['pcr_trend']}")
    print(f"\n流量数据: {result['flow_data']}")
    print(f"\n偏度数据: {result['skew_data']}")
    print(f"\n情绪得分: {result['sentiment_score']}")
    print(f"\n信号数量: {len(result['signals'])}")
    
    if result['signals']:
        print("\n生成的信号:")
        for i, signal in enumerate(result['signals'], 1):
            print(f"{i}. {signal['reason']} (等级: {signal['level']})")