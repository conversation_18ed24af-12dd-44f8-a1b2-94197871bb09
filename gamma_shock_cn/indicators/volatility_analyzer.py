#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波动率分析模块

实现波动率相关的分析功能，包括：
- 历史波动率计算
- 波动率百分位排名
- VIX数据处理
- 波动率制度判断
- IV统计分析

作者: AI Assistant
创建时间: 2024-12-11
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class VolatilityAnalyzer:
    """
    波动率分析器
    
    提供全面的波动率分析功能，支持多种波动率指标计算和分析
    """
    
    def __init__(self, trading_days_per_year: int = 252):
        """
        初始化波动率分析器
        
        Args:
            trading_days_per_year: 年交易日数，默认252
        """
        self.trading_days_per_year = trading_days_per_year
        
        # 波动率制度阈值
        self.low_vol_threshold = 0.3  # 低波动率阈值（30分位）
        self.high_vol_threshold = 0.7  # 高波动率阈值（70分位）
        
        logger.info(f"波动率分析器初始化完成 - 年交易日: {trading_days_per_year}")
    
    def calculate_historical_volatility(self, df: pd.DataFrame, period: int = 20) -> pd.DataFrame:
        """
        计算历史波动率
        
        Args:
            df: 包含价格数据的DataFrame
            period: 计算周期，默认20天
            
        Returns:
            添加了历史波动率列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 计算日收益率
            result_df['returns'] = result_df['close'].pct_change()
            
            # 计算滚动标准差
            rolling_std = result_df['returns'].rolling(window=period, min_periods=1).std()
            
            # 年化波动率
            result_df['historical_volatility'] = rolling_std * np.sqrt(self.trading_days_per_year)
            
            # 计算波动率变化
            result_df['vol_change'] = result_df['historical_volatility'].pct_change()
            
            logger.debug(f"历史波动率计算完成，周期: {period}天")
            return result_df
            
        except Exception as e:
            logger.error(f"计算历史波动率时出错: {e}")
            raise
    
    def calculate_volatility_percentile(self, df: pd.DataFrame, lookback: int = 252) -> pd.DataFrame:
        """
        计算波动率百分位排名
        
        Args:
            df: 包含历史波动率的DataFrame
            lookback: 回看周期，默认252天（1年）
            
        Returns:
            添加了波动率百分位列的DataFrame
        """
        try:
            result_df = df.copy()
            
            # 确保历史波动率存在
            if 'historical_volatility' not in result_df.columns:
                result_df = self.calculate_historical_volatility(result_df)
            
            # 计算滚动百分位
            def rolling_percentile(series, window):
                return series.rolling(window=window, min_periods=1).apply(
                    lambda x: (x.iloc[-1] <= x).mean() * 100 if len(x) > 1 else 50.0
                )
            
            result_df['vol_percentile'] = rolling_percentile(
                result_df['historical_volatility'], lookback
            )
            
            # 波动率制度分类
            result_df['vol_regime'] = np.where(
                result_df['vol_percentile'] < self.low_vol_threshold * 100, 'low',
                np.where(
                    result_df['vol_percentile'] > self.high_vol_threshold * 100, 'high',
                    'medium'
                )
            )
            
            logger.debug(f"波动率百分位计算完成，回看周期: {lookback}天")
            return result_df
            
        except Exception as e:
            logger.error(f"计算波动率百分位时出错: {e}")
            raise
    
    def process_vix_data(self, vix_data: pd.DataFrame) -> Dict[str, float]:
        """
        处理VIX数据
        
        Args:
            vix_data: VIX数据DataFrame
            
        Returns:
            VIX统计信息字典
        """
        try:
            if vix_data.empty:
                logger.warning("VIX数据为空")
                return {
                    'current_vix': 0.0,
                    'vix_mean': 0.0,
                    'vix_std': 0.0,
                    'vix_percentile': 50.0,
                    'vix_regime': 'unknown'
                }
            
            # 确保数据按日期排序
            if 'date' in vix_data.columns:
                vix_data = vix_data.sort_values('date')
            
            # 获取VIX值列（可能的列名）
            vix_col = None
            for col in ['vix', 'close', 'value', 'volatility']:
                if col in vix_data.columns:
                    vix_col = col
                    break
            
            if vix_col is None:
                logger.error("未找到VIX值列")
                return {
                    'current_vix': 0.0,
                    'vix_mean': 0.0,
                    'vix_std': 0.0,
                    'vix_percentile': 50.0,
                    'vix_regime': 'unknown'
                }
            
            vix_values = vix_data[vix_col].dropna()
            
            if len(vix_values) == 0:
                logger.warning("VIX数据中无有效值")
                return {
                    'current_vix': 0.0,
                    'vix_mean': 0.0,
                    'vix_std': 0.0,
                    'vix_percentile': 50.0,
                    'vix_regime': 'unknown'
                }
            
            current_vix = float(vix_values.iloc[-1])
            vix_mean = float(vix_values.mean())
            vix_std = float(vix_values.std())
            
            # 计算百分位
            vix_percentile = float((vix_values <= current_vix).mean() * 100)
            
            # 判断波动率制度
            if vix_percentile < 30:
                vix_regime = 'low'
            elif vix_percentile > 70:
                vix_regime = 'high'
            else:
                vix_regime = 'medium'
            
            vix_stats = {
                'current_vix': current_vix,
                'vix_mean': vix_mean,
                'vix_std': vix_std,
                'vix_percentile': vix_percentile,
                'vix_regime': vix_regime,
                'vix_z_score': (current_vix - vix_mean) / vix_std if vix_std > 0 else 0.0
            }
            
            logger.debug(f"VIX数据处理完成: {vix_stats}")
            return vix_stats
            
        except Exception as e:
            logger.error(f"处理VIX数据时出错: {e}")
            return {
                'current_vix': 0.0,
                'vix_mean': 0.0,
                'vix_std': 0.0,
                'vix_percentile': 50.0,
                'vix_regime': 'unknown'
            }
    
    def calculate_iv_statistics(self, iv_data: List[float], period: int = 30) -> Dict[str, float]:
        """
        计算隐含波动率统计数据
        
        Args:
            iv_data: 隐含波动率数据列表
            period: 统计周期，默认30天
            
        Returns:
            IV统计信息字典
        """
        try:
            if not iv_data or len(iv_data) == 0:
                logger.warning("IV数据为空")
                return {
                    'current_iv': 0.0,
                    'iv_mean': 0.0,
                    'iv_std': 0.0,
                    'iv_percentile_25': 0.0,
                    'iv_percentile_50': 0.0,
                    'iv_percentile_75': 0.0,
                    'iv_percentile_90': 0.0,
                    'iv_regime': 'unknown',
                    'iv_z_score': 0.0
                }
            
            # 取最近period天的数据
            recent_data = iv_data[-period:] if len(iv_data) >= period else iv_data
            iv_array = np.array(recent_data)
            current_iv = float(iv_data[-1])
            
            # 基础统计
            iv_mean = float(np.mean(iv_array))
            iv_std = float(np.std(iv_array))
            
            # 百分位统计
            percentiles = {
                'iv_percentile_25': float(np.percentile(iv_array, 25)),
                'iv_percentile_50': float(np.percentile(iv_array, 50)),
                'iv_percentile_75': float(np.percentile(iv_array, 75)),
                'iv_percentile_90': float(np.percentile(iv_array, 90))
            }
            
            # 当前IV在历史数据中的百分位
            current_percentile = float((iv_array <= current_iv).mean() * 100)
            
            # IV制度判断
            if current_percentile < 30:
                iv_regime = 'low'
            elif current_percentile > 70:
                iv_regime = 'high'
            else:
                iv_regime = 'medium'
            
            # Z-score
            iv_z_score = (current_iv - iv_mean) / iv_std if iv_std > 0 else 0.0
            
            iv_stats = {
                'current_iv': current_iv,
                'iv_mean': iv_mean,
                'iv_std': iv_std,
                'iv_regime': iv_regime,
                'iv_z_score': float(iv_z_score),
                'current_percentile': current_percentile,
                **percentiles
            }
            
            logger.debug(f"IV统计计算完成: {iv_stats}")
            return iv_stats
            
        except Exception as e:
            logger.error(f"计算IV统计时出错: {e}")
            return {
                'current_iv': 0.0,
                'iv_mean': 0.0,
                'iv_std': 0.0,
                'iv_percentile_25': 0.0,
                'iv_percentile_50': 0.0,
                'iv_percentile_75': 0.0,
                'iv_percentile_90': 0.0,
                'iv_regime': 'unknown',
                'iv_z_score': 0.0
            }
    
    def detect_volatility_signals(self, df: pd.DataFrame, iv_stats: Dict[str, float]) -> List[Dict[str, any]]:
        """
        基于波动率检测交易信号
        
        Args:
            df: 包含价格和技术指标的DataFrame
            iv_stats: IV统计信息
            
        Returns:
            波动率信号列表
        """
        try:
            signals = []
            
            if df.empty:
                return signals
            
            latest = df.iloc[-1]
            current_iv = iv_stats.get('current_iv', 0.0)
            iv_mean = iv_stats.get('iv_mean', 0.0)
            iv_std = iv_stats.get('iv_std', 0.0)
            iv_regime = iv_stats.get('iv_regime', 'unknown')
            
            # 高波动率策略
            if current_iv > (iv_mean + 1.5 * iv_std):
                # 买入看涨期权条件：EMA8上穿EMA21且突破布林带上轨
                if (latest.get('EMA8', 0) > latest.get('EMA21', 0) and 
                    latest.get('close', 0) > latest.get('BB_UPPER', 0)):
                    signals.append({
                        'level': 5,
                        'type': 'high_iv_call',
                        'strategy': 'buy_call',
                        'reason': 'EMA8上穿EMA21且突破布林带上轨（高波动率）',
                        'iv_regime': 'high',
                        'signal_strength': min(2.0, (current_iv - iv_mean) / iv_std)
                    })
                
                # 买入看跌期权条件：EMA8下穿EMA21且跌破布林带下轨
                if (latest.get('EMA8', 0) < latest.get('EMA21', 0) and 
                    latest.get('close', 0) < latest.get('BB_LOWER', 0)):
                    signals.append({
                        'level': 5,
                        'type': 'high_iv_put',
                        'strategy': 'buy_put',
                        'reason': 'EMA8下穿EMA21且跌破布林带下轨（高波动率）',
                        'iv_regime': 'high',
                        'signal_strength': min(2.0, (current_iv - iv_mean) / iv_std)
                    })
            
            # 低波动率策略
            elif current_iv < (iv_mean - 1.0 * iv_std):
                # 买入看涨期权条件：突破EMA21，成交量增加，MACD柱体转红
                if (latest.get('close', 0) > latest.get('EMA21', 0) and 
                    latest.get('VOL_ABNORMAL', False) and 
                    latest.get('MACD', 0) > 0):
                    signals.append({
                        'level': 5,
                        'type': 'low_iv_call',
                        'strategy': 'buy_call',
                        'reason': '突破EMA21，成交量增加，MACD柱体转红（低波动率）',
                        'iv_regime': 'low',
                        'signal_strength': min(2.0, abs((current_iv - iv_mean) / iv_std))
                    })
                
                # 买入看跌期权条件：跌破EMA21，成交量放大，MACD柱体翻绿
                if (latest.get('close', 0) < latest.get('EMA21', 0) and 
                    latest.get('VOL_ABNORMAL', False) and 
                    latest.get('MACD', 0) < 0):
                    signals.append({
                        'level': 5,
                        'type': 'low_iv_put',
                        'strategy': 'buy_put',
                        'reason': '跌破EMA21，成交量放大，MACD柱体翻绿（低波动率）',
                        'iv_regime': 'low',
                        'signal_strength': min(2.0, abs((current_iv - iv_mean) / iv_std))
                    })
            
            logger.debug(f"波动率信号检测完成，发现 {len(signals)} 个信号")
            return signals
            
        except Exception as e:
            logger.error(f"检测波动率信号时出错: {e}")
            return []
    
    def analyze_volatility_trend(self, df: pd.DataFrame, lookback: int = 10) -> Dict[str, any]:
        """
        分析波动率趋势
        
        Args:
            df: 包含波动率数据的DataFrame
            lookback: 回看周期，默认10天
            
        Returns:
            波动率趋势分析结果
        """
        try:
            if len(df) < lookback + 1:
                logger.warning("数据不足，无法进行波动率趋势分析")
                return {
                    'vol_trend': 'unknown',
                    'trend_strength': 0.0,
                    'vol_acceleration': 0.0,
                    'regime_stability': 'unknown'
                }
            
            recent_data = df.tail(lookback + 1)
            
            # 波动率趋势
            if 'historical_volatility' in recent_data.columns:
                vol_values = recent_data['historical_volatility'].values
                vol_trend = 'up' if vol_values[-1] > vol_values[0] else 'down'
                vol_change = (vol_values[-1] - vol_values[0]) / vol_values[0] if vol_values[0] != 0 else 0
                
                # 波动率加速度（二阶导数）
                vol_diff = np.diff(vol_values)
                vol_acceleration = np.mean(np.diff(vol_diff)) if len(vol_diff) > 1 else 0.0
            else:
                vol_trend = 'unknown'
                vol_change = 0.0
                vol_acceleration = 0.0
            
            # 制度稳定性
            if 'vol_regime' in recent_data.columns:
                regime_changes = (recent_data['vol_regime'] != recent_data['vol_regime'].shift(1)).sum()
                regime_stability = 'stable' if regime_changes <= 1 else 'unstable'
            else:
                regime_stability = 'unknown'
            
            analysis = {
                'vol_trend': vol_trend,
                'trend_strength': abs(vol_change),
                'vol_acceleration': float(vol_acceleration),
                'regime_stability': regime_stability,
                'vol_change_pct': vol_change * 100
            }
            
            logger.debug(f"波动率趋势分析: {analysis}")
            return analysis
            
        except Exception as e:
            logger.error(f"分析波动率趋势时出错: {e}")
            return {
                'vol_trend': 'unknown',
                'trend_strength': 0.0,
                'vol_acceleration': 0.0,
                'regime_stability': 'unknown'
            }


def calculate_volatility_indicators(df: pd.DataFrame, vix_data: Optional[pd.DataFrame] = None, 
                                  iv_data: Optional[List[float]] = None) -> Tuple[pd.DataFrame, Dict[str, any]]:
    """
    便捷函数：计算所有波动率指标
    
    Args:
        df: 包含价格数据的DataFrame
        vix_data: VIX数据DataFrame（可选）
        iv_data: IV数据列表（可选）
        
    Returns:
        (包含波动率指标的DataFrame, 波动率统计信息字典)
    """
    analyzer = VolatilityAnalyzer()
    
    # 计算历史波动率和百分位
    result_df = analyzer.calculate_historical_volatility(df)
    result_df = analyzer.calculate_volatility_percentile(result_df)
    
    # 处理VIX和IV数据
    vix_stats = analyzer.process_vix_data(vix_data) if vix_data is not None else {}
    iv_stats = analyzer.calculate_iv_statistics(iv_data) if iv_data is not None else {}
    
    # 趋势分析
    trend_analysis = analyzer.analyze_volatility_trend(result_df)
    
    stats = {
        'vix_stats': vix_stats,
        'iv_stats': iv_stats,
        'trend_analysis': trend_analysis
    }
    
    return result_df, stats


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 创建测试数据
    np.random.seed(42)
    test_data = {
        'datetime': pd.date_range('2024-01-01', periods=100, freq='D'),
        'close': np.random.randn(100).cumsum() + 100,
        'volume': np.random.randint(1000000, 10000000, 100)
    }
    
    test_df = pd.DataFrame(test_data)
    
    # 创建VIX测试数据
    vix_test_data = {
        'date': pd.date_range('2024-01-01', periods=100, freq='D'),
        'vix': np.random.normal(20, 5, 100)
    }
    vix_df = pd.DataFrame(vix_test_data)
    
    # 创建IV测试数据
    iv_test_data = np.random.normal(0.25, 0.05, 100).tolist()
    
    print("测试波动率分析...")
    
    result_df, stats = calculate_volatility_indicators(test_df, vix_df, iv_test_data)
    
    print(f"计算完成，数据形状: {result_df.shape}")
    print(f"包含列: {list(result_df.columns)}")
    print(f"\nVIX统计: {stats['vix_stats']}")
    print(f"\nIV统计: {stats['iv_stats']}")
    print(f"\n趋势分析: {stats['trend_analysis']}")
    
    print("\n最后5行波动率数据:")
    vol_cols = ['historical_volatility', 'vol_percentile', 'vol_regime']
    print(result_df[vol_cols].tail())