#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号分类器模块

本模块负责对交易信号进行分类、评级和优先级排序，
提供信号过滤和历史记录功能。

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from collections import defaultdict, deque


class SignalType(Enum):
    """信号类型枚举"""
    ENTRY_LONG = "entry_long"          # 多头进场
    ENTRY_SHORT = "entry_short"        # 空头进场
    EXIT_LONG = "exit_long"            # 多头出场
    EXIT_SHORT = "exit_short"          # 空头出场
    WARNING = "warning"                # 预警信号
    WILLIAMS = "williams"              # 威廉信号
    ANOMALY = "anomaly"                # 异常信号
    VOLATILITY = "volatility"          # 波动率信号
    SENTIMENT = "sentiment"            # 情绪信号


class SignalStrength(Enum):
    """信号强度枚举"""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5


class SignalPriority(Enum):
    """信号优先级枚举"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class Signal:
    """交易信号数据类"""
    signal_id: str
    symbol: str
    signal_type: SignalType
    strength: SignalStrength
    priority: SignalPriority
    timestamp: datetime
    price: float
    volume: Optional[float] = None
    indicators: Dict[str, float] = field(default_factory=dict)
    description: str = ""
    confidence: float = 0.0
    source: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """后处理初始化"""
        if not self.signal_id:
            self.signal_id = self._generate_signal_id()
    
    def _generate_signal_id(self) -> str:
        """生成信号ID"""
        timestamp_str = self.timestamp.strftime('%Y%m%d_%H%M%S')
        return f"{self.symbol}_{self.signal_type.value}_{timestamp_str}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'signal_id': self.signal_id,
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'strength': self.strength.value,
            'priority': self.priority.value,
            'timestamp': self.timestamp.isoformat(),
            'price': self.price,
            'volume': self.volume,
            'indicators': self.indicators,
            'description': self.description,
            'confidence': self.confidence,
            'source': self.source,
            'metadata': self.metadata
        }


@dataclass
class SignalFilter:
    """信号过滤器配置"""
    min_strength: SignalStrength = SignalStrength.WEAK
    min_confidence: float = 0.3
    allowed_types: Optional[List[SignalType]] = None
    min_priority: SignalPriority = SignalPriority.LOW
    time_window: Optional[timedelta] = None
    symbols: Optional[List[str]] = None
    max_signals_per_symbol: Optional[int] = None
    require_volume_confirmation: bool = False
    min_volume_ratio: float = 1.0


@dataclass
class ClassificationResult:
    """分类结果数据类"""
    original_signal: Signal
    classified_signal: Signal
    classification_score: float
    classification_reasons: List[str]
    adjustments_made: List[str]
    timestamp: datetime = field(default_factory=datetime.now)


class SignalClassifier:
    """信号分类器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 信号历史记录
        self.signal_history: deque = deque(maxlen=1000)
        self.classification_history: deque = deque(maxlen=1000)
        
        # 统计信息
        self.stats = {
            'total_signals': 0,
            'classified_signals': 0,
            'filtered_signals': 0,
            'by_type': defaultdict(int),
            'by_strength': defaultdict(int),
            'by_priority': defaultdict(int)
        }
        
        # 分类权重配置
        self.classification_weights = self._initialize_classification_weights()
        
        # 信号相关性分析
        self.correlation_matrix = {}
        self.signal_patterns = {}
    
    def _initialize_classification_weights(self) -> Dict[str, Dict[str, float]]:
        """初始化分类权重"""
        return {
            'strength_factors': {
                'volume_confirmation': 0.25,
                'multiple_indicators': 0.20,
                'trend_alignment': 0.20,
                'support_resistance': 0.15,
                'momentum': 0.10,
                'volatility': 0.10
            },
            'priority_factors': {
                'signal_urgency': 0.30,
                'market_conditions': 0.25,
                'risk_level': 0.20,
                'profit_potential': 0.15,
                'time_sensitivity': 0.10
            },
            'confidence_factors': {
                'indicator_consensus': 0.30,
                'historical_accuracy': 0.25,
                'market_regime': 0.20,
                'data_quality': 0.15,
                'signal_clarity': 0.10
            }
        }
    
    def classify_signal(self, signal: Signal) -> ClassificationResult:
        """对信号进行分类和评级"""
        try:
            self.stats['total_signals'] += 1
            
            # 创建分类后的信号副本
            classified_signal = self._create_signal_copy(signal)
            
            # 分类过程
            classification_reasons = []
            adjustments_made = []
            
            # 1. 强度分类
            new_strength, strength_reasons = self._classify_strength(signal)
            if new_strength != signal.strength:
                classified_signal.strength = new_strength
                adjustments_made.append(f"强度调整: {signal.strength.name} -> {new_strength.name}")
            classification_reasons.extend(strength_reasons)
            
            # 2. 优先级分类
            new_priority, priority_reasons = self._classify_priority(signal)
            if new_priority != signal.priority:
                classified_signal.priority = new_priority
                adjustments_made.append(f"优先级调整: {signal.priority.name} -> {new_priority.name}")
            classification_reasons.extend(priority_reasons)
            
            # 3. 置信度计算
            new_confidence, confidence_reasons = self._calculate_confidence(signal)
            if abs(new_confidence - signal.confidence) > 0.05:
                classified_signal.confidence = new_confidence
                adjustments_made.append(f"置信度调整: {signal.confidence:.2f} -> {new_confidence:.2f}")
            classification_reasons.extend(confidence_reasons)
            
            # 4. 信号类型验证和调整
            verified_type, type_reasons = self._verify_signal_type(signal)
            if verified_type != signal.signal_type:
                classified_signal.signal_type = verified_type
                adjustments_made.append(f"类型调整: {signal.signal_type.name} -> {verified_type.name}")
            classification_reasons.extend(type_reasons)
            
            # 5. 计算分类分数
            classification_score = self._calculate_classification_score(
                signal, classified_signal
            )
            
            # 创建分类结果
            result = ClassificationResult(
                original_signal=signal,
                classified_signal=classified_signal,
                classification_score=classification_score,
                classification_reasons=classification_reasons,
                adjustments_made=adjustments_made
            )
            
            # 记录历史
            self.classification_history.append(result)
            self.stats['classified_signals'] += 1
            self._update_stats(classified_signal)
            
            self.logger.info(
                f"信号分类完成: {signal.signal_id}, "
                f"分数: {classification_score:.2f}, "
                f"调整: {len(adjustments_made)}项"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"信号分类失败: {e}")
            # 返回原始信号作为分类结果
            return ClassificationResult(
                original_signal=signal,
                classified_signal=signal,
                classification_score=0.0,
                classification_reasons=[f"分类失败: {str(e)}"],
                adjustments_made=[]
            )
    
    def _classify_strength(self, signal: Signal) -> Tuple[SignalStrength, List[str]]:
        """分类信号强度"""
        reasons = []
        score = 0.0
        weights = self.classification_weights['strength_factors']
        
        # 成交量确认
        if signal.volume and signal.metadata.get('avg_volume'):
            volume_ratio = signal.volume / signal.metadata['avg_volume']
            if volume_ratio > 2.0:
                score += weights['volume_confirmation'] * 1.0
                reasons.append(f"成交量放大{volume_ratio:.1f}倍")
            elif volume_ratio > 1.5:
                score += weights['volume_confirmation'] * 0.7
                reasons.append(f"成交量增加{volume_ratio:.1f}倍")
            elif volume_ratio < 0.5:
                score += weights['volume_confirmation'] * -0.5
                reasons.append("成交量萎缩")
        
        # 多指标确认
        indicator_count = len([v for v in signal.indicators.values() if abs(v) > 0.5])
        if indicator_count >= 3:
            score += weights['multiple_indicators'] * 1.0
            reasons.append(f"{indicator_count}个指标确认")
        elif indicator_count >= 2:
            score += weights['multiple_indicators'] * 0.6
            reasons.append(f"{indicator_count}个指标确认")
        
        # 趋势一致性
        trend_score = signal.metadata.get('trend_alignment', 0.0)
        if trend_score > 0.7:
            score += weights['trend_alignment'] * 1.0
            reasons.append("趋势高度一致")
        elif trend_score > 0.4:
            score += weights['trend_alignment'] * 0.6
            reasons.append("趋势基本一致")
        
        # 支撑阻力位
        if signal.metadata.get('near_support_resistance'):
            score += weights['support_resistance'] * 0.8
            reasons.append("接近关键支撑/阻力位")
        
        # 动量指标
        momentum = signal.indicators.get('momentum', 0.0)
        if abs(momentum) > 0.8:
            score += weights['momentum'] * 1.0
            reasons.append(f"动量强劲({momentum:.2f})")
        elif abs(momentum) > 0.5:
            score += weights['momentum'] * 0.6
            reasons.append(f"动量适中({momentum:.2f})")
        
        # 波动率环境
        volatility = signal.metadata.get('volatility_regime', 'normal')
        if volatility == 'high':
            score += weights['volatility'] * 0.8
            reasons.append("高波动率环境")
        elif volatility == 'low':
            score += weights['volatility'] * 0.4
            reasons.append("低波动率环境")
        
        # 转换为强度等级
        if score >= 0.8:
            strength = SignalStrength.VERY_STRONG
        elif score >= 0.6:
            strength = SignalStrength.STRONG
        elif score >= 0.4:
            strength = SignalStrength.MODERATE
        elif score >= 0.2:
            strength = SignalStrength.WEAK
        else:
            strength = SignalStrength.VERY_WEAK
        
        return strength, reasons
    
    def _classify_priority(self, signal: Signal) -> Tuple[SignalPriority, List[str]]:
        """分类信号优先级"""
        reasons = []
        score = 0.0
        weights = self.classification_weights['priority_factors']
        
        # 信号紧急程度
        if signal.signal_type in [SignalType.ANOMALY, SignalType.WARNING]:
            score += weights['signal_urgency'] * 1.0
            reasons.append("异常/预警信号")
        elif signal.signal_type in [SignalType.ENTRY_LONG, SignalType.ENTRY_SHORT]:
            score += weights['signal_urgency'] * 0.8
            reasons.append("进场信号")
        
        # 市场条件
        market_condition = signal.metadata.get('market_condition', 'normal')
        if market_condition == 'volatile':
            score += weights['market_conditions'] * 0.9
            reasons.append("市场波动剧烈")
        elif market_condition == 'trending':
            score += weights['market_conditions'] * 0.7
            reasons.append("市场趋势明确")
        
        # 风险水平
        risk_level = signal.metadata.get('risk_level', 0.5)
        if risk_level > 0.8:
            score += weights['risk_level'] * 1.0
            reasons.append("高风险信号")
        elif risk_level > 0.6:
            score += weights['risk_level'] * 0.7
            reasons.append("中等风险信号")
        
        # 盈利潜力
        profit_potential = signal.metadata.get('profit_potential', 0.5)
        if profit_potential > 0.8:
            score += weights['profit_potential'] * 1.0
            reasons.append("高盈利潜力")
        elif profit_potential > 0.6:
            score += weights['profit_potential'] * 0.7
            reasons.append("中等盈利潜力")
        
        # 时间敏感性
        time_sensitivity = signal.metadata.get('time_sensitivity', 0.5)
        if time_sensitivity > 0.8:
            score += weights['time_sensitivity'] * 1.0
            reasons.append("时间敏感")
        
        # 转换为优先级等级
        if score >= 0.8:
            priority = SignalPriority.CRITICAL
        elif score >= 0.6:
            priority = SignalPriority.URGENT
        elif score >= 0.4:
            priority = SignalPriority.HIGH
        elif score >= 0.2:
            priority = SignalPriority.MEDIUM
        else:
            priority = SignalPriority.LOW
        
        return priority, reasons
    
    def _calculate_confidence(self, signal: Signal) -> Tuple[float, List[str]]:
        """计算信号置信度"""
        reasons = []
        score = 0.0
        weights = self.classification_weights['confidence_factors']
        
        # 指标一致性
        indicator_values = list(signal.indicators.values())
        if indicator_values:
            consistency = 1.0 - np.std(indicator_values) / (np.mean(np.abs(indicator_values)) + 1e-6)
            score += weights['indicator_consensus'] * consistency
            reasons.append(f"指标一致性: {consistency:.2f}")
        
        # 历史准确率
        historical_accuracy = self._get_historical_accuracy(signal)
        score += weights['historical_accuracy'] * historical_accuracy
        reasons.append(f"历史准确率: {historical_accuracy:.2f}")
        
        # 市场制度
        market_regime = signal.metadata.get('market_regime_score', 0.5)
        score += weights['market_regime'] * market_regime
        reasons.append(f"市场制度适配: {market_regime:.2f}")
        
        # 数据质量
        data_quality = signal.metadata.get('data_quality', 0.8)
        score += weights['data_quality'] * data_quality
        reasons.append(f"数据质量: {data_quality:.2f}")
        
        # 信号清晰度
        signal_clarity = min(signal.strength.value / 5.0, 1.0)
        score += weights['signal_clarity'] * signal_clarity
        reasons.append(f"信号清晰度: {signal_clarity:.2f}")
        
        return min(max(score, 0.0), 1.0), reasons
    
    def _verify_signal_type(self, signal: Signal) -> Tuple[SignalType, List[str]]:
        """验证和调整信号类型"""
        reasons = []
        
        # 基于指标值验证信号类型
        macd = signal.indicators.get('macd', 0.0)
        rsi = signal.indicators.get('rsi', 50.0)
        williams = signal.indicators.get('williams', 0.0)
        
        # 威廉指标信号检测
        if abs(williams) > 0.8:
            if signal.signal_type != SignalType.WILLIAMS:
                reasons.append(f"威廉指标强信号({williams:.2f})")
                return SignalType.WILLIAMS, reasons
        
        # 异常信号检测
        if signal.metadata.get('is_anomaly', False):
            if signal.signal_type != SignalType.ANOMALY:
                reasons.append("检测到异常模式")
                return SignalType.ANOMALY, reasons
        
        # 超买超卖检测
        if rsi > 80 and signal.signal_type == SignalType.ENTRY_LONG:
            reasons.append(f"RSI超买({rsi:.1f})，建议谨慎")
            return SignalType.WARNING, reasons
        elif rsi < 20 and signal.signal_type == SignalType.ENTRY_SHORT:
            reasons.append(f"RSI超卖({rsi:.1f})，建议谨慎")
            return SignalType.WARNING, reasons
        
        reasons.append("信号类型验证通过")
        return signal.signal_type, reasons
    
    def _calculate_classification_score(self, original: Signal, classified: Signal) -> float:
        """计算分类质量分数"""
        score = 0.0
        
        # 强度调整合理性
        strength_diff = abs(classified.strength.value - original.strength.value)
        score += max(0, 1.0 - strength_diff / 4.0) * 0.3
        
        # 优先级调整合理性
        priority_diff = abs(classified.priority.value - original.priority.value)
        score += max(0, 1.0 - priority_diff / 4.0) * 0.3
        
        # 置信度改进
        confidence_improvement = classified.confidence - original.confidence
        score += min(max(confidence_improvement, -0.2), 0.2) * 0.2 + 0.1
        
        # 信号类型一致性
        if classified.signal_type == original.signal_type:
            score += 0.2
        
        return min(max(score, 0.0), 1.0)
    
    def _get_historical_accuracy(self, signal: Signal) -> float:
        """获取历史准确率"""
        # 简化实现，实际应该基于历史数据计算
        signal_type_accuracy = {
            SignalType.ENTRY_LONG: 0.65,
            SignalType.ENTRY_SHORT: 0.60,
            SignalType.EXIT_LONG: 0.70,
            SignalType.EXIT_SHORT: 0.70,
            SignalType.WARNING: 0.75,
            SignalType.WILLIAMS: 0.68,
            SignalType.ANOMALY: 0.55,
            SignalType.VOLATILITY: 0.62,
            SignalType.SENTIMENT: 0.58
        }
        
        base_accuracy = signal_type_accuracy.get(signal.signal_type, 0.6)
        
        # 根据信号强度调整
        strength_bonus = (signal.strength.value - 3) * 0.05
        
        return min(max(base_accuracy + strength_bonus, 0.3), 0.9)
    
    def _create_signal_copy(self, signal: Signal) -> Signal:
        """创建信号副本"""
        return Signal(
            signal_id=signal.signal_id,
            symbol=signal.symbol,
            signal_type=signal.signal_type,
            strength=signal.strength,
            priority=signal.priority,
            timestamp=signal.timestamp,
            price=signal.price,
            volume=signal.volume,
            indicators=signal.indicators.copy(),
            description=signal.description,
            confidence=signal.confidence,
            source=signal.source,
            metadata=signal.metadata.copy()
        )
    
    def _update_stats(self, signal: Signal):
        """更新统计信息"""
        self.stats['by_type'][signal.signal_type.value] += 1
        self.stats['by_strength'][signal.strength.value] += 1
        self.stats['by_priority'][signal.priority.value] += 1
    
    def filter_signals(self, signals: List[Signal], 
                      filter_config: SignalFilter) -> List[Signal]:
        """过滤信号"""
        filtered_signals = []
        
        for signal in signals:
            if self._should_filter_signal(signal, filter_config):
                self.stats['filtered_signals'] += 1
                continue
            
            filtered_signals.append(signal)
        
        return filtered_signals
    
    def _should_filter_signal(self, signal: Signal, filter_config: SignalFilter) -> bool:
        """判断是否应该过滤信号"""
        # 强度过滤
        if signal.strength.value < filter_config.min_strength.value:
            return True
        
        # 置信度过滤
        if signal.confidence < filter_config.min_confidence:
            return True
        
        # 类型过滤
        if (filter_config.allowed_types and 
            signal.signal_type not in filter_config.allowed_types):
            return True
        
        # 优先级过滤
        if signal.priority.value < filter_config.min_priority.value:
            return True
        
        # 时间窗口过滤
        if filter_config.time_window:
            cutoff_time = datetime.now() - filter_config.time_window
            if signal.timestamp < cutoff_time:
                return True
        
        # 标的过滤
        if (filter_config.symbols and 
            signal.symbol not in filter_config.symbols):
            return True
        
        # 成交量确认过滤
        if filter_config.require_volume_confirmation:
            if not signal.volume or not signal.metadata.get('avg_volume'):
                return True
            volume_ratio = signal.volume / signal.metadata['avg_volume']
            if volume_ratio < filter_config.min_volume_ratio:
                return True
        
        return False
    
    def sort_signals_by_priority(self, signals: List[Signal]) -> List[Signal]:
        """按优先级排序信号"""
        return sorted(signals, key=lambda s: (
            -s.priority.value,  # 优先级降序
            -s.strength.value,  # 强度降序
            -s.confidence,      # 置信度降序
            s.timestamp         # 时间升序
        ))
    
    def get_signal_statistics(self) -> Dict[str, Any]:
        """获取信号统计信息"""
        return {
            'total_signals': self.stats['total_signals'],
            'classified_signals': self.stats['classified_signals'],
            'filtered_signals': self.stats['filtered_signals'],
            'classification_rate': (
                self.stats['classified_signals'] / max(self.stats['total_signals'], 1)
            ),
            'filter_rate': (
                self.stats['filtered_signals'] / max(self.stats['total_signals'], 1)
            ),
            'by_type': dict(self.stats['by_type']),
            'by_strength': dict(self.stats['by_strength']),
            'by_priority': dict(self.stats['by_priority']),
            'recent_classifications': len(self.classification_history)
        }
    
    def get_recent_classifications(self, limit: int = 10) -> List[ClassificationResult]:
        """获取最近的分类结果"""
        return list(self.classification_history)[-limit:]
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_signals': 0,
            'classified_signals': 0,
            'filtered_signals': 0,
            'by_type': defaultdict(int),
            'by_strength': defaultdict(int),
            'by_priority': defaultdict(int)
        }
        self.signal_history.clear()
        self.classification_history.clear()


if __name__ == '__main__':
    # 测试代码
    import random
    
    # 创建分类器
    classifier = SignalClassifier()
    
    # 创建测试信号
    test_signals = []
    for i in range(5):
        signal = Signal(
            signal_id=f"test_{i}",
            symbol="510050",
            signal_type=random.choice(list(SignalType)),
            strength=random.choice(list(SignalStrength)),
            priority=random.choice(list(SignalPriority)),
            timestamp=datetime.now(),
            price=2.85 + random.uniform(-0.1, 0.1),
            volume=1000000 * random.uniform(0.5, 2.0),
            indicators={
                'macd': random.uniform(-0.1, 0.1),
                'rsi': random.uniform(30, 70),
                'williams': random.uniform(-1, 1)
            },
            confidence=random.uniform(0.3, 0.9),
            metadata={
                'avg_volume': 1000000,
                'trend_alignment': random.uniform(0.3, 0.9),
                'volatility_regime': random.choice(['low', 'normal', 'high'])
            }
        )
        test_signals.append(signal)
    
    print("=== 信号分类测试 ===")
    for signal in test_signals:
        result = classifier.classify_signal(signal)
        print(f"\n信号: {signal.signal_id}")
        print(f"原始: {signal.signal_type.name}, {signal.strength.name}, {signal.confidence:.2f}")
        print(f"分类: {result.classified_signal.signal_type.name}, {result.classified_signal.strength.name}, {result.classified_signal.confidence:.2f}")
        print(f"分数: {result.classification_score:.2f}")
        print(f"调整: {len(result.adjustments_made)}项")
    
    # 过滤测试
    print("\n=== 信号过滤测试 ===")
    filter_config = SignalFilter(
        min_strength=SignalStrength.MODERATE,
        min_confidence=0.5,
        min_priority=SignalPriority.MEDIUM
    )
    
    classified_signals = [r.classified_signal for r in 
                         [classifier.classify_signal(s) for s in test_signals]]
    filtered_signals = classifier.filter_signals(classified_signals, filter_config)
    
    print(f"原始信号: {len(test_signals)}")
    print(f"过滤后: {len(filtered_signals)}")
    
    # 排序测试
    sorted_signals = classifier.sort_signals_by_priority(filtered_signals)
    print("\n=== 优先级排序 ===")
    for signal in sorted_signals:
        print(f"{signal.signal_id}: {signal.priority.name}, {signal.strength.name}, {signal.confidence:.2f}")
    
    # 统计信息
    print("\n=== 统计信息 ===")
    stats = classifier.get_signal_statistics()
    for key, value in stats.items():
        print(f"{key}: {value}")