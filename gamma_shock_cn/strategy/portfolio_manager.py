#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
投资组合管理器模块

本模块负责投资组合的构建、优化、风险管理和绩效评估，
提供资产配置、仓位管理和风险控制功能。

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from collections import defaultdict, deque
import json

# 尝试导入可选依赖
try:
    from scipy.optimize import minimize
    from scipy import stats
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

try:
    from sklearn.covariance import LedoitWolf
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class AssetType(Enum):
    """资产类型枚举"""
    STOCK = "stock"                    # 股票
    ETF = "etf"                        # ETF
    OPTION = "option"                  # 期权
    FUTURE = "future"                  # 期货
    BOND = "bond"                      # 债券
    CASH = "cash"                      # 现金
    COMMODITY = "commodity"            # 商品
    CRYPTO = "crypto"                  # 加密货币


class PositionType(Enum):
    """仓位类型枚举"""
    LONG = "long"                      # 多头
    SHORT = "short"                    # 空头
    NEUTRAL = "neutral"                # 中性


class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = 1
    LOW = 2
    MODERATE = 3
    HIGH = 4
    VERY_HIGH = 5


class RebalanceFrequency(Enum):
    """再平衡频率枚举"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ANNUALLY = "annually"
    MANUAL = "manual"


@dataclass
class Asset:
    """资产数据类"""
    symbol: str
    name: str
    asset_type: AssetType
    sector: Optional[str] = None
    currency: str = "CNY"
    multiplier: float = 1.0
    min_lot_size: float = 1.0
    tick_size: float = 0.01
    margin_rate: float = 1.0
    commission_rate: float = 0.0003
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """后处理初始化"""
        if not self.name:
            self.name = self.symbol


@dataclass
class Position:
    """持仓数据类"""
    asset: Asset
    quantity: float
    avg_price: float
    current_price: float
    position_type: PositionType
    entry_time: datetime
    last_update: datetime = field(default_factory=datetime.now)
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    total_commission: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """后处理初始化"""
        self.update_pnl()
    
    @property
    def market_value(self) -> float:
        """市值"""
        return abs(self.quantity) * self.current_price * self.asset.multiplier
    
    @property
    def cost_basis(self) -> float:
        """成本基础"""
        return abs(self.quantity) * self.avg_price * self.asset.multiplier
    
    @property
    def weight(self) -> float:
        """权重（需要组合总值计算）"""
        return self.metadata.get('weight', 0.0)
    
    def update_price(self, new_price: float):
        """更新价格"""
        self.current_price = new_price
        self.last_update = datetime.now()
        self.update_pnl()
    
    def update_pnl(self):
        """更新盈亏"""
        price_diff = self.current_price - self.avg_price
        if self.position_type == PositionType.LONG:
            self.unrealized_pnl = self.quantity * price_diff * self.asset.multiplier
        elif self.position_type == PositionType.SHORT:
            self.unrealized_pnl = -self.quantity * price_diff * self.asset.multiplier
        else:
            self.unrealized_pnl = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'symbol': self.asset.symbol,
            'asset_type': self.asset.asset_type.value,
            'quantity': self.quantity,
            'avg_price': self.avg_price,
            'current_price': self.current_price,
            'position_type': self.position_type.value,
            'market_value': self.market_value,
            'cost_basis': self.cost_basis,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'total_commission': self.total_commission,
            'entry_time': self.entry_time.isoformat(),
            'last_update': self.last_update.isoformat(),
            'metadata': self.metadata
        }


@dataclass
class Trade:
    """交易记录数据类"""
    trade_id: str
    asset: Asset
    quantity: float
    price: float
    trade_type: str  # 'buy', 'sell', 'short', 'cover'
    timestamp: datetime
    commission: float = 0.0
    slippage: float = 0.0
    order_id: Optional[str] = None
    strategy: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def notional_value(self) -> float:
        """名义价值"""
        return abs(self.quantity) * self.price * self.asset.multiplier
    
    @property
    def total_cost(self) -> float:
        """总成本（包含佣金和滑点）"""
        return self.notional_value + self.commission + abs(self.slippage)


@dataclass
class PortfolioConstraints:
    """投资组合约束条件"""
    max_position_size: float = 0.1          # 单个资产最大权重
    max_sector_exposure: float = 0.3        # 单个行业最大敞口
    max_leverage: float = 1.0               # 最大杠杆
    min_cash_ratio: float = 0.05            # 最小现金比例
    max_turnover: float = 0.5               # 最大换手率
    max_tracking_error: float = 0.05        # 最大跟踪误差
    risk_budget: Dict[str, float] = field(default_factory=dict)
    forbidden_assets: List[str] = field(default_factory=list)
    required_assets: List[str] = field(default_factory=list)


@dataclass
class PerformanceMetrics:
    """绩效指标数据类"""
    total_return: float = 0.0
    annualized_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    calmar_ratio: float = 0.0
    sortino_ratio: float = 0.0
    information_ratio: float = 0.0
    beta: float = 1.0
    alpha: float = 0.0
    var_95: float = 0.0
    cvar_95: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 1.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    trade_count: int = 0
    turnover: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典"""
        return {
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'calmar_ratio': self.calmar_ratio,
            'sortino_ratio': self.sortino_ratio,
            'information_ratio': self.information_ratio,
            'beta': self.beta,
            'alpha': self.alpha,
            'var_95': self.var_95,
            'cvar_95': self.cvar_95,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'trade_count': self.trade_count,
            'turnover': self.turnover
        }


class PortfolioManager:
    """投资组合管理器"""
    
    def __init__(self, initial_capital: float = 1000000.0, 
                 config: Optional[Dict[str, Any]] = None):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 持仓管理
        self.positions: Dict[str, Position] = {}
        self.assets: Dict[str, Asset] = {}
        
        # 交易记录
        self.trades: List[Trade] = []
        self.trade_history: deque = deque(maxlen=10000)
        
        # 净值历史
        self.nav_history: List[Tuple[datetime, float]] = []
        self.benchmark_history: List[Tuple[datetime, float]] = []
        
        # 约束条件
        self.constraints = PortfolioConstraints()
        
        # 风险管理
        self.risk_metrics = {}
        self.correlation_matrix = pd.DataFrame()
        self.covariance_matrix = pd.DataFrame()
        
        # 再平衡设置
        self.rebalance_frequency = RebalanceFrequency.MONTHLY
        self.last_rebalance = datetime.now()
        self.target_weights: Dict[str, float] = {}
        
        # 绩效跟踪
        self.performance_metrics = PerformanceMetrics()
        self.benchmark_symbol = "000300.SH"  # 沪深300作为基准
        
        # 现金账户
        self._initialize_cash_account()
    
    def _initialize_cash_account(self):
        """初始化现金账户"""
        cash_asset = Asset(
            symbol="CASH",
            name="现金",
            asset_type=AssetType.CASH,
            currency="CNY"
        )
        self.assets["CASH"] = cash_asset
        
        cash_position = Position(
            asset=cash_asset,
            quantity=self.initial_capital,
            avg_price=1.0,
            current_price=1.0,
            position_type=PositionType.LONG,
            entry_time=datetime.now()
        )
        self.positions["CASH"] = cash_position
    
    def add_asset(self, asset: Asset):
        """添加资产"""
        self.assets[asset.symbol] = asset
        self.logger.info(f"添加资产: {asset.symbol} ({asset.name})")
    
    def update_prices(self, price_data: Dict[str, float]):
        """更新价格数据"""
        for symbol, price in price_data.items():
            if symbol in self.positions:
                self.positions[symbol].update_price(price)
        
        # 更新净值历史
        current_nav = self.get_total_value()
        self.nav_history.append((datetime.now(), current_nav))
        
        # 限制历史记录长度
        if len(self.nav_history) > 10000:
            self.nav_history = self.nav_history[-5000:]
    
    def execute_trade(self, symbol: str, quantity: float, price: float, 
                     trade_type: str, strategy: Optional[str] = None) -> bool:
        """执行交易"""
        try:
            if symbol not in self.assets:
                self.logger.error(f"未知资产: {symbol}")
                return False
            
            asset = self.assets[symbol]
            
            # 计算佣金和滑点
            commission = abs(quantity) * price * asset.commission_rate
            slippage = abs(quantity) * price * 0.0001  # 简化滑点计算
            
            # 检查资金充足性
            if not self._check_sufficient_funds(symbol, quantity, price, commission):
                self.logger.error(f"资金不足，无法执行交易: {symbol}")
                return False
            
            # 创建交易记录
            trade = Trade(
                trade_id=self._generate_trade_id(),
                asset=asset,
                quantity=quantity,
                price=price,
                trade_type=trade_type,
                timestamp=datetime.now(),
                commission=commission,
                slippage=slippage,
                strategy=strategy
            )
            
            # 更新持仓
            self._update_position(trade)
            
            # 更新现金
            self._update_cash(trade)
            
            # 记录交易
            self.trades.append(trade)
            self.trade_history.append(trade)
            
            self.logger.info(
                f"交易执行成功: {trade_type} {quantity} {symbol} @ {price}"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")
            return False
    
    def _check_sufficient_funds(self, symbol: str, quantity: float, 
                               price: float, commission: float) -> bool:
        """检查资金充足性"""
        if quantity > 0:  # 买入
            required_cash = quantity * price + commission
            available_cash = self.positions["CASH"].quantity
            return available_cash >= required_cash
        else:  # 卖出
            if symbol in self.positions:
                available_quantity = self.positions[symbol].quantity
                return available_quantity >= abs(quantity)
            return False
    
    def _update_position(self, trade: Trade):
        """更新持仓"""
        symbol = trade.asset.symbol
        
        if symbol not in self.positions:
            # 新建持仓
            position_type = PositionType.LONG if trade.quantity > 0 else PositionType.SHORT
            self.positions[symbol] = Position(
                asset=trade.asset,
                quantity=trade.quantity,
                avg_price=trade.price,
                current_price=trade.price,
                position_type=position_type,
                entry_time=trade.timestamp
            )
        else:
            # 更新现有持仓
            position = self.positions[symbol]
            old_quantity = position.quantity
            new_quantity = old_quantity + trade.quantity
            
            if new_quantity == 0:
                # 平仓
                position.realized_pnl += (
                    trade.price - position.avg_price
                ) * abs(trade.quantity) * trade.asset.multiplier
                del self.positions[symbol]
            elif (old_quantity > 0 and new_quantity > 0) or (old_quantity < 0 and new_quantity < 0):
                # 加仓
                total_cost = (old_quantity * position.avg_price + 
                             trade.quantity * trade.price)
                position.avg_price = total_cost / new_quantity
                position.quantity = new_quantity
            else:
                # 减仓或反向
                if abs(new_quantity) < abs(old_quantity):
                    # 部分平仓
                    closed_quantity = abs(trade.quantity)
                    position.realized_pnl += (
                        trade.price - position.avg_price
                    ) * closed_quantity * trade.asset.multiplier
                    position.quantity = new_quantity
                else:
                    # 反向开仓
                    position.realized_pnl += (
                        trade.price - position.avg_price
                    ) * abs(old_quantity) * trade.asset.multiplier
                    position.quantity = new_quantity
                    position.avg_price = trade.price
                    position.position_type = (
                        PositionType.LONG if new_quantity > 0 else PositionType.SHORT
                    )
            
            if symbol in self.positions:
                self.positions[symbol].total_commission += trade.commission
    
    def _update_cash(self, trade: Trade):
        """更新现金持仓"""
        cash_change = -(trade.quantity * trade.price + trade.commission)
        self.positions["CASH"].quantity += cash_change
    
    def _generate_trade_id(self) -> str:
        """生成交易ID"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        return f"T_{timestamp}"
    
    def get_total_value(self) -> float:
        """获取组合总价值"""
        total_value = 0.0
        for position in self.positions.values():
            total_value += position.market_value
        return total_value
    
    def get_position_weights(self) -> Dict[str, float]:
        """获取持仓权重"""
        total_value = self.get_total_value()
        if total_value == 0:
            return {}
        
        weights = {}
        for symbol, position in self.positions.items():
            weights[symbol] = position.market_value / total_value
            position.metadata['weight'] = weights[symbol]
        
        return weights
    
    def get_sector_exposure(self) -> Dict[str, float]:
        """获取行业敞口"""
        sector_exposure = defaultdict(float)
        total_value = self.get_total_value()
        
        if total_value == 0:
            return dict(sector_exposure)
        
        for position in self.positions.values():
            if position.asset.sector:
                sector_exposure[position.asset.sector] += (
                    position.market_value / total_value
                )
        
        return dict(sector_exposure)
    
    def calculate_risk_metrics(self) -> Dict[str, float]:
        """计算风险指标"""
        if len(self.nav_history) < 2:
            return {}
        
        # 提取净值序列
        nav_series = pd.Series(
            [nav for _, nav in self.nav_history],
            index=[dt for dt, _ in self.nav_history]
        )
        
        # 计算收益率
        returns = nav_series.pct_change().dropna()
        
        if len(returns) == 0:
            return {}
        
        risk_metrics = {}
        
        # 基础风险指标
        risk_metrics['volatility'] = returns.std() * np.sqrt(252)
        risk_metrics['downside_volatility'] = (
            returns[returns < 0].std() * np.sqrt(252)
        )
        
        # VaR和CVaR
        if len(returns) >= 20:
            risk_metrics['var_95'] = np.percentile(returns, 5)
            risk_metrics['cvar_95'] = returns[returns <= risk_metrics['var_95']].mean()
        
        # 最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        risk_metrics['max_drawdown'] = drawdown.min()
        
        # 贝塔值（如果有基准数据）
        if len(self.benchmark_history) > 0:
            benchmark_series = pd.Series(
                [nav for _, nav in self.benchmark_history],
                index=[dt for dt, _ in self.benchmark_history]
            )
            benchmark_returns = benchmark_series.pct_change().dropna()
            
            # 对齐时间序列
            aligned_returns = returns.reindex(benchmark_returns.index).dropna()
            aligned_benchmark = benchmark_returns.reindex(aligned_returns.index).dropna()
            
            if len(aligned_returns) > 10 and len(aligned_benchmark) > 10:
                if SCIPY_AVAILABLE:
                    beta, alpha, r_value, p_value, std_err = stats.linregress(
                        aligned_benchmark, aligned_returns
                    )
                    risk_metrics['beta'] = beta
                    risk_metrics['alpha'] = alpha * 252  # 年化
                    risk_metrics['correlation'] = r_value
                else:
                    # 简化计算
                    covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
                    benchmark_variance = np.var(aligned_benchmark)
                    risk_metrics['beta'] = covariance / benchmark_variance if benchmark_variance > 0 else 1.0
        
        self.risk_metrics = risk_metrics
        return risk_metrics
    
    def calculate_performance_metrics(self, 
                                    benchmark_returns: Optional[pd.Series] = None) -> PerformanceMetrics:
        """计算绩效指标"""
        if len(self.nav_history) < 2:
            return self.performance_metrics
        
        # 提取净值序列
        nav_series = pd.Series(
            [nav for _, nav in self.nav_history],
            index=[dt for dt, _ in self.nav_history]
        )
        
        # 计算收益率
        returns = nav_series.pct_change().dropna()
        
        if len(returns) == 0:
            return self.performance_metrics
        
        metrics = PerformanceMetrics()
        
        # 总收益率
        metrics.total_return = (nav_series.iloc[-1] / nav_series.iloc[0]) - 1
        
        # 年化收益率
        days = (nav_series.index[-1] - nav_series.index[0]).days
        if days > 0:
            metrics.annualized_return = (1 + metrics.total_return) ** (365.25 / days) - 1
        
        # 波动率
        metrics.volatility = returns.std() * np.sqrt(252)
        
        # 夏普比率
        risk_free_rate = 0.03  # 假设无风险利率3%
        if metrics.volatility > 0:
            metrics.sharpe_ratio = (metrics.annualized_return - risk_free_rate) / metrics.volatility
        
        # 最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        metrics.max_drawdown = drawdown.min()
        
        # 卡尔马比率
        if metrics.max_drawdown < 0:
            metrics.calmar_ratio = metrics.annualized_return / abs(metrics.max_drawdown)
        
        # 索提诺比率
        downside_returns = returns[returns < 0]
        if len(downside_returns) > 0:
            downside_volatility = downside_returns.std() * np.sqrt(252)
            if downside_volatility > 0:
                metrics.sortino_ratio = (metrics.annualized_return - risk_free_rate) / downside_volatility
        
        # VaR和CVaR
        if len(returns) >= 20:
            metrics.var_95 = np.percentile(returns, 5)
            metrics.cvar_95 = returns[returns <= metrics.var_95].mean()
        
        # 交易统计
        if self.trades:
            winning_trades = [t for t in self.trades if self._is_winning_trade(t)]
            losing_trades = [t for t in self.trades if not self._is_winning_trade(t)]
            
            metrics.trade_count = len(self.trades)
            metrics.win_rate = len(winning_trades) / len(self.trades) if self.trades else 0
            
            if winning_trades:
                metrics.avg_win = np.mean([self._get_trade_pnl(t) for t in winning_trades])
            if losing_trades:
                metrics.avg_loss = np.mean([abs(self._get_trade_pnl(t)) for t in losing_trades])
            
            if metrics.avg_loss > 0:
                metrics.profit_factor = metrics.avg_win / metrics.avg_loss
        
        # 基准比较
        if benchmark_returns is not None:
            aligned_returns = returns.reindex(benchmark_returns.index).dropna()
            aligned_benchmark = benchmark_returns.reindex(aligned_returns.index).dropna()
            
            if len(aligned_returns) > 10:
                excess_returns = aligned_returns - aligned_benchmark
                tracking_error = excess_returns.std() * np.sqrt(252)
                
                if tracking_error > 0:
                    metrics.information_ratio = excess_returns.mean() * 252 / tracking_error
                
                # 贝塔和阿尔法
                if SCIPY_AVAILABLE and len(aligned_benchmark) > 10:
                    beta, alpha, r_value, p_value, std_err = stats.linregress(
                        aligned_benchmark, aligned_returns
                    )
                    metrics.beta = beta
                    metrics.alpha = alpha * 252
        
        self.performance_metrics = metrics
        return metrics
    
    def _is_winning_trade(self, trade: Trade) -> bool:
        """判断是否为盈利交易"""
        # 简化实现，实际需要根据开平仓配对计算
        return self._get_trade_pnl(trade) > 0
    
    def _get_trade_pnl(self, trade: Trade) -> float:
        """获取交易盈亏"""
        # 简化实现，实际需要根据开平仓配对计算
        if trade.asset.symbol in self.positions:
            position = self.positions[trade.asset.symbol]
            return (trade.price - position.avg_price) * trade.quantity
        return 0.0
    
    def optimize_portfolio(self, expected_returns: Dict[str, float], 
                          risk_aversion: float = 1.0) -> Dict[str, float]:
        """优化投资组合权重"""
        if not SCIPY_AVAILABLE:
            self.logger.warning("SciPy不可用，使用等权重分配")
            symbols = list(expected_returns.keys())
            if 'CASH' in symbols:
                symbols.remove('CASH')
            if symbols:
                equal_weight = (1.0 - self.constraints.min_cash_ratio) / len(symbols)
                weights = {symbol: equal_weight for symbol in symbols}
                weights['CASH'] = self.constraints.min_cash_ratio
                return weights
            return {'CASH': 1.0}
        
        try:
            symbols = list(expected_returns.keys())
            if 'CASH' in symbols:
                symbols.remove('CASH')
            
            if not symbols:
                return {'CASH': 1.0}
            
            n_assets = len(symbols)
            
            # 构建协方差矩阵（简化实现）
            if self.covariance_matrix.empty or not all(s in self.covariance_matrix.index for s in symbols):
                # 使用简化的协方差矩阵
                cov_matrix = np.eye(n_assets) * 0.04  # 假设20%的年化波动率
                for i in range(n_assets):
                    for j in range(i+1, n_assets):
                        cov_matrix[i, j] = cov_matrix[j, i] = 0.01  # 假设5%的相关性
            else:
                cov_matrix = self.covariance_matrix.loc[symbols, symbols].values
            
            # 期望收益向量
            mu = np.array([expected_returns[symbol] for symbol in symbols])
            
            # 目标函数：最大化效用 = 期望收益 - 0.5 * 风险厌恶 * 方差
            def objective(weights):
                portfolio_return = np.dot(weights, mu)
                portfolio_variance = np.dot(weights, np.dot(cov_matrix, weights))
                return -(portfolio_return - 0.5 * risk_aversion * portfolio_variance)
            
            # 约束条件
            constraints = [
                {'type': 'eq', 'fun': lambda w: np.sum(w) - (1.0 - self.constraints.min_cash_ratio)},  # 权重和
            ]
            
            # 边界条件
            bounds = [(0, self.constraints.max_position_size) for _ in range(n_assets)]
            
            # 初始权重
            x0 = np.array([1.0 / n_assets * (1.0 - self.constraints.min_cash_ratio)] * n_assets)
            
            # 优化
            result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints)
            
            if result.success:
                optimal_weights = {symbol: weight for symbol, weight in zip(symbols, result.x)}
                optimal_weights['CASH'] = self.constraints.min_cash_ratio
                
                self.logger.info(f"投资组合优化成功，目标函数值: {-result.fun:.4f}")
                return optimal_weights
            else:
                self.logger.warning(f"投资组合优化失败: {result.message}")
                # 返回等权重分配
                equal_weight = (1.0 - self.constraints.min_cash_ratio) / len(symbols)
                weights = {symbol: equal_weight for symbol in symbols}
                weights['CASH'] = self.constraints.min_cash_ratio
                return weights
                
        except Exception as e:
            self.logger.error(f"投资组合优化出错: {e}")
            # 返回当前权重
            return self.get_position_weights()
    
    def rebalance_portfolio(self, target_weights: Dict[str, float], 
                           price_data: Dict[str, float]) -> List[Trade]:
        """再平衡投资组合"""
        trades = []
        current_weights = self.get_position_weights()
        total_value = self.get_total_value()
        
        # 计算需要调整的权重
        weight_adjustments = {}
        for symbol in set(list(current_weights.keys()) + list(target_weights.keys())):
            current_weight = current_weights.get(symbol, 0.0)
            target_weight = target_weights.get(symbol, 0.0)
            weight_diff = target_weight - current_weight
            
            if abs(weight_diff) > 0.01:  # 1%的阈值
                weight_adjustments[symbol] = weight_diff
        
        # 执行调整交易
        for symbol, weight_diff in weight_adjustments.items():
            if symbol == 'CASH':
                continue
                
            if symbol not in self.assets:
                self.logger.warning(f"未知资产，跳过再平衡: {symbol}")
                continue
            
            if symbol not in price_data:
                self.logger.warning(f"缺少价格数据，跳过再平衡: {symbol}")
                continue
            
            target_value = total_value * target_weights.get(symbol, 0.0)
            current_value = total_value * current_weights.get(symbol, 0.0)
            value_diff = target_value - current_value
            
            if abs(value_diff) > 1000:  # 1000元的阈值
                price = price_data[symbol]
                quantity = value_diff / price
                
                # 调整到最小交易单位
                min_lot = self.assets[symbol].min_lot_size
                quantity = round(quantity / min_lot) * min_lot
                
                if abs(quantity) >= min_lot:
                    trade_type = 'buy' if quantity > 0 else 'sell'
                    if self.execute_trade(symbol, abs(quantity), price, trade_type, 'rebalance'):
                        trades.append(self.trades[-1])
        
        self.last_rebalance = datetime.now()
        self.target_weights = target_weights.copy()
        
        self.logger.info(f"再平衡完成，执行{len(trades)}笔交易")
        return trades
    
    def check_risk_limits(self) -> Dict[str, bool]:
        """检查风险限制"""
        violations = {}
        
        # 检查单个资产权重限制
        weights = self.get_position_weights()
        for symbol, weight in weights.items():
            if symbol != 'CASH' and weight > self.constraints.max_position_size:
                violations[f'max_position_{symbol}'] = True
        
        # 检查行业敞口限制
        sector_exposure = self.get_sector_exposure()
        for sector, exposure in sector_exposure.items():
            if exposure > self.constraints.max_sector_exposure:
                violations[f'max_sector_{sector}'] = True
        
        # 检查杠杆限制
        total_exposure = sum(abs(pos.market_value) for pos in self.positions.values() 
                           if pos.asset.symbol != 'CASH')
        total_value = self.get_total_value()
        leverage = total_exposure / total_value if total_value > 0 else 0
        if leverage > self.constraints.max_leverage:
            violations['max_leverage'] = True
        
        # 检查现金比例
        cash_ratio = weights.get('CASH', 0.0)
        if cash_ratio < self.constraints.min_cash_ratio:
            violations['min_cash_ratio'] = True
        
        return violations
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        total_value = self.get_total_value()
        weights = self.get_position_weights()
        
        summary = {
            'total_value': total_value,
            'initial_capital': self.initial_capital,
            'total_return': (total_value / self.initial_capital) - 1,
            'cash_ratio': weights.get('CASH', 0.0),
            'position_count': len([p for p in self.positions.values() 
                                 if p.asset.symbol != 'CASH' and p.quantity != 0]),
            'total_unrealized_pnl': sum(p.unrealized_pnl for p in self.positions.values()),
            'total_realized_pnl': sum(p.realized_pnl for p in self.positions.values()),
            'total_commission': sum(p.total_commission for p in self.positions.values()),
            'trade_count': len(self.trades),
            'last_update': datetime.now().isoformat(),
            'risk_violations': self.check_risk_limits()
        }
        
        # 添加绩效指标
        if hasattr(self, 'performance_metrics'):
            summary['performance'] = self.performance_metrics.to_dict()
        
        return summary
    
    def export_positions(self) -> List[Dict[str, Any]]:
        """导出持仓数据"""
        return [pos.to_dict() for pos in self.positions.values() 
               if pos.asset.symbol != 'CASH' and pos.quantity != 0]
    
    def export_trades(self, start_date: Optional[datetime] = None, 
                     end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """导出交易记录"""
        filtered_trades = self.trades
        
        if start_date:
            filtered_trades = [t for t in filtered_trades if t.timestamp >= start_date]
        
        if end_date:
            filtered_trades = [t for t in filtered_trades if t.timestamp <= end_date]
        
        return [{
            'trade_id': t.trade_id,
            'symbol': t.asset.symbol,
            'quantity': t.quantity,
            'price': t.price,
            'trade_type': t.trade_type,
            'timestamp': t.timestamp.isoformat(),
            'commission': t.commission,
            'slippage': t.slippage,
            'notional_value': t.notional_value,
            'strategy': t.strategy,
            'metadata': t.metadata
        } for t in filtered_trades]


if __name__ == '__main__':
    # 测试代码
    import random
    
    # 创建投资组合管理器
    portfolio = PortfolioManager(initial_capital=1000000)
    
    # 添加测试资产
    assets = [
        Asset("510050", "50ETF", AssetType.ETF, "金融"),
        Asset("510300", "300ETF", AssetType.ETF, "综合"),
        Asset("000001.SZ", "平安银行", AssetType.STOCK, "金融"),
        Asset("000858.SZ", "五粮液", AssetType.STOCK, "消费")
    ]
    
    for asset in assets:
        portfolio.add_asset(asset)
    
    print("=== 投资组合管理测试 ===")
    
    # 模拟价格数据
    price_data = {
        "510050": 2.85,
        "510300": 4.12,
        "000001.SZ": 12.50,
        "000858.SZ": 185.60
    }
    
    # 执行一些交易
    trades = [
        ("510050", 10000, 2.85, "buy"),
        ("510300", 5000, 4.12, "buy"),
        ("000001.SZ", 2000, 12.50, "buy"),
        ("000858.SZ", 500, 185.60, "buy")
    ]
    
    for symbol, quantity, price, trade_type in trades:
        success = portfolio.execute_trade(symbol, quantity, price, trade_type)
        print(f"交易 {symbol}: {success}")
    
    # 更新价格
    new_prices = {
        "510050": 2.90,
        "510300": 4.20,
        "000001.SZ": 12.80,
        "000858.SZ": 190.00
    }
    portfolio.update_prices(new_prices)
    
    # 显示投资组合摘要
    summary = portfolio.get_portfolio_summary()
    print("\n=== 投资组合摘要 ===")
    for key, value in summary.items():
        if key != 'performance':
            print(f"{key}: {value}")
    
    # 显示持仓权重
    weights = portfolio.get_position_weights()
    print("\n=== 持仓权重 ===")
    for symbol, weight in weights.items():
        print(f"{symbol}: {weight:.2%}")
    
    # 显示行业敞口
    sector_exposure = portfolio.get_sector_exposure()
    print("\n=== 行业敞口 ===")
    for sector, exposure in sector_exposure.items():
        print(f"{sector}: {exposure:.2%}")
    
    # 风险指标
    risk_metrics = portfolio.calculate_risk_metrics()
    print("\n=== 风险指标 ===")
    for metric, value in risk_metrics.items():
        print(f"{metric}: {value:.4f}")
    
    # 投资组合优化测试
    expected_returns = {
        "510050": 0.08,
        "510300": 0.10,
        "000001.SZ": 0.12,
        "000858.SZ": 0.15
    }
    
    optimal_weights = portfolio.optimize_portfolio(expected_returns)
    print("\n=== 优化权重 ===")
    for symbol, weight in optimal_weights.items():
        print(f"{symbol}: {weight:.2%}")