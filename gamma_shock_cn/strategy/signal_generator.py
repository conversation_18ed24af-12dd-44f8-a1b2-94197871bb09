#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号生成器模块

整合技术指标和期权情绪分析，生成综合交易信号：
- 多重确认机制
- 信号分级系统
- 信号升级逻辑
- 风险控制

作者: AI Assistant
创建时间: 2024-12-11
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# 导入技术指标模块
from indicators import (
    calculate_all_indicators,
    calculate_williams_variant,
    calculate_volatility_indicators,
    analyze_option_sentiment
)

logger = logging.getLogger(__name__)

class SignalLevel(Enum):
    """信号等级枚举"""
    LEVEL_1 = 1  # 弱信号
    LEVEL_2 = 2  # 一般信号
    LEVEL_3 = 3  # 中等信号
    LEVEL_4 = 4  # 强信号
    LEVEL_5 = 5  # 极强信号

class SignalType(Enum):
    """信号类型枚举"""
    BUY_CALL = "buy_call"      # 买入看涨期权
    BUY_PUT = "buy_put"        # 买入看跌期权
    SELL_CALL = "sell_call"    # 卖出看涨期权
    SELL_PUT = "sell_put"      # 卖出看跌期权
    HOLD = "hold"              # 持有
    CLOSE = "close"            # 平仓

@dataclass
class TradingSignal:
    """交易信号数据结构"""
    symbol: str                    # 标的代码
    timestamp: datetime           # 信号时间
    signal_type: SignalType       # 信号类型
    level: SignalLevel           # 信号等级
    confidence: float            # 置信度 (0-100)
    reason: str                  # 信号原因
    technical_score: float       # 技术面得分
    sentiment_score: float       # 情绪面得分
    volatility_regime: str       # 波动率制度
    risk_level: str             # 风险等级
    entry_price: Optional[float] # 入场价格
    stop_loss: Optional[float]   # 止损价格
    take_profit: Optional[float] # 止盈价格
    position_size: Optional[float] # 仓位大小
    expiry_date: Optional[str]   # 期权到期日
    strike_price: Optional[float] # 行权价
    metadata: Dict[str, Any]     # 额外信息

class SignalGenerator:
    """
    信号生成器
    
    整合多种分析方法，生成综合交易信号
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化信号生成器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or self._get_default_config()
        
        # 信号权重配置
        self.weights = self.config.get('weights', {
            'technical': 0.4,
            'sentiment': 0.3,
            'volatility': 0.2,
            'williams': 0.1
        })
        
        # 信号阈值配置
        self.thresholds = self.config.get('thresholds', {
            'level_5': 85,  # 极强信号阈值
            'level_4': 70,  # 强信号阈值
            'level_3': 55,  # 中等信号阈值
            'level_2': 40,  # 一般信号阈值
            'level_1': 25   # 弱信号阈值
        })
        
        # 风险控制参数
        self.risk_params = self.config.get('risk_params', {
            'max_position_size': 0.1,  # 最大仓位比例
            'stop_loss_pct': 0.2,      # 止损百分比
            'take_profit_pct': 0.5,    # 止盈百分比
            'volatility_adjustment': True  # 是否根据波动率调整仓位
        })
        
        logger.info("信号生成器初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'weights': {
                'technical': 0.4,
                'sentiment': 0.3,
                'volatility': 0.2,
                'williams': 0.1
            },
            'thresholds': {
                'level_5': 85,
                'level_4': 70,
                'level_3': 55,
                'level_2': 40,
                'level_1': 25
            },
            'risk_params': {
                'max_position_size': 0.1,
                'stop_loss_pct': 0.2,
                'take_profit_pct': 0.5,
                'volatility_adjustment': True
            }
        }
    
    def generate_signals(self, symbol: str, price_data: pd.DataFrame,
                        option_data: Optional[Dict[str, Any]] = None,
                        vix_data: Optional[pd.DataFrame] = None) -> List[TradingSignal]:
        """
        生成综合交易信号
        
        Args:
            symbol: 标的代码
            price_data: 价格数据DataFrame
            option_data: 期权数据字典
            vix_data: VIX数据DataFrame
            
        Returns:
            交易信号列表
        """
        try:
            if price_data.empty:
                logger.warning(f"价格数据为空: {symbol}")
                return []
            
            signals = []
            
            # 1. 计算技术指标
            technical_data = calculate_all_indicators(price_data)
            
            # 2. 计算威廉指标变种
            williams_data = calculate_williams_variant(price_data)
            
            # 3. 计算波动率指标
            volatility_data, vol_stats = calculate_volatility_indicators(
                price_data, vix_data
            )
            
            # 4. 分析期权情绪（如果有期权数据）
            sentiment_analysis = None
            if option_data:
                sentiment_analysis = analyze_option_sentiment(
                    put_volume=option_data.get('put_volume', 0),
                    call_volume=option_data.get('call_volume', 0),
                    put_oi=option_data.get('put_oi'),
                    call_oi=option_data.get('call_oi'),
                    option_data=option_data.get('option_df'),
                    iv_data=option_data.get('iv_data'),
                    pcr_history=option_data.get('pcr_history')
                )
            
            # 5. 整合所有数据
            integrated_data = self._integrate_data(
                technical_data, williams_data, volatility_data, sentiment_analysis
            )
            
            # 6. 生成信号
            signals = self._generate_comprehensive_signals(
                symbol, integrated_data, vol_stats, sentiment_analysis
            )
            
            # 7. 信号过滤和优化
            signals = self._filter_and_optimize_signals(signals)
            
            logger.info(f"为 {symbol} 生成了 {len(signals)} 个信号")
            return signals
            
        except Exception as e:
            logger.error(f"生成信号时出错 ({symbol}): {e}")
            return []
    
    def _integrate_data(self, technical_data: pd.DataFrame, williams_data: pd.DataFrame,
                       volatility_data: pd.DataFrame, sentiment_analysis: Optional[Dict]) -> pd.DataFrame:
        """
        整合所有分析数据
        
        Args:
            technical_data: 技术指标数据
            williams_data: 威廉指标数据
            volatility_data: 波动率数据
            sentiment_analysis: 期权情绪分析
            
        Returns:
            整合后的数据DataFrame
        """
        try:
            # 以技术指标数据为基础
            integrated = technical_data.copy()
            
            # 合并威廉指标数据
            williams_cols = ['ZLS', 'CZX', 'C_SIGNAL', 'P_SIGNAL']
            for col in williams_cols:
                if col in williams_data.columns:
                    integrated[f'WILLIAMS_{col}'] = williams_data[col]
            
            # 合并波动率数据
            vol_cols = ['historical_volatility', 'vol_percentile', 'vol_regime']
            for col in vol_cols:
                if col in volatility_data.columns:
                    integrated[f'VOL_{col.upper()}'] = volatility_data[col]
            
            # 添加期权情绪数据（如果有）
            if sentiment_analysis:
                pcr_data = sentiment_analysis.get('pcr_data', {})
                sentiment_score = sentiment_analysis.get('sentiment_score', {})
                
                # 添加PCR相关列
                integrated['PCR_COMBINED'] = pcr_data.get('pcr_combined', 1.0)
                integrated['PCR_SENTIMENT'] = pcr_data.get('sentiment', 'neutral')
                
                # 添加情绪得分
                integrated['SENTIMENT_SCORE'] = sentiment_score.get('composite_score', 50.0)
                integrated['SENTIMENT_LEVEL'] = sentiment_score.get('sentiment_level', 'neutral')
            
            logger.debug(f"数据整合完成，最终列数: {len(integrated.columns)}")
            return integrated
            
        except Exception as e:
            logger.error(f"整合数据时出错: {e}")
            return technical_data
    
    def _generate_comprehensive_signals(self, symbol: str, data: pd.DataFrame,
                                      vol_stats: Dict, sentiment_analysis: Optional[Dict]) -> List[TradingSignal]:
        """
        生成综合交易信号
        
        Args:
            symbol: 标的代码
            data: 整合后的数据
            vol_stats: 波动率统计
            sentiment_analysis: 期权情绪分析
            
        Returns:
            交易信号列表
        """
        try:
            signals = []
            
            if data.empty:
                return signals
            
            latest = data.iloc[-1]
            current_price = latest.get('close', 0)
            
            # 计算各维度得分
            technical_score = self._calculate_technical_score(latest)
            williams_score = self._calculate_williams_score(latest)
            volatility_score = self._calculate_volatility_score(latest, vol_stats)
            sentiment_score = self._calculate_sentiment_score(latest, sentiment_analysis)
            
            # 计算综合得分
            composite_score = (
                technical_score * self.weights['technical'] +
                sentiment_score * self.weights['sentiment'] +
                volatility_score * self.weights['volatility'] +
                williams_score * self.weights['williams']
            )
            
            # 确定信号等级
            signal_level = self._determine_signal_level(composite_score)
            
            # 确定信号类型和方向
            signal_type, confidence = self._determine_signal_type(
                latest, technical_score, sentiment_score, volatility_score
            )
            
            if signal_type != SignalType.HOLD:
                # 计算风险参数
                risk_params = self._calculate_risk_parameters(
                    current_price, latest, vol_stats
                )
                
                # 生成信号原因
                reason = self._generate_signal_reason(
                    signal_type, technical_score, sentiment_score, 
                    volatility_score, williams_score
                )
                
                # 创建交易信号
                signal = TradingSignal(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    signal_type=signal_type,
                    level=signal_level,
                    confidence=confidence,
                    reason=reason,
                    technical_score=technical_score,
                    sentiment_score=sentiment_score,
                    volatility_regime=latest.get('VOL_VOL_REGIME', 'unknown'),
                    risk_level=self._assess_risk_level(vol_stats),
                    entry_price=current_price,
                    stop_loss=risk_params['stop_loss'],
                    take_profit=risk_params['take_profit'],
                    position_size=risk_params['position_size'],
                    expiry_date=None,  # 需要根据策略确定
                    strike_price=None,  # 需要根据策略确定
                    metadata={
                        'composite_score': composite_score,
                        'williams_score': williams_score,
                        'vol_stats': vol_stats,
                        'sentiment_analysis': sentiment_analysis
                    }
                )
                
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"生成综合信号时出错: {e}")
            return []
    
    def _calculate_technical_score(self, latest: pd.Series) -> float:
        """
        计算技术面得分
        
        Args:
            latest: 最新数据行
            
        Returns:
            技术面得分 (0-100)
        """
        try:
            score = 50.0  # 基础分数
            
            # EMA趋势得分
            ema8 = latest.get('EMA8', 0)
            ema21 = latest.get('EMA21', 0)
            ema55 = latest.get('EMA55', 0)
            close = latest.get('close', 0)
            
            if ema8 > ema21 > ema55 and close > ema8:
                score += 20  # 强烈上涨趋势
            elif ema8 > ema21 and close > ema21:
                score += 10  # 上涨趋势
            elif ema8 < ema21 < ema55 and close < ema8:
                score -= 20  # 强烈下跌趋势
            elif ema8 < ema21 and close < ema21:
                score -= 10  # 下跌趋势
            
            # KDJ得分
            k = latest.get('KDJ_K', 50)
            d = latest.get('KDJ_D', 50)
            j = latest.get('KDJ_J', 50)
            
            if k > 80 and d > 80:
                score -= 10  # 超买
            elif k < 20 and d < 20:
                score += 10  # 超卖反弹机会
            elif k > d and k < 80:  # 金叉且未超买
                score += 15
            elif k < d and k > 20:  # 死叉且未超卖
                score -= 15
            
            # MACD得分
            macd = latest.get('MACD', 0)
            macd_signal = latest.get('MACD_SIGNAL', 0)
            macd_hist = latest.get('MACD_HIST', 0)
            
            if macd > macd_signal and macd_hist > 0:
                score += 15  # MACD金叉且柱体为正
            elif macd < macd_signal and macd_hist < 0:
                score -= 15  # MACD死叉且柱体为负
            
            # 布林带得分
            bb_upper = latest.get('BB_UPPER', 0)
            bb_lower = latest.get('BB_LOWER', 0)
            bb_middle = latest.get('BB_MIDDLE', 0)
            
            if close > bb_upper:
                score -= 5  # 突破上轨，可能回调
            elif close < bb_lower:
                score += 5  # 跌破下轨，可能反弹
            elif close > bb_middle:
                score += 5  # 在中轨上方
            else:
                score -= 5  # 在中轨下方
            
            # 成交量确认
            vol_abnormal = latest.get('VOL_ABNORMAL', False)
            if vol_abnormal:
                if score > 50:
                    score += 10  # 放量上涨
                else:
                    score -= 10  # 放量下跌
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算技术面得分时出错: {e}")
            return 50.0
    
    def _calculate_williams_score(self, latest: pd.Series) -> float:
        """
        计算威廉指标得分
        
        Args:
            latest: 最新数据行
            
        Returns:
            威廉指标得分 (0-100)
        """
        try:
            score = 50.0
            
            c_signal = latest.get('WILLIAMS_C_SIGNAL', False)
            p_signal = latest.get('WILLIAMS_P_SIGNAL', False)
            zls = latest.get('WILLIAMS_ZLS', 0)
            czx = latest.get('WILLIAMS_CZX', 0)
            
            if c_signal:
                score += 25  # C信号：超卖反弹
            elif p_signal:
                score -= 25  # P信号：超买回调
            
            # ZLS和CZX趋势
            if zls > czx:
                score += 10  # 长期线在短期线上方
            else:
                score -= 10
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算威廉指标得分时出错: {e}")
            return 50.0
    
    def _calculate_volatility_score(self, latest: pd.Series, vol_stats: Dict) -> float:
        """
        计算波动率得分
        
        Args:
            latest: 最新数据行
            vol_stats: 波动率统计
            
        Returns:
            波动率得分 (0-100)
        """
        try:
            score = 50.0
            
            vol_regime = latest.get('VOL_VOL_REGIME', 'medium')
            vol_percentile = latest.get('VOL_VOL_PERCENTILE', 50)
            
            # 波动率制度得分
            if vol_regime == 'low':
                score += 15  # 低波动率，适合买入期权
            elif vol_regime == 'high':
                score += 10  # 高波动率，需要谨慎
            
            # 波动率百分位得分
            if vol_percentile < 30:
                score += 10  # 波动率较低
            elif vol_percentile > 70:
                score -= 5   # 波动率较高
            
            # VIX相关得分
            vix_stats = vol_stats.get('vix_stats', {})
            vix_regime = vix_stats.get('vix_regime', 'medium')
            
            if vix_regime == 'low':
                score += 10
            elif vix_regime == 'high':
                score -= 10
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算波动率得分时出错: {e}")
            return 50.0
    
    def _calculate_sentiment_score(self, latest: pd.Series, sentiment_analysis: Optional[Dict]) -> float:
        """
        计算情绪面得分
        
        Args:
            latest: 最新数据行
            sentiment_analysis: 期权情绪分析
            
        Returns:
            情绪面得分 (0-100)
        """
        try:
            if not sentiment_analysis:
                return 50.0  # 无情绪数据，返回中性得分
            
            # 直接使用情绪分析的综合得分
            sentiment_score = sentiment_analysis.get('sentiment_score', {})
            composite_score = sentiment_score.get('composite_score', 50.0)
            
            # 可以根据需要进行调整
            return max(0, min(100, composite_score))
            
        except Exception as e:
            logger.error(f"计算情绪面得分时出错: {e}")
            return 50.0
    
    def _determine_signal_level(self, composite_score: float) -> SignalLevel:
        """
        根据综合得分确定信号等级
        
        Args:
            composite_score: 综合得分
            
        Returns:
            信号等级
        """
        if composite_score >= self.thresholds['level_5']:
            return SignalLevel.LEVEL_5
        elif composite_score >= self.thresholds['level_4']:
            return SignalLevel.LEVEL_4
        elif composite_score >= self.thresholds['level_3']:
            return SignalLevel.LEVEL_3
        elif composite_score >= self.thresholds['level_2']:
            return SignalLevel.LEVEL_2
        else:
            return SignalLevel.LEVEL_1
    
    def _determine_signal_type(self, latest: pd.Series, technical_score: float,
                             sentiment_score: float, volatility_score: float) -> Tuple[SignalType, float]:
        """
        确定信号类型和置信度
        
        Args:
            latest: 最新数据行
            technical_score: 技术面得分
            sentiment_score: 情绪面得分
            volatility_score: 波动率得分
            
        Returns:
            (信号类型, 置信度)
        """
        try:
            # 计算综合得分
            avg_score = (technical_score + sentiment_score + volatility_score) / 3
            
            # 确定方向和置信度
            if avg_score > 65:
                # 强烈看涨
                signal_type = SignalType.BUY_CALL
                confidence = min(95, avg_score + 10)
            elif avg_score > 55:
                # 温和看涨
                signal_type = SignalType.BUY_CALL
                confidence = avg_score
            elif avg_score < 35:
                # 强烈看跌
                signal_type = SignalType.BUY_PUT
                confidence = min(95, (100 - avg_score) + 10)
            elif avg_score < 45:
                # 温和看跌
                signal_type = SignalType.BUY_PUT
                confidence = 100 - avg_score
            else:
                # 中性，持有
                signal_type = SignalType.HOLD
                confidence = 50.0
            
            return signal_type, confidence
            
        except Exception as e:
            logger.error(f"确定信号类型时出错: {e}")
            return SignalType.HOLD, 50.0
    
    def _calculate_risk_parameters(self, current_price: float, latest: pd.Series,
                                 vol_stats: Dict) -> Dict[str, float]:
        """
        计算风险参数
        
        Args:
            current_price: 当前价格
            latest: 最新数据行
            vol_stats: 波动率统计
            
        Returns:
            风险参数字典
        """
        try:
            # 基础止损止盈
            stop_loss_pct = self.risk_params['stop_loss_pct']
            take_profit_pct = self.risk_params['take_profit_pct']
            
            # 根据波动率调整
            if self.risk_params['volatility_adjustment']:
                vol_regime = latest.get('VOL_VOL_REGIME', 'medium')
                if vol_regime == 'high':
                    stop_loss_pct *= 1.5  # 高波动率时放宽止损
                    take_profit_pct *= 1.2
                elif vol_regime == 'low':
                    stop_loss_pct *= 0.8  # 低波动率时收紧止损
                    take_profit_pct *= 0.9
            
            stop_loss = current_price * (1 - stop_loss_pct)
            take_profit = current_price * (1 + take_profit_pct)
            
            # 仓位大小
            base_position = self.risk_params['max_position_size']
            vol_percentile = latest.get('VOL_VOL_PERCENTILE', 50)
            
            # 根据波动率调整仓位
            if vol_percentile > 70:
                position_size = base_position * 0.7  # 高波动率减仓
            elif vol_percentile < 30:
                position_size = base_position * 1.2  # 低波动率加仓
            else:
                position_size = base_position
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'position_size': min(position_size, self.risk_params['max_position_size'])
            }
            
        except Exception as e:
            logger.error(f"计算风险参数时出错: {e}")
            return {
                'stop_loss': current_price * 0.8,
                'take_profit': current_price * 1.2,
                'position_size': 0.05
            }
    
    def _assess_risk_level(self, vol_stats: Dict) -> str:
        """
        评估风险等级
        
        Args:
            vol_stats: 波动率统计
            
        Returns:
            风险等级字符串
        """
        try:
            vix_stats = vol_stats.get('vix_stats', {})
            vix_regime = vix_stats.get('vix_regime', 'medium')
            
            trend_analysis = vol_stats.get('trend_analysis', {})
            regime_stability = trend_analysis.get('regime_stability', 'unknown')
            
            if vix_regime == 'high' or regime_stability == 'unstable':
                return 'high'
            elif vix_regime == 'low' and regime_stability == 'stable':
                return 'low'
            else:
                return 'medium'
                
        except Exception as e:
            logger.error(f"评估风险等级时出错: {e}")
            return 'medium'
    
    def _generate_signal_reason(self, signal_type: SignalType, technical_score: float,
                              sentiment_score: float, volatility_score: float,
                              williams_score: float) -> str:
        """
        生成信号原因描述
        
        Args:
            signal_type: 信号类型
            technical_score: 技术面得分
            sentiment_score: 情绪面得分
            volatility_score: 波动率得分
            williams_score: 威廉指标得分
            
        Returns:
            信号原因字符串
        """
        try:
            reasons = []
            
            if technical_score > 60:
                reasons.append("技术面偏多")
            elif technical_score < 40:
                reasons.append("技术面偏空")
            
            if sentiment_score > 60:
                reasons.append("期权情绪偏多")
            elif sentiment_score < 40:
                reasons.append("期权情绪偏空")
            
            if volatility_score > 60:
                reasons.append("波动率环境有利")
            elif volatility_score < 40:
                reasons.append("波动率环境不利")
            
            if williams_score > 60:
                reasons.append("威廉指标看多")
            elif williams_score < 40:
                reasons.append("威廉指标看空")
            
            if signal_type == SignalType.BUY_CALL:
                direction = "买入看涨期权"
            elif signal_type == SignalType.BUY_PUT:
                direction = "买入看跌期权"
            else:
                direction = "持有观望"
            
            reason_text = f"{direction}: {', '.join(reasons) if reasons else '综合分析'}"
            return reason_text
            
        except Exception as e:
            logger.error(f"生成信号原因时出错: {e}")
            return "综合分析信号"
    
    def _filter_and_optimize_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """
        过滤和优化信号
        
        Args:
            signals: 原始信号列表
            
        Returns:
            优化后的信号列表
        """
        try:
            if not signals:
                return signals
            
            # 按置信度排序
            signals.sort(key=lambda x: x.confidence, reverse=True)
            
            # 过滤低质量信号
            filtered_signals = []
            for signal in signals:
                # 只保留置信度大于阈值的信号
                if signal.confidence >= 60 and signal.level.value >= 3:
                    filtered_signals.append(signal)
            
            # 限制信号数量（避免过度交易）
            max_signals = 3
            return filtered_signals[:max_signals]
            
        except Exception as e:
            logger.error(f"过滤优化信号时出错: {e}")
            return signals


def generate_trading_signals(symbol: str, price_data: pd.DataFrame,
                           option_data: Optional[Dict[str, Any]] = None,
                           vix_data: Optional[pd.DataFrame] = None,
                           config: Optional[Dict[str, Any]] = None) -> List[TradingSignal]:
    """
    便捷函数：生成交易信号
    
    Args:
        symbol: 标的代码
        price_data: 价格数据DataFrame
        option_data: 期权数据字典
        vix_data: VIX数据DataFrame
        config: 配置参数
        
    Returns:
        交易信号列表
    """
    generator = SignalGenerator(config)
    return generator.generate_signals(symbol, price_data, option_data, vix_data)


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    print("测试信号生成器...")
    
    # 创建测试数据
    np.random.seed(42)
    test_data = {
        'datetime': pd.date_range('2024-01-01', periods=100, freq='D'),
        'open': np.random.randn(100).cumsum() + 100,
        'high': np.random.randn(100).cumsum() + 102,
        'low': np.random.randn(100).cumsum() + 98,
        'close': np.random.randn(100).cumsum() + 100,
        'volume': np.random.randint(1000000, 10000000, 100)
    }
    
    test_df = pd.DataFrame(test_data)
    
    # 创建期权测试数据
    test_option_data = {
        'put_volume': 1200,
        'call_volume': 1000,
        'put_oi': 5000,
        'call_oi': 4500,
        'pcr_history': [1.1, 1.2, 1.3, 1.1, 1.0, 0.9, 1.2]
    }
    
    # 生成信号
    signals = generate_trading_signals(
        symbol="159915",  # 创业板ETF
        price_data=test_df,
        option_data=test_option_data
    )
    
    print(f"\n生成了 {len(signals)} 个信号:")
    for i, signal in enumerate(signals, 1):
        print(f"{i}. {signal.signal_type.value} - 等级{signal.level.value} - 置信度{signal.confidence:.1f}%")
        print(f"   原因: {signal.reason}")
        print(f"   技术面: {signal.technical_score:.1f}, 情绪面: {signal.sentiment_score:.1f}")
        print(f"   入场: {signal.entry_price:.2f}, 止损: {signal.stop_loss:.2f}, 止盈: {signal.take_profit:.2f}")
        print()