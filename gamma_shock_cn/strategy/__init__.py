#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy Module - 策略模块

本模块包含期权策略相关的核心组件：
- SignalClassifier: 信号分类器，负责对交易信号进行分类、评级和优先级排序
- PortfolioManager: 投资组合管理器，负责投资组合构建、优化和风险管理

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

from .signal_classifier import (
    SignalClassifier,
    Signal,
    SignalType,
    SignalStrength,
    SignalPriority,
    SignalFilter,
    ClassificationResult
)

from .portfolio_manager import (
    PortfolioManager,
    Asset,
    Position,
    Trade,
    AssetType,
    PositionType,
    RiskLevel,
    RebalanceFrequency,
    PortfolioConstraints,
    PerformanceMetrics
)

__all__ = [
    # Signal Classifier
    'SignalClassifier',
    'Signal',
    'SignalType',
    'SignalStrength',
    'SignalPriority',
    'SignalFilter',
    'ClassificationResult',
    
    # Portfolio Manager
    'PortfolioManager',
    'Asset',
    'Position',
    'Trade',
    'AssetType',
    'PositionType',
    'RiskLevel',
    'RebalanceFrequency',
    'PortfolioConstraints',
    'PerformanceMetrics'
]

__version__ = '1.0.0'
__author__ = 'Gamma Shock Team'