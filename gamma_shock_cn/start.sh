#!/bin/bash
# Gamma Shock CN 启动脚本
# 用于快速启动应用程序

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 项目配置
PROJECT_NAME="Gamma Shock CN"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_DIR="$PROJECT_DIR/venv"
REQUIREMENTS_FILE="$PROJECT_DIR/requirements.txt"
CONFIG_FILE="$PROJECT_DIR/config/config.yaml"
LOG_DIR="$PROJECT_DIR/logs"
DATA_DIR="$PROJECT_DIR/data"

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python 3.8+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    log_info "Python版本: $PYTHON_VERSION"
    
    if [[ "$PYTHON_VERSION" < "3.8" ]]; then
        log_error "Python版本过低，需要 Python 3.8+"
        exit 1
    fi
    
    log_success "Python环境检查完成"
}

# 创建虚拟环境
create_venv() {
    if [ ! -d "$VENV_DIR" ]; then
        log_info "创建虚拟环境..."
        python3 -m venv "$VENV_DIR"
        log_success "虚拟环境创建完成"
    else
        log_info "虚拟环境已存在"
    fi
}

# 激活虚拟环境
activate_venv() {
    log_info "激活虚拟环境..."
    source "$VENV_DIR/bin/activate"
    log_success "虚拟环境已激活"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    if [ -f "$REQUIREMENTS_FILE" ]; then
        pip install --upgrade pip
        pip install -r "$REQUIREMENTS_FILE"
        log_success "依赖安装完成"
    else
        log_warning "requirements.txt 文件不存在，跳过依赖安装"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p "$LOG_DIR"
    mkdir -p "$DATA_DIR/cache"
    mkdir -p "$DATA_DIR/raw"
    mkdir -p "$DATA_DIR/processed"
    mkdir -p "$PROJECT_DIR/backups"
    mkdir -p "$PROJECT_DIR/config"
    
    log_success "目录创建完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "$CONFIG_FILE" ]; then
        log_warning "配置文件不存在，创建默认配置"
        
        cat > "$CONFIG_FILE" << 'EOF'
# Gamma Shock CN 配置文件

# 应用配置
app:
  name: "Gamma Shock CN"
  version: "1.0.0"
  debug: false
  log_level: "INFO"

# 数据源配置
data_sources:
  primary: "tushare"  # tushare, eod, alpha_vantage
  tushare:
    token: "your-tushare-token-here"
    base_url: "http://api.tushare.pro"
  eod:
    api_key: "your-eod-api-key-here"
    base_url: "https://eodhistoricaldata.com/api"
  alpha_vantage:
    api_key: "your-alpha-vantage-api-key-here"
    base_url: "https://www.alphavantage.co/query"

# AI配置
ai:
  provider: "deepseek"
  deepseek:
    api_key: "your-deepseek-api-key-here"
    base_url: "https://api.deepseek.com"
    model: "deepseek-chat"
    max_tokens: 2000
    temperature: 0.1

# 通知配置
notifications:
  email:
    enabled: true
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    smtp_user: "<EMAIL>"
    smtp_password: "your-app-password"
    from_email: "<EMAIL>"
    to_emails:
      - "<EMAIL>"

# 交易配置
trading:
  symbols:
    - "AAPL"
    - "GOOGL"
    - "MSFT"
    - "AMZN"
    - "TSLA"
  timeframes:
    - "1d"
    - "4h"
    - "1h"
  
# 策略配置
strategy:
  signal_threshold: 0.7
  risk_level: "medium"
  max_positions: 5
  
# 技术指标配置
indicators:
  sma_periods: [20, 50, 200]
  ema_periods: [12, 26]
  rsi_period: 14
  macd_fast: 12
  macd_slow: 26
  macd_signal: 9
  bollinger_period: 20
  bollinger_std: 2

# 数据库配置
database:
  type: "sqlite"  # sqlite, postgresql, mysql
  sqlite:
    path: "data/gamma_shock.db"
  postgresql:
    host: "localhost"
    port: 5432
    database: "gamma_shock"
    username: "postgres"
    password: "password"

# 缓存配置
cache:
  type: "memory"  # memory, redis
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: ""

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/gamma_shock.log"
  max_size: "10MB"
  backup_count: 5
EOF
        
        log_warning "请编辑 $CONFIG_FILE 文件，配置正确的API密钥和参数"
    else
        log_success "配置文件检查完成"
    fi
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    # 检查是否有.env文件
    if [ -f "$PROJECT_DIR/.env" ]; then
        log_info "加载环境变量文件..."
        set -a  # 自动导出变量
        source "$PROJECT_DIR/.env"
        set +a
        log_success "环境变量加载完成"
    else
        log_warning ".env 文件不存在，使用默认配置"
    fi
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."
    
    # 这里可以添加数据库初始化逻辑
    # 例如：python -c "from modules.database import init_db; init_db()"
    
    log_success "数据库初始化完成"
}

# 启动应用
start_app() {
    log_info "启动 $PROJECT_NAME..."
    
    cd "$PROJECT_DIR"
    
    # 设置Python路径
    export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"
    
    # 启动主程序
    python run.py
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查必要的文件
    local required_files=("run.py" "config/config_manager.py" "data/data_fetcher.py")
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$PROJECT_DIR/$file" ]; then
            log_error "必要文件缺失: $file"
            return 1
        fi
    done
    
    # 检查Python模块
    if ! python -c "import yaml, pandas, numpy, requests" 2>/dev/null; then
        log_error "必要的Python模块缺失"
        return 1
    fi
    
    log_success "健康检查通过"
}

# 显示启动信息
show_startup_info() {
    log_info "启动信息:"
    echo "=========================================="
    echo "项目名称: $PROJECT_NAME"
    echo "项目目录: $PROJECT_DIR"
    echo "Python版本: $(python --version)"
    echo "启动时间: $(date)"
    echo "=========================================="
    echo "配置文件: $CONFIG_FILE"
    echo "日志目录: $LOG_DIR"
    echo "数据目录: $DATA_DIR"
    echo "=========================================="
    echo "使用 Ctrl+C 停止应用"
    echo "=========================================="
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    # 这里可以添加清理逻辑
    log_success "清理完成"
}

# 停止应用
stop_app() {
    log_info "停止应用..."
    # 发送停止信号
    pkill -f "python run.py" 2>/dev/null || true
    log_success "应用已停止"
}

# 重启应用
restart_app() {
    log_info "重启应用..."
    stop_app
    sleep 2
    start_app
}

# 查看日志
view_logs() {
    local log_file="$LOG_DIR/gamma_shock.log"
    
    if [ -f "$log_file" ]; then
        log_info "查看应用日志..."
        tail -f "$log_file"
    else
        log_warning "日志文件不存在: $log_file"
    fi
}

# 开发模式启动
dev_start() {
    log_info "以开发模式启动..."
    
    # 设置开发环境变量
    export ENVIRONMENT="development"
    export DEBUG="true"
    export LOG_LEVEL="DEBUG"
    
    # 启动应用
    start_app
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        "start")
            log_info "开始启动 $PROJECT_NAME..."
            check_python
            create_venv
            activate_venv
            install_dependencies
            create_directories
            check_config
            check_env_vars
            init_database
            health_check
            show_startup_info
            start_app
            ;;
        "dev")
            log_info "以开发模式启动 $PROJECT_NAME..."
            check_python
            create_venv
            activate_venv
            install_dependencies
            create_directories
            check_config
            check_env_vars
            init_database
            health_check
            show_startup_info
            dev_start
            ;;
        "stop")
            stop_app
            ;;
        "restart")
            restart_app
            ;;
        "logs")
            view_logs
            ;;
        "health")
            check_python
            create_venv
            activate_venv
            health_check
            ;;
        "setup")
            log_info "设置开发环境..."
            check_python
            create_venv
            activate_venv
            install_dependencies
            create_directories
            check_config
            log_success "开发环境设置完成"
            ;;
        *)
            echo "使用方法: $0 {start|dev|stop|restart|logs|health|setup}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动应用（生产模式）"
            echo "  dev     - 启动应用（开发模式）"
            echo "  stop    - 停止应用"
            echo "  restart - 重启应用"
            echo "  logs    - 查看应用日志"
            echo "  health  - 健康检查"
            echo "  setup   - 设置开发环境"
            exit 1
            ;;
    esac
}

# 捕获退出信号
trap cleanup EXIT

# 执行主函数
main "$@"