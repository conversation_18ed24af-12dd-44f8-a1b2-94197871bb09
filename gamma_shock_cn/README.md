# A股期权策略跟踪系统

基于技术分析的A股期权交易策略系统，采用双重确认升级版策略，结合多种技术指标和AI增强功能，实现自动化的期权交易信号生成和推送。

## 🎯 项目特点

- **多重确认机制**: 结合多种技术指标进行信号确认，避免假信号
- **六级信号分类**: 从进场信号到异常信号的完整分级体系
- **AI增强分析**: 集成DeepSeek大模型进行智能决策确认
- **实时监控**: 5分钟K线实时监控，交易时段自动运行
- **自动通知**: 邮件和企业微信自动推送交易信号

## 📊 监控标的

- **中证1000指数** (399852)
- **中证500ETF** (510500)
- **创业板ETF** (159915)

## 🏗️ 系统架构

```
gamma_shock_cn/
├── main.py                    # 主程序入口
├── run.py                     # 快速启动脚本
├── config/                    # 配置模块
│   ├── __init__.py
│   ├── settings.py           # 主配置文件
│   └── trading_hours.py      # 交易时间管理
├── data/                     # 数据模块
│   ├── __init__.py
│   ├── data_fetcher.py       # 数据获取模块
│   ├── data_cache.py         # 数据缓存管理
│   ├── data_storage.py       # 数据存储模块
│   └── data_validator.py     # 数据验证模块
├── indicators/               # 技术指标模块
│   ├── __init__.py
│   ├── technical_indicators.py # 技术指标计算
│   ├── williams_variant.py   # 威廉指标变种
│   ├── volatility_analyzer.py # 波动率分析
│   └── option_sentiment.py   # 期权情绪分析
├── signals/                  # 信号模块
│   └── signal_generator.py   # 信号生成器
├── ai/                       # AI模块
│   ├── __init__.py
│   └── deepseek_client.py    # DeepSeek API客户端
├── monitor/                  # 监控模块
│   └── market_monitor.py     # 市场监控器
├── notifications/            # 通知模块
│   └── notification_system.py # 通知系统
├── utils/                    # 工具模块
│   ├── __init__.py
│   └── logger.py            # 日志系统
├── tests/                    # 测试模块
│   └── __init__.py
├── docs/                     # 文档目录
│   ├── development_plan.md   # 开发计划
│   └── project_design.md     # 项目设计
├── requirements.txt          # 依赖包
├── .env                     # 环境变量
└── README.md               # 项目说明
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd gamma_shock_cn

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

创建 `.env` 文件并配置必要的环境变量：

```bash
# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key

# 邮件配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
RECIPIENT_EMAIL=<EMAIL>

# 数据缓存配置
CACHE_ENABLED=true
CACHE_DURATION=300

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/gamma_shock.log
```

### 3. 运行系统

```bash
# 方式1：使用主程序（完整功能）
python main.py

# 方式2：使用快速启动脚本
python run.py                # 默认配置启动
python run.py --test         # 测试模式（干运行）
python run.py --notify-test  # 通知测试

# 方式3：带参数启动
python main.py --config config.json --log-level DEBUG
python main.py --dry-run     # 干运行模式
python main.py --test-notification  # 测试通知
```

## 📈 技术指标体系

### 均线系统
- EMA8/21/55/125：多层次趋势判断
- 短期趋势：EMA8 vs EMA21
- 中期趋势：EMA21 vs EMA55
- 长期趋势：EMA55 vs EMA125

### 威廉指标变种（核心创新）
- ZLS线：21日EMA平滑的长期线
- CZX线：5日EMA平滑的短期线
- C信号：CZX上穿ZLS且ZLS<0.1（超卖反弹）
- P信号：ZLS上穿CZX且ZLS>0.25（超买回调）

### 其他指标
- KDJ随机指标（14周期）
- MACD指标（12/26/9）
- 布林带指标
- 成交量分析
- 波动率分析
- 沽购比（PCR）分析

## 🚦 信号分级系统

### 一级信号（强度3-5）- 进场信号
1. **多头进场**：EMA8上穿EMA21 + 当前收盘>EMA8
2. **空头进场**：EMA8下穿EMA21 + 当前收盘<EMA8

### 二级信号（强度2-3）- 预警信号
3. **多头预警**：收盘价介于EMA8和EMA21之间 + J线上穿K线
4. **空头预警**：收盘价介于EMA8和EMA21之间 + K线上穿J线

### 三级信号（强度2-3）- 威廉信号
5. **威廉超卖反弹**：纯C信号
6. **威廉超买回调**：纯P信号

### 四级信号（强度1）- 异常信号
7. **成交量异常**：仅成交量放大但无明确技术信号

### 五级信号 - 基于波动率（IV）的策略
- 高波动率策略：IV > 平均值 + 1.5倍标准差
- 低波动率策略：IV < 平均值 - 1倍标准差

### 六级信号 - 基于沽购比（PCR）的策略
- 高沽购比策略：PCR > 1.2
- 低沽购比策略：PCR < 0.8

## ⚡ 信号增强机制

- **成交量确认**：+1强度
- **威廉指标确认**：+1强度，可将预警升级为进场信号
- **最低触发阈值**：信号强度≥2

## 🤖 AI增强功能

- 集成DeepSeek大模型进行信号分析
- 自动生成期权策略建议（方向、行权价、到期日、仓位）
- 提供风险管理建议（止损、止盈、滚动策略）
- 双重确认机制：技术信号+AI确认

## ⏰ 运行机制

- **数据周期**：5分钟K线
- **运行时间**：北京时间9:30-11:30 & 13:00-15:00交易时段
- **缓存机制**：本地数据缓存，提高效率
- **通知系统**：邮件自动推送交易信号

## 🧪 测试

```bash
# 测试通知系统
python main.py --test-notification

# 干运行模式测试
python main.py --dry-run

# 使用快速启动脚本测试
python run.py --test
python run.py --notify-test

# 调试模式运行
python main.py --log-level DEBUG
```

## 📝 开发日志

### 当前状态 (v1.0-beta)
- ✅ **数据模块**：AKShare数据接口封装、缓存机制、数据验证
- ✅ **技术指标**：EMA、KDJ、MACD、威廉指标变种、波动率分析、期权情绪
- ✅ **信号系统**：多重确认机制、信号分级、信号生成器
- ✅ **AI模块**：DeepSeek客户端、智能分析、信号确认
- ✅ **监控系统**：实时市场监控、定时任务、状态管理
- ✅ **通知系统**：邮件通知、通知模板、失败重试
- ✅ **日志系统**：多级日志、性能监控、错误追踪
- ✅ **主程序**：命令行参数、优雅关闭、状态报告

### 版本规划
- **v1.0**：基础功能实现 (当前)
- **v1.1**：性能优化和错误处理增强
- **v1.2**：Web界面和实时图表
- **v2.0**：策略回测和参数优化

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本系统仅供学习和研究使用，不构成投资建议。使用本系统进行实际交易的风险由用户自行承担。

## 📞 联系方式

- 项目维护者：Gamma Shock Team
- 邮箱：<EMAIL>
- 项目地址：[GitHub Repository](https://github.com/your-username/gamma_shock_cn)