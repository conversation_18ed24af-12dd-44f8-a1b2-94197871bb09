#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gamma Shock 主程序入口

主要功能：
- 启动市场监控系统
- 命令行参数处理
- 日志配置
- 系统状态监控
- 优雅关闭处理

作者: AI Assistant
创建时间: 2024-12-11
"""

import asyncio
import argparse
import logging
import signal
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from monitor.market_monitor import create_market_monitor
from notifications.notification_system import create_notification_system
from config.settings import settings
from utils import setup_logging

from utils import get_logger

logger = get_logger(__name__)

class GammaShockApp:
    """
    Gamma Shock 主应用程序
    
    负责协调各个模块，管理系统生命周期
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化应用程序
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        
        # 初始化组件
        self.market_monitor = None
        self.notification_system = None
        
        # 运行状态
        self.is_running = False
        self.start_time = None
        self.shutdown_requested = False
        
        # 统计信息
        self.app_stats = {
            'start_time': None,
            'uptime': 0,
            'monitoring_cycles': 0,
            'notifications_sent': 0,
            'errors': 0
        }
        
        logger.info("Gamma Shock 应用程序初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置
        
        Returns:
            配置字典
        """
        config = {
            'monitoring': settings.system.SCHEDULE_CONFIG,
            'notification': {'email': settings.email},
            'symbols': settings.get_target_symbols(),
            'ai': settings.ai,
            'logging': settings.system.LOGGING_CONFIG
        }
        
        # 如果指定了配置文件，则加载并合并
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                
                # 深度合并配置
                config = self._merge_config(config, file_config)
                logger.info(f"已加载配置文件: {self.config_file}")
                
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
        
        return config
    
    def _merge_config(self, base_config: Dict[str, Any], 
                     file_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并配置
        
        Args:
            base_config: 基础配置
            file_config: 文件配置
            
        Returns:
            合并后的配置
        """
        merged = base_config.copy()
        
        for key, value in file_config.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_config(merged[key], value)
            else:
                merged[key] = value
        
        return merged
    
    async def start(self):
        """
        启动应用程序
        """
        if self.is_running:
            logger.warning("应用程序已在运行中")
            return
        
        try:
            logger.info("🚀 启动 Gamma Shock 交易监控系统")
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            # 初始化组件
            await self._initialize_components()
            
            # 发送启动通知
            await self._send_startup_notification()
            
            # 标记为运行状态
            self.is_running = True
            self.start_time = datetime.now()
            self.app_stats['start_time'] = self.start_time
            
            logger.info("✅ 系统启动完成，开始监控...")
            
            # 启动监控
            await self._run_monitoring_loop()
            
        except Exception as e:
            logger.error(f"启动失败: {e}")
            self.app_stats['errors'] += 1
            await self._send_error_notification(f"系统启动失败: {e}")
            raise
        finally:
            await self._cleanup()
    
    async def _initialize_components(self):
        """
        初始化系统组件
        """
        logger.info("初始化系统组件...")
        
        # 初始化通知系统
        # 从配置中获取EmailConfig对象并转换为字典格式
        email_config_obj = self.config['notification']['email']
        notification_config = {
            'email': {
                'enabled': True,
                'smtp_server': email_config_obj.SMTP_SERVER,
                'smtp_port': email_config_obj.SMTP_PORT,
                'sender_email': email_config_obj.SENDER_EMAIL,
                'sender_password': email_config_obj.SENDER_PASSWORD,
                'use_tls': email_config_obj.SMTP_USE_TLS
            },
            'max_history_size': 1000
        }
        self.notification_system = create_notification_system(notification_config)
        logger.info("✅ 通知系统初始化完成")
        
        # 初始化市场监控器
        # 将MonitoringConfig对象转换为字典格式
        monitoring_config = self.config['monitoring']
        monitor_config_dict = {
            'monitoring_interval': monitoring_config.get('monitoring_interval', 300),
            'ai_confirmation_threshold': monitoring_config.get('ai_confirmation_threshold', 3),
            'status_interval': monitoring_config.get('status_interval', 3600),
            'test_mode': monitoring_config.get('test_mode', False)  # 测试模式，忽略交易时间限制
        }
        self.market_monitor = create_market_monitor(monitor_config_dict)
        logger.info("✅ 市场监控器初始化完成")
        
        # 验证组件状态
        await self._validate_components()
    
    async def _validate_components(self):
        """
        验证组件状态
        """
        logger.info("验证组件状态...")
        
        # 验证通知系统
        notification_stats = self.notification_system.get_notification_stats()
        if not notification_stats['providers']:
            logger.warning("⚠️ 没有可用的通知提供者")
        else:
            logger.info(f"✅ 通知提供者: {notification_stats['providers']}")
        
        # 验证监控器
        monitor_stats = self.market_monitor.get_monitoring_stats()
        symbols_count = monitor_stats['symbols_count']
        if symbols_count == 0:
            logger.warning("⚠️ 没有配置监控标的")
        else:
            logger.info(f"✅ 监控标的数量: {symbols_count}")
        
        logger.info("✅ 组件验证完成")
    
    async def _run_monitoring_loop(self):
        """
        运行监控循环
        """
        try:
            # 启动市场监控
            monitoring_task = asyncio.create_task(
                self.market_monitor.start_monitoring()
            )
            
            # 启动状态监控
            status_task = asyncio.create_task(
                self._run_status_monitoring()
            )
            
            # 等待任务完成或被取消
            await asyncio.gather(monitoring_task, status_task)
            
        except asyncio.CancelledError:
            logger.info("监控循环被取消")
        except Exception as e:
            logger.error(f"监控循环异常: {e}")
            self.app_stats['errors'] += 1
            raise
    
    async def _run_status_monitoring(self):
        """
        运行状态监控
        """
        status_interval = self.config['monitoring'].get('status_interval', 3600)  # 1小时
        
        while self.is_running and not self.shutdown_requested:
            try:
                await asyncio.sleep(status_interval)
                
                if not self.shutdown_requested:
                    await self._report_status()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"状态监控异常: {e}")
                self.app_stats['errors'] += 1
    
    async def _report_status(self):
        """
        报告系统状态
        """
        try:
            # 更新统计信息
            self._update_stats()
            
            # 获取各组件状态
            monitor_stats = self.market_monitor.get_monitoring_stats()
            notification_stats = self.notification_system.get_notification_stats()
            
            # 生成状态报告
            status_report = self._generate_status_report(
                monitor_stats, notification_stats
            )
            
            logger.info(f"系统状态报告:\n{status_report}")
            
            # 发送状态通知（如果配置了）
            # 注意：EmailConfig对象没有get方法，这里暂时禁用状态报告发送
            # 如果需要启用，可以在EmailConfig中添加send_status_reports属性
            send_status_reports = getattr(self.config['notification'], 'send_status_reports', False)
            if send_status_reports:
                await self.notification_system.send_system_notification(
                    title="系统状态报告",
                    content=status_report,
                    priority="low"
                )
            
        except Exception as e:
            logger.error(f"状态报告失败: {e}")
            self.app_stats['errors'] += 1
    
    def _update_stats(self):
        """
        更新统计信息
        """
        if self.start_time:
            self.app_stats['uptime'] = (datetime.now() - self.start_time).total_seconds()
        
        # 获取监控统计
        if self.market_monitor:
            monitor_stats = self.market_monitor.get_monitoring_stats()
            self.app_stats['monitoring_cycles'] = monitor_stats['monitoring_stats']['total_runs']
        
        # 获取通知统计
        if self.notification_system:
            notification_stats = self.notification_system.get_notification_stats()
            self.app_stats['notifications_sent'] = notification_stats['send_stats']['total_sent']
    
    def _generate_status_report(self, monitor_stats: Dict[str, Any],
                              notification_stats: Dict[str, Any]) -> str:
        """
        生成状态报告
        
        Args:
            monitor_stats: 监控统计
            notification_stats: 通知统计
            
        Returns:
            状态报告文本
        """
        uptime_hours = self.app_stats['uptime'] / 3600
        
        report_lines = [
            "=== Gamma Shock 系统状态 ===",
            f"运行时间: {uptime_hours:.1f} 小时",
            f"启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else 'N/A'}",
            "",
            "=== 监控统计 ===",
            f"监控周期: {monitor_stats['monitoring_stats']['total_runs']}",
            f"成功周期: {monitor_stats['monitoring_stats']['successful_runs']}",
            f"失败周期: {monitor_stats['monitoring_stats']['failed_runs']}",
            f"生成信号: {monitor_stats['monitoring_stats']['signals_generated']}",
            f"AI确认: {monitor_stats['monitoring_stats']['ai_confirmations']}",
            f"监控标的: {monitor_stats['symbols_count']}",
            "",
            "=== 通知统计 ===",
            f"总发送: {notification_stats['send_stats']['total_sent']}",
            f"成功发送: {notification_stats['send_stats']['successful_sent']}",
            f"失败发送: {notification_stats['send_stats']['failed_sent']}",
            f"通知提供者: {', '.join(notification_stats['providers'])}",
            f"失败通知: {notification_stats['failed_notifications']}",
            "",
            "=== 系统统计 ===",
            f"系统错误: {self.app_stats['errors']}",
            f"运行状态: {'正常' if self.is_running else '已停止'}"
        ]
        
        return "\n".join(report_lines)
    
    async def _send_startup_notification(self):
        """
        发送启动通知
        """
        try:
            if self.notification_system:
                await self.notification_system.send_system_notification(
                    title="系统启动",
                    content=f"Gamma Shock 交易监控系统已于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 启动",
                    priority="medium"
                )
        except Exception as e:
            logger.error(f"发送启动通知失败: {e}")
    
    async def _send_error_notification(self, error_message: str):
        """
        发送错误通知
        
        Args:
            error_message: 错误消息
        """
        try:
            if self.notification_system:
                await self.notification_system.send_system_notification(
                    title="系统错误",
                    content=f"系统发生错误: {error_message}\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    priority="urgent"
                )
        except Exception as e:
            logger.error(f"发送错误通知失败: {e}")
    
    async def _send_shutdown_notification(self):
        """
        发送关闭通知
        """
        try:
            if self.notification_system:
                uptime_hours = self.app_stats['uptime'] / 3600
                await self.notification_system.send_system_notification(
                    title="系统关闭",
                    content=f"Gamma Shock 交易监控系统已关闭\n运行时间: {uptime_hours:.1f} 小时\n关闭时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    priority="medium"
                )
        except Exception as e:
            logger.error(f"发送关闭通知失败: {e}")
    
    def _setup_signal_handlers(self):
        """
        设置信号处理器
        """
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name
            logger.info(f"收到信号 {signal_name}，准备优雅关闭...")
            self.shutdown_requested = True
            
            # 创建关闭任务
            asyncio.create_task(self.shutdown())
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)  # 挂起信号
    
    async def shutdown(self):
        """
        优雅关闭系统
        """
        if not self.is_running:
            return
        
        logger.info("🛑 开始关闭系统...")
        
        try:
            # 标记关闭状态
            self.shutdown_requested = True
            self.is_running = False
            
            # 更新统计信息
            self._update_stats()
            
            # 停止市场监控
            if self.market_monitor:
                self.market_monitor.stop_monitoring()
                logger.info("✅ 市场监控已停止")
            
            # 重试失败的通知
            if self.notification_system:
                retry_count = await self.notification_system.retry_failed_notifications()
                if retry_count > 0:
                    logger.info(f"✅ 重试了 {retry_count} 个失败通知")
            
            # 发送关闭通知
            await self._send_shutdown_notification()
            
            # 等待一段时间确保通知发送完成
            await asyncio.sleep(2)
            
            logger.info("✅ 系统已优雅关闭")
            
        except Exception as e:
            logger.error(f"关闭过程中出错: {e}")
        finally:
            await self._cleanup()
    
    async def _cleanup(self):
        """
        清理资源
        """
        try:
            # 清理组件
            self.market_monitor = None
            self.notification_system = None
            
            logger.info("✅ 资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    def get_app_stats(self) -> Dict[str, Any]:
        """
        获取应用程序统计信息
        
        Returns:
            统计信息字典
        """
        self._update_stats()
        return self.app_stats.copy()
    
    async def run_once(self):
        """
        执行一次监控循环（测试模式）
        """
        logger.info("🧪 开始执行单次监控循环...")
        
        try:
            # 确保组件已初始化
            if not self.market_monitor:
                logger.error("市场监控器未初始化")
                return
            
            if not self.notification_system:
                logger.error("通知系统未初始化")
                return
            
            # 启用测试模式
            self.market_monitor.test_mode = True
            logger.info("✅ 已启用测试模式：忽略交易时间限制")
            
            # 执行一次监控
            logger.info("📊 开始数据获取和分析...")
            await self.market_monitor._run_monitoring_cycle()
            
            # 获取监控统计
            monitor_stats = self.market_monitor.get_monitoring_stats()
            logger.info(f"📈 监控统计: {monitor_stats}")
            
            # 获取通知统计
            notification_stats = self.notification_system.get_notification_stats()
            logger.info(f"📧 通知统计: {notification_stats}")
            
            logger.info("✅ 单次监控循环完成")
            
        except Exception as e:
            logger.error(f"❌ 单次监控循环失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise


def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器
    
    Returns:
        参数解析器
    """
    parser = argparse.ArgumentParser(
        description="Gamma Shock 期权交易监控系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                          # 使用默认配置启动
  python main.py --config config.json    # 使用指定配置文件
  python main.py --log-level DEBUG       # 设置日志级别
  python main.py --dry-run               # 干运行模式（不发送通知）
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--log-level', '-l',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--log-file',
        type=str,
        help='日志文件路径'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，不发送实际通知'
    )
    
    parser.add_argument(
        '--test-notification',
        action='store_true',
        help='发送测试通知后退出'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='Gamma Shock v1.0.0'
    )
    
    return parser


async def test_notification(app: GammaShockApp):
    """
    测试通知功能
    
    Args:
        app: 应用程序实例
    """
    logger.info("🧪 测试通知功能...")
    
    try:
        # 初始化通知系统
        app.notification_system = create_notification_system(
            app.config['notification']
        )
        
        # 发送测试通知
        results = await app.notification_system.send_system_notification(
            title="系统测试",
            content="这是一个测试通知，用于验证通知系统是否正常工作。",
            priority="low"
        )
        
        logger.info(f"✅ 测试通知发送结果: {results}")
        
        # 获取统计信息
        stats = app.notification_system.get_notification_stats()
        logger.info(f"📊 通知统计: {stats}")
        
    except Exception as e:
        logger.error(f"❌ 测试通知失败: {e}")
        raise


async def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 设置日志
    log_config = settings.system.LOGGING_CONFIG.copy()
    log_config['level'] = args.log_level
    if args.log_file:
        log_config['file'] = args.log_file
    
    setup_logging(log_config)
    
    logger.info("🚀 启动 Gamma Shock 期权交易监控系统")
    logger.info(f"📋 命令行参数: {vars(args)}")
    
    try:
        # 创建应用程序
        app = GammaShockApp(config_file=args.config)
        
        # 如果是测试通知模式
        if args.test_notification:
            await test_notification(app)
            logger.info("✅ 测试通知完成")
            return
        
        # 如果是干运行模式，修改配置
        if args.dry_run:
            logger.info("🧪 干运行模式：不会发送实际通知")
            # 禁用所有通知提供者
            for provider_config in app.config['notification'].values():
                if isinstance(provider_config, dict):
                    provider_config['enabled'] = False
        
        # 启动应用程序
        await app.start()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"❌ 应用程序异常: {e}")
        sys.exit(1)
    finally:
        logger.info("👋 Gamma Shock 系统已退出")


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {e}")
        sys.exit(1)