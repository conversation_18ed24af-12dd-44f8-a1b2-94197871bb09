# -*- coding: utf-8 -*-
"""
美股个股期权策略跟踪系统
- 应用于QQQ, SPY, NVDA, TSLA, AMZN, META, NFLX等标的
- 基于multi_index_gamma.py策略改编
- 利用AI导师(DeepSeek)进行交易信号分析
"""
import akshare as ak
import pandas as pd
from functools import lru_cache
from datetime import datetime, timedelta
import os
import sys
import json
import time
import requests
import schedule
import logging
import argparse
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
import my_mail_client as mymail
from dotenv import load_dotenv

# 导入自定义模块
# 假设通用模块在同一路径或PYTHONPATH下
from modules.cache_manager import setup_cache_dirs, save_cache, load_cache
from modules.email_sender import send_qw_wrapper

# --- AI导师模块 ---
AGENT_PROMPT_JSON_PATH = "options_trading_prompt.json"
MASTER_AGENT_PROMPT = None

def load_master_agent_prompt():
    """
    /**
     * @description 在脚本启动时加载AI导师的配置文件
     */
    """
    global MASTER_AGENT_PROMPT
    try:
        with open(AGENT_PROMPT_JSON_PATH, 'r', encoding='utf-8') as f:
            MASTER_AGENT_PROMPT = json.load(f)
        logger.info(f"成功加载AI导师配置文件: {AGENT_PROMPT_JSON_PATH}")
    except Exception as e:
        logger.error(f"加载AI导师配置文件失败: {e}")
        MASTER_AGENT_PROMPT = None

def call_llm_api(system_prompt: str, user_prompt: str) -> str:
    """
    /**
     * @description 调用DeepSeek大语言模型API的函数。
     * @param {string} system_prompt - 系统提示词，定义了AI的角色和规则。
     * @param {string} user_prompt - 用户提示词，包含了实时市场数据和请求。
     * @returns {string} 从LLM返回的分析文本。
     */
    """
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        logger.error("错误: DEEPSEEK_API_KEY 环境变量未设置。")
        return "错误: DeepSeek API Key未配置。"

    url = "https://api.deepseek.com/chat/completions"
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    payload = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "temperature": 0.7,
        "stream": False
    }

    try:
        logger.info("正在调用DeepSeek Chat API...")
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        
        data = response.json()
        content = data['choices'][0]['message']['content']
        logger.info("成功从DeepSeek Chat API获取分析。")
        return content

    except requests.exceptions.RequestException as e:
        logger.error(f"调用DeepSeek API时发生网络错误: {e}")
        return f"错误: 调用DeepSeek API时发生网络错误: {e}"
    except KeyError:
        logger.error(f"从DeepSeek API返回的数据格式不正确: {response.text}")
        return "错误: 从DeepSeek API返回的数据格式不正确。"
    except Exception as e:
        logger.error(f"调用DeepSeek API时发生未知错误: {e}")
        return f"错误: 调用DeepSeek API时发生未知错误: {e}"

def get_master_analysis(market_data: dict) -> str:
    """
    /**
     * @description 获取AI导师的综合分析。
     * @param {dict} market_data - 包含市场数据的结构化字典。
     * @returns {string} AI导师的详细分析。
     */
    """
    if not MASTER_AGENT_PROMPT:
        return "错误: AI导师的配置文件未加载。"

    try:
        system_prompt = json.dumps(MASTER_AGENT_PROMPT, ensure_ascii=False)
        user_prompt_text = f"""你好，Alata。我的美股期权双重确认策略脚本检测到一个潜在的交易信号，请按照结论优先的格式进行分析。

请严格按照以下格式回复：

## 期权策略建议
**方向**: [买入看涨期权/买入看跌期权/观察等待]
**行权价**: [具体价格]（[价格相对性描述]）
**到期日**: [建议天数范围]
**仓位**: [百分比]（[基于信号强度的说明]）

## 风险管理
**止损**: [具体条件或百分比]
**止盈**: [分层策略描述]
**Roll Up**: [滚动策略说明]

## 分析依据
[详细的技术分析和市场逻辑]

以下是相关数据：
{json.dumps(market_data, indent=2, ensure_ascii=False)}"""
        
        analysis = call_llm_api(system_prompt, user_prompt_text)
        return analysis
        
    except Exception as e:
        logger.error(f"获取AI导师分析时出错: {e}")
        return f"获取AI导师分析时出错: {e}"

# --- 配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), "gamma_shock_us.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

CONFIG = {
    'data_dir': "/Users/<USER>/VSCodeProjects/data/us_data",
    'symbols': ['QQQ', 'NVDA', 'TSLA', 'AMZN', 'META', 'NFLX', 'AVGO', 'MSTR', 'COIN', 'HOOD', 'GOOG', 'AMD', 'PLTR'],
    'period': "5"  # 单位：分钟
}

data_dir = str(CONFIG['data_dir']) # 明确转换为字符串以帮助linter
cache_dir = os.path.join(data_dir, "cache")
setup_cache_dirs(data_dir, cache_dir)

# --- 数据获取 ---

def get_us_stock_ticker_map():
    """
    /**
     * @description 获取美股代码到akshare代码的映射（简化版本）。
     * @returns {dict} Ticker到akshare代码的映射字典, e.g., {'NVDA': '105.NVDA'}。
     */
    """
    # 直接映射为105.xxx格式，无需API调用
    symbols = CONFIG['symbols']
    ticker_map = {symbol: f"105.{symbol}" for symbol in symbols}
    logger.info(f"已创建美股ticker映射: {ticker_map}")
    return ticker_map


def calculate_stock_volatility(stock_data: pd.DataFrame, period: int = 20) -> float:
    """
    /**
     * @description 计算个股的历史波动率（年化）
     * @param {pd.DataFrame} stock_data - 股票数据
     * @param {int} period - 计算周期（默认20日）
     * @returns {float} 年化波动率
     */
    """
    if len(stock_data) < period:
        return 0.0
    
    # 计算收益率
    returns = stock_data['close'].pct_change().dropna()
    
    if len(returns) < period:
        return 0.0
    
    # 计算最近period天的波动率并年化
    recent_returns = returns.tail(period)
    volatility = recent_returns.std() * (252 ** 0.5)  # 年化（假设252个交易日）
    
    return volatility





def get_us_stock_data(symbol: str, ak_code: str, period: str = "5"):
    """
    /**
     * @description 获取美股分钟线数据，并进行缓存和处理。
     * @param {string} symbol - 股票代码 (e.g., "NVDA")。
     * @param {string} ak_code - Akshare专用的股票代码 (e.g., "105.NVDA")。
     * @param {string} period - K线周期（分钟）。
     * @returns {pd.DataFrame | None} 处理后的分钟线数据。
     */
    """
    cache_file = os.path.join(cache_dir, f"stock_{symbol}_{period}min.csv")
    
    # 尝试从缓存加载
    if os.path.exists(cache_file):
        file_mod_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        # 对于分钟线数据，设置一个较短的缓存有效期，例如等于K线周期
        if (datetime.now() - file_mod_time).seconds < int(period) * 60:
            try:
                logger.info(f"使用缓存的 {symbol} 数据。")
                df = pd.read_csv(cache_file)
                df['date'] = pd.to_datetime(df['date'])
                return df.set_index('date')
            except Exception as e:
                logger.warning(f"读取 {symbol} 缓存失败: {e}, 将重新获取。")

    logger.info(f"从API获取 {symbol} ({ak_code}) 的分钟线数据...")
    try:
        # stock_us_hist_min_em 获取的是1分钟数据，我们需要重采样
        stock_hist_min_df = ak.stock_us_hist_min_em(symbol=ak_code)
        if stock_hist_min_df is None or stock_hist_min_df.empty:
            logger.warning(f"未能获取到 {symbol} 的分钟线数据。")
            return None

        stock_hist_min_df.rename(columns={'时间': 'date', '开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low', '成交量': 'volume'}, inplace=True)
        stock_hist_min_df['date'] = pd.to_datetime(stock_hist_min_df['date'])
        stock_hist_min_df.set_index('date', inplace=True)
        
        # 将数据类型转换为数值型
        for col in ['open', 'high', 'low', 'close', 'volume']:
            stock_hist_min_df[col] = pd.to_numeric(stock_hist_min_df[col], errors='coerce')

        # 重采样为指定周期，使用新的时间单位格式
        resampled_df = stock_hist_min_df.resample(f'{period}min').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        if resampled_df.empty:
            logger.warning(f"{symbol} 重采样后数据为空。")
            return None

        resampled_df.to_csv(cache_file)
        logger.info(f"已更新 {symbol} 的数据缓存。")
        return resampled_df

    except Exception as e:
        logger.error(f"获取 {symbol} 数据时出错: {e}")
        # 如果API失败，尝试返回旧缓存
        if os.path.exists(cache_file):
            try:
                logger.warning(f"API请求失败，使用 {symbol} 的旧缓存数据。")
                df = pd.read_csv(cache_file)
                df['date'] = pd.to_datetime(df['date'])
                return df.set_index('date')
            except Exception as cache_err:
                logger.error(f"读取 {symbol} 的旧缓存也失败了: {cache_err}")
        return None

# --- 策略分析 ---

def analyze_stock_data(symbol: str, ak_code: str):
    """
    /**
     * @description 分析单个美股标的数据并生成交易信号（双重确认升级版策略）。
     * @param {string} symbol - 股票代码 (e.g., "NVDA")。
     * @param {string} ak_code - Akshare专用代码 (e.g., "105.NVDA")。
     * @returns {tuple} 包含市场数据的字典和信号检测标志的元组 (market_data, signal_detected)。
     */
     """
    logger.info(f"开始分析 {symbol} 数据...")
    
    stock_data = get_us_stock_data(symbol, ak_code, period=str(CONFIG['period']))
    
    if stock_data is None or stock_data.empty:
        logger.error(f"无法获取 {symbol} 数据，跳过分析。")
        return None, False

    # --- 计算技术指标（双重确认升级版） ---
    # 多条均线系统
    stock_data['EMA8'] = stock_data['close'].ewm(span=8, adjust=False).mean()
    stock_data['EMA21'] = stock_data['close'].ewm(span=21, adjust=False).mean()
    stock_data['EMA55'] = stock_data['close'].ewm(span=55, adjust=False).mean()
    stock_data['EMA125'] = stock_data['close'].ewm(span=125, adjust=False).mean()

    # 改进的KDJ指标（使用14周期）
    low_list = stock_data['low'].rolling(window=14, min_periods=1).min()
    high_list = stock_data['high'].rolling(window=14, min_periods=1).max()
    rsv = (stock_data['close'] - low_list) / (high_list - low_list) * 100
    stock_data['K'] = rsv.ewm(com=2, adjust=False).mean()  # SMA(RSV,3,1) 近似
    stock_data['D'] = stock_data['K'].ewm(com=2, adjust=False).mean()  # SMA(K,3,1) 近似
    stock_data['J'] = 3 * stock_data['K'] - 2 * stock_data['D']
    
    # MACD指标
    stock_data['EMA12'] = stock_data['close'].ewm(span=12, adjust=False).mean()
    stock_data['EMA26'] = stock_data['close'].ewm(span=26, adjust=False).mean()
    stock_data['DIF'] = stock_data['EMA12'] - stock_data['EMA26']
    stock_data['DEA'] = stock_data['DIF'].ewm(span=9, adjust=False).mean()
    stock_data['MACD'] = (stock_data['DIF'] - stock_data['DEA']) * 2

    # 成交量移动平均线
    stock_data['VOL_MA10'] = stock_data['volume'].rolling(window=10, min_periods=1).mean()

    # --- 威廉指标变种（新增） ---
    N = 19
    stock_data['VAR1'] = stock_data['high'].rolling(window=N, min_periods=1).max()  # HHV(HIGH,19)
    stock_data['VAR2'] = stock_data['low'].rolling(window=N, min_periods=1).min()   # LLV(LOW,19)
    
    # 计算威廉指标基础值
    williams_base = (stock_data['close'] - stock_data['VAR2']) / (stock_data['VAR1'] - stock_data['VAR2'])
    williams_base = williams_base.fillna(0.5)  # 处理除零情况
    
    # ZLS: 21日EMA平滑后减0.5 (长期线)
    stock_data['ZLS'] = williams_base.ewm(span=21, adjust=False).mean() - 0.5
    
    # CZX: 5日EMA平滑后减0.5 (短期线)  
    stock_data['CZX'] = williams_base.ewm(span=5, adjust=False).mean() - 0.5
    
    # HLB: 两线差值
    stock_data['HLB'] = stock_data['CZX'] - stock_data['ZLS']

    # --- 简化的波动率指标（仅使用个股波动率） ---
    stock_volatility = None
    vol_percentile = None
    
    try:
        stock_volatility = calculate_stock_volatility(stock_data, period=20)
        if stock_volatility > 0:
            # 计算波动率的历史百分位
            volatilities = []
            for i in range(20, len(stock_data)):
                hist_vol = calculate_stock_volatility(stock_data.iloc[:i+1], period=20)
                if hist_vol > 0:
                    volatilities.append(hist_vol)
            
            if volatilities:
                vol_percentile = sum(1 for v in volatilities if v <= stock_volatility) / len(volatilities)
                logger.info(f"{symbol}: 个股波动率={stock_volatility:.2%}, 百分位={vol_percentile:.2%}")
    except Exception as e:
        logger.warning(f"{symbol}: 计算个股波动率时出错: {e}")
        stock_volatility = 0.2  # 默认值
        vol_percentile = 0.5

    # --- 准备返回数据 ---
    if len(stock_data) < 3:
        logger.warning(f"{symbol} 数据点不足，无法生成信号。")
        return None, False
        
    # 获取当前和历史数据点
    current_row = stock_data.iloc[-1]
    prev_row = stock_data.iloc[-2] if len(stock_data) >= 2 else current_row
    prev2_row = stock_data.iloc[-3] if len(stock_data) >= 3 else prev_row

    # --- 威廉指标信号检测 ---
    williams_c_signal = False  # 超卖反弹信号
    williams_p_signal = False  # 超买回调信号
    
    # C信号: CROSS(CZX, ZLS) AND ZLS < 0.1
    if (current_row['CZX'] > current_row['ZLS'] and 
        prev_row['CZX'] <= prev_row['ZLS'] and 
        current_row['ZLS'] < 0.1):
        williams_c_signal = True
        logger.info(f"{symbol}: 威廉指标C信号 - 超卖反弹 (ZLS={current_row['ZLS']:.3f})")
    
    # P信号: CROSS(ZLS, CZX) AND ZLS > 0.25
    if (current_row['ZLS'] > current_row['CZX'] and 
        prev_row['ZLS'] <= prev_row['CZX'] and 
        current_row['ZLS'] > 0.25):
        williams_p_signal = True
        logger.info(f"{symbol}: 威廉指标P信号 - 超买回调 (ZLS={current_row['ZLS']:.3f})")

    # --- 双重确认升级版信号逻辑 ---
    signal_type = "无信号"
    signal_strength = 0
    volume_confirmation = False
    williams_confirmation = ""
    
    # 成交量确认条件
    if current_row['volume'] > current_row['VOL_MA10'] * 3:
        volume_confirmation = True
        logger.info(f"{symbol}: 检测到成交量异常放大 (当前: {current_row['volume']:.0f}, 10日均量: {current_row['VOL_MA10']:.0f})")

    # 1. 多头进场信号
    # CROSS(EMA8, EMA21) AND REF(EMA8,1)<REF(EMA21,1) AND REF(CLOSE,1)>REF(EMA8,1)
    if (current_row['EMA8'] > current_row['EMA21'] and 
        prev_row['EMA8'] < prev_row['EMA21'] and 
        prev_row['close'] > prev_row['EMA8']):
        signal_type = "多头进场信号"
        signal_strength = 3
        if volume_confirmation:
            signal_strength += 1
        if williams_c_signal:  # 威廉指标确认
            signal_strength += 1
            williams_confirmation = "威廉C信号确认"

    # 2. 空头进场信号  
    # CROSS(EMA21, EMA8) AND REF(EMA8,1)>REF(EMA21,1) AND REF(CLOSE,1)<REF(EMA8,1)
    elif (current_row['EMA21'] > current_row['EMA8'] and 
          prev_row['EMA8'] > prev_row['EMA21'] and 
          prev_row['close'] < prev_row['EMA8']):
        signal_type = "空头进场信号"
        signal_strength = 3
        if volume_confirmation:
            signal_strength += 1
        if williams_p_signal:  # 威廉指标确认
            signal_strength += 1
            williams_confirmation = "威廉P信号确认"

    # 3. 多头预警
    # REF(CLOSE,1)>REF(EMA8,1) AND REF(CLOSE,1)<REF(EMA21,1) AND CROSS(J,K)
    elif (prev_row['close'] > prev_row['EMA8'] and 
          prev_row['close'] < prev_row['EMA21'] and 
          current_row['J'] > current_row['K'] and 
          prev_row['J'] < prev_row['K']):
        signal_type = "多头预警信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        if williams_c_signal:  # 威廉指标确认可升级为进场信号
            signal_type = "多头进场信号(威廉确认)"
            signal_strength += 1
            williams_confirmation = "威廉C信号升级"

    # 4. 空头预警
    # REF(CLOSE,1)<REF(EMA8,1) AND REF(CLOSE,1)>REF(EMA21,1) AND CROSS(K,J)
    elif (prev_row['close'] < prev_row['EMA8'] and 
          prev_row['close'] > prev_row['EMA21'] and 
          current_row['K'] > current_row['J'] and 
          prev_row['K'] < prev_row['J']):
        signal_type = "空头预警信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        if williams_p_signal:  # 威廉指标确认可升级为进场信号
            signal_type = "空头进场信号(威廉确认)"
            signal_strength += 1
            williams_confirmation = "威廉P信号升级"

    # 5. 纯威廉指标信号（当主策略无明确信号时）
    elif williams_c_signal:
        signal_type = "威廉超卖反弹信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        williams_confirmation = "纯威廉C信号"
    elif williams_p_signal:
        signal_type = "威廉超买回调信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        williams_confirmation = "纯威廉P信号"

    # 6. 成交量异常但无明确技术信号
    elif volume_confirmation:
        signal_type = "成交量异常信号"
        signal_strength = 1

    # 判断是否有信号
    signal_detected = signal_strength >= 2

    market_data = {
        'symbol': symbol,
        'timestamp': current_row.name.strftime('%Y-%m-%d %H:%M:%S'),
        'price': current_row['close'],
        'signal_type': signal_type,
        'signal_strength': signal_strength,
        'volume_confirmation': volume_confirmation,
        'williams_confirmation': williams_confirmation,
        # 技术指标数据
        'EMA8': current_row['EMA8'],
        'EMA21': current_row['EMA21'],
        'EMA55': current_row['EMA55'],
        'EMA125': current_row['EMA125'],
        'K_value': current_row['K'],
        'D_value': current_row['D'],
        'J_value': current_row['J'],
        'DIF': current_row['DIF'],
        'DEA': current_row['DEA'],
        'MACD': current_row['MACD'],
        'volume': current_row['volume'],
        'vol_ma10': current_row['VOL_MA10'],
        'vol_ratio': current_row['volume'] / current_row['VOL_MA10'] if current_row['VOL_MA10'] > 0 else 0,
        # 威廉指标数据
        'ZLS': current_row['ZLS'],
        'CZX': current_row['CZX'],
        'HLB': current_row['HLB'],
        'williams_c_signal': williams_c_signal,
        'williams_p_signal': williams_p_signal,
        # 波动率数据（简化，移除VIX）
        'stock_volatility': stock_volatility,
        'vol_percentile': vol_percentile,
        'volatility_source': "stock_volatility",
        # 趋势分析
        'trend_short': "上涨" if current_row['EMA8'] > current_row['EMA21'] else "下跌",
        'trend_medium': "上涨" if current_row['EMA21'] > current_row['EMA55'] else "下跌",
        'trend_long': "上涨" if current_row['EMA55'] > current_row['EMA125'] else "下跌",
        'signal': signal_type
    }

    if signal_detected:
        confirmation_info = f" ({williams_confirmation})" if williams_confirmation else ""
        logger.info(f"{symbol} 检测到信号: {signal_type} (强度: {signal_strength}){confirmation_info}")
    
    return market_data, signal_detected

# --- 任务调度 ---

def is_us_trading_hours(test_mode=False):
    """
    /**
     * @description 判断当前是否在美国股市的主要交易时段（美东时间 9:30 - 16:00）。
     * @param {boolean} test_mode - 测试模式，如果为True则忽略交易时间限制
     * @returns {boolean} 如果是交易时间则返回True。
     */
    """
    if test_mode:
        logger.info("测试模式：忽略交易时间限制")
        return True
        
    try:
        from pytz import timezone
    except ImportError:
        logger.warning("未安装pytz模块，使用简化时间判断")
        # 如果没有时区库，则使用一个保守的UTC时间窗口
        now_utc = datetime.now().replace(tzinfo=None)
        # 9:30 ET is 13:30 or 14:30 UTC. 16:00 ET is 20:00 or 21:00 UTC.
        return 13 <= now_utc.hour <= 22

    et = timezone('US/Eastern')
    now_et = datetime.now(et)
    
    # 只在工作日运行
    if now_et.weekday() >= 5:
        logger.info("当前为周末，非交易日")
        return False
    
    # 交易时段: 9:30-16:00
    trade_start = now_et.replace(hour=9, minute=30, second=0, microsecond=0).time()
    trade_end = now_et.replace(hour=16, minute=0, second=0, microsecond=0).time()
    
    current_time = now_et.time()
    is_trading = trade_start <= current_time <= trade_end
    
    if not is_trading:
        logger.info(f"当前时间 {current_time} 不在交易时段 {trade_start}-{trade_end}")
    
    return is_trading


def job(test_mode=False, force_run=False):
    """
    /**
     * @description 定时执行的任务，分析所有配置的股票。
     * @param {boolean} test_mode - 测试模式，忽略交易时间限制
     * @param {boolean} force_run - 强制运行一次
     */
    """
    if not test_mode and not force_run and not is_us_trading_hours():
        logger.info("当前非美股交易时段，跳过运行。")
        return
        
    start_time = time.time()
    logger.info("开始美股策略定时任务...")
    
    ticker_map = get_us_stock_ticker_map()
    if not ticker_map:
        logger.error("无法获取ticker映射，任务中止。")
        return

    for symbol in CONFIG['symbols']:
        ak_code = ticker_map.get(symbol)
        if not ak_code:
            logger.warning(f"在ticker映射中找不到 {symbol}，跳过。")
            continue
        
        try:
            market_data, signal_detected = analyze_stock_data(symbol, ak_code)
            if signal_detected and market_data:
                logger.info(f"为 {symbol} 检测到信号，正在获取AI导师分析...")
                master_analysis = get_master_analysis(market_data)
                logger.info(f"AI导师分析结果 for {symbol}:\n{master_analysis}")
                # 发送企业微信通知
                send_qw_wrapper(f"--- AI导师美股期权分析: {symbol} ---\n{master_analysis}")
            elif market_data:
                logger.info(f"{symbol} 未检测到明确交易信号。")
        except Exception as e:
            logger.error(f"分析 {symbol} 数据时出错: {e}", exc_info=True)
            
    elapsed_time = time.time() - start_time
    logger.info(f"任务完成，耗时: {elapsed_time:.2f}秒")

def run_scheduler(test_mode=False):
    """
    /**
     * @description 启动定时任务调度器。
     * @param {boolean} test_mode - 测试模式
     */
    """
    logger.info("启动美股策略定时任务调度器...")
    
    schedule.every(int(CONFIG['period'])).minutes.do(job, test_mode=test_mode)
    
    logger.info("立即执行一次任务...")
    job(test_mode=test_mode, force_run=True)
    
    logger.info(f"定时任务已设置，每{CONFIG['period']}分钟运行一次。")
    
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到中断信号，退出程序。")
            break
        except Exception as e:
            logger.error(f"调度器出错: {str(e)}")
            send_qw_wrapper(f"美股策略调度器出错: {str(e)}")
            time.sleep(60)

def init():
    """
    /**
     * @description 初始化程序，加载配置和检查API密钥。
     */
    """
    # 加载 .env 文件中的环境变量
    if not load_dotenv():
        logger.warning(".env 文件未找到。请确保在项目根目录下创建了 .env 文件，并已设置 DEEPSEEK_API_KEY。")
        logger.warning("例如: DEEPSEEK_API_KEY=\"your_key_here\"")

    load_master_agent_prompt()
    
    if not os.getenv("DEEPSEEK_API_KEY"):
        logger.error("关键错误: DEEPSEEK_API_KEY 环境变量未设置。程序无法继续。")
        logger.info("请在终端使用 'export DEEPSEEK_API_KEY=\"your_key_here\"' 命令设置。")
        sys.exit(1)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='美股期权策略跟踪系统')
    parser.add_argument('--test-mode', action='store_true', help='测试模式：忽略交易时间限制')
    parser.add_argument('--force-run', action='store_true', help='强制运行一次分析')
    parser.add_argument('--run-once', action='store_true', help='运行一次性分析（兼容旧参数）')
    
    args = parser.parse_args()
    
    try:
        init()
        
        if not MASTER_AGENT_PROMPT:
            logger.error("AI导师配置未能加载，程序退出。")
            sys.exit(1)

        if args.run_once or args.force_run:
            logger.info("运行一次性分析...")
            job(test_mode=args.test_mode, force_run=True)
            logger.info("一次性分析完成。")
        else:
            run_scheduler(test_mode=args.test_mode)
            
    except Exception as e:
        error_msg = f"程序主流程运行出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        try:
            send_qw_wrapper(f"美股策略程序运行出错: {str(e)}")
        except Exception as notify_e:
            logger.error(f"发送错误通知也失败了: {notify_e}") 