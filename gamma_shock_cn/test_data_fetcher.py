#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取器测试脚本
测试DataFetcher类的各种功能
"""

import sys
import pandas as pd
from datetime import datetime
from data.data_fetcher import DataFetcher

def test_data_fetcher():
    """
    测试数据获取器的各种功能
    """
    print("=" * 60)
    print("开始测试数据获取器功能")
    print("=" * 60)
    
    # 创建数据获取器实例
    fetcher = DataFetcher()
    
    # 测试标的列表
    test_symbols = ['zz1000', 'zz500', 'cyb']
    
    for symbol in test_symbols:
        print(f"\n{'='*40}")
        print(f"测试标的: {symbol}")
        print(f"{'='*40}")
        
        # 1. 测试日线数据获取
        print(f"\n1. 获取{symbol}日线数据...")
        daily_data = fetcher.get_daily_data(symbol)
        if daily_data is not None:
            print(f"   ✓ 成功获取日线数据，共{len(daily_data)}条记录")
            print(f"   数据列: {list(daily_data.columns)}")
            print(f"   最新数据:")
            print(daily_data.tail(2))
        else:
            print(f"   ✗ 获取日线数据失败")
        
        # 2. 测试5分钟线数据获取
        print(f"\n2. 获取{symbol} 5分钟线数据...")
        minute_5_data = fetcher.get_minute_data(symbol, "5", days_back=7)
        if minute_5_data is not None:
            print(f"   ✓ 成功获取5分钟线数据，共{len(minute_5_data)}条记录")
            print(f"   数据列: {list(minute_5_data.columns)}")
            print(f"   最新数据:")
            print(minute_5_data.tail(2))
        else:
            print(f"   ✗ 获取5分钟线数据失败")
        
        # 3. 测试60分钟线数据获取
        print(f"\n3. 获取{symbol} 60分钟线数据...")
        minute_60_data = fetcher.get_minute_data(symbol, "60", days_back=7)
        if minute_60_data is not None:
            print(f"   ✓ 成功获取60分钟线数据，共{len(minute_60_data)}条记录")
            print(f"   数据列: {list(minute_60_data.columns)}")
            print(f"   最新数据:")
            print(minute_60_data.tail(2))
        else:
            print(f"   ✗ 获取60分钟线数据失败")
        
        # 4. 测试历史波动率数据获取
        print(f"\n4. 获取{symbol}历史波动率数据...")
        hist_vol_data = fetcher.get_volatility_data(symbol, "historical")
        if hist_vol_data is not None:
            print(f"   ✓ 成功获取历史波动率数据，共{len(hist_vol_data)}条记录")
            print(f"   数据列: {list(hist_vol_data.columns)}")
            print(f"   最新数据:")
            print(hist_vol_data.tail(2))
        else:
            print(f"   ✗ 获取历史波动率数据失败")
        
        # 5. 测试分时波动率数据获取
        print(f"\n5. 获取{symbol}分时波动率数据...")
        intraday_vol_data = fetcher.get_volatility_data(symbol, "intraday")
        if intraday_vol_data is not None:
            print(f"   ✓ 成功获取分时波动率数据，共{len(intraday_vol_data)}条记录")
            print(f"   数据列: {list(intraday_vol_data.columns)}")
            print(f"   最新数据:")
            print(intraday_vol_data.tail(2))
        else:
            print(f"   ✗ 获取分时波动率数据失败")
    
    # 6. 测试期权统计数据获取
    print(f"\n{'='*40}")
    print("测试期权统计数据获取")
    print(f"{'='*40}")
    
    option_stats = fetcher.get_option_stats_data("both")
    if option_stats:
        for exchange, data in option_stats.items():
            if data is not None:
                print(f"\n{exchange.upper()}期权统计数据:")
                print(f"   ✓ 成功获取，共{len(data)}条记录")
                print(f"   数据列: {list(data.columns)}")
                print(f"   数据预览:")
                print(data.head(2))
            else:
                print(f"\n{exchange.upper()}期权统计数据: ✗ 获取失败")
    
    # 7. 测试期权看板数据获取
    print(f"\n{'='*40}")
    print("测试期权看板数据获取")
    print(f"{'='*40}")
    
    option_board = fetcher.get_option_finance_board()
    if option_board is not None:
        print(f"   ✓ 成功获取期权看板数据，共{len(option_board)}条记录")
        print(f"   数据列: {list(option_board.columns)}")
        print(f"   数据预览:")
        print(option_board.head(2))
    else:
        print(f"   ✗ 获取期权看板数据失败")
    
    # 8. 测试获取所有数据功能
    print(f"\n{'='*40}")
    print("测试获取所有数据功能")
    print(f"{'='*40}")
    
    all_data = fetcher.get_all_data('zz1000', include_minute=True, minute_periods=["5", "60"])
    print(f"\n获取zz1000所有数据结果:")
    for key, value in all_data.items():
        if isinstance(value, pd.DataFrame):
            print(f"   {key}: DataFrame with {len(value)} records")
        elif isinstance(value, dict):
            print(f"   {key}: Dict with keys {list(value.keys())}")
            for sub_key, sub_value in value.items():
                if isinstance(sub_value, pd.DataFrame):
                    print(f"     {sub_key}: DataFrame with {len(sub_value)} records")
                else:
                    print(f"     {sub_key}: {type(sub_value)}")
        else:
            print(f"   {key}: {type(value)}")
    
    print(f"\n{'='*60}")
    print("数据获取器测试完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    try:
        test_data_fetcher()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()