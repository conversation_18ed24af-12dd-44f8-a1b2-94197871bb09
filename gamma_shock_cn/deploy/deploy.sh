#!/bin/bash
# Gamma Shock CN 部署脚本
# 用于自动化部署到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="gamma-shock-cn"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="./backups"
LOG_DIR="./logs"

# 检查必要的工具
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "./data/cache"
    mkdir -p "./config"
    
    log_success "目录创建完成"
}

# 检查环境配置文件
check_env_file() {
    log_info "检查环境配置文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "环境配置文件 $ENV_FILE 不存在，创建默认配置"
        cat > "$ENV_FILE" << EOF
# Gamma Shock CN 环境配置

# 数据库配置
POSTGRES_PASSWORD=postgres123
DATABASE_URL=***********************************************/gamma_shock

# Redis配置
REDIS_PASSWORD=redis123
REDIS_URL=redis://redis:6379/0

# 监控配置
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin123

# 应用配置
ENVIRONMENT=production
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-here

# API配置
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# 数据源配置
DATA_SOURCE=tushare
TUSHARE_TOKEN=your-tushare-token
EOD_API_KEY=your-eod-api-key
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key
EOF
        log_warning "请编辑 $ENV_FILE 文件，配置正确的参数"
    else
        log_success "环境配置文件检查完成"
    fi
}

# 备份数据
backup_data() {
    log_info "备份现有数据..."
    
    BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP.tar.gz"
    
    if [ -d "./data" ]; then
        tar -czf "$BACKUP_FILE" ./data ./logs 2>/dev/null || true
        log_success "数据备份完成: $BACKUP_FILE"
    else
        log_info "没有现有数据需要备份"
    fi
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 首先启动基础服务
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 启动主应用
    docker-compose up -d gamma-shock
    
    log_success "服务启动完成"
}

# 启动监控服务
start_monitoring() {
    log_info "启动监控服务..."
    
    docker-compose --profile monitoring up -d
    
    log_success "监控服务启动完成"
}

# 启动日志服务
start_logging() {
    log_info "启动日志服务..."
    
    docker-compose --profile logging up -d
    
    log_success "日志服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查容器状态
    if docker-compose ps | grep -q "Up"; then
        log_success "容器运行正常"
    else
        log_error "部分容器未正常运行"
        docker-compose ps
        return 1
    fi
    
    # 检查应用健康状态
    sleep 5
    if docker-compose exec -T gamma-shock python -c "import sys; sys.exit(0)" 2>/dev/null; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败，请检查日志"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_info "部署信息:"
    echo "=========================================="
    echo "项目名称: $PROJECT_NAME"
    echo "部署时间: $(date)"
    echo "容器状态:"
    docker-compose ps
    echo "=========================================="
    echo "访问地址:"
    echo "  - 应用: http://localhost:8000"
    echo "  - Grafana: http://localhost:3000 (admin/admin123)"
    echo "  - Prometheus: http://localhost:9090"
    echo "  - Kibana: http://localhost:5601"
    echo "=========================================="
    echo "日志查看:"
    echo "  docker-compose logs -f gamma-shock"
    echo "  docker-compose logs -f postgres"
    echo "  docker-compose logs -f redis"
    echo "=========================================="
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    # 这里可以添加清理逻辑
    log_success "清理完成"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    log_success "服务重启完成"
}

# 查看日志
view_logs() {
    local service=${1:-gamma-shock}
    log_info "查看 $service 服务日志..."
    docker-compose logs -f "$service"
}

# 更新服务
update_services() {
    log_info "更新服务..."
    
    # 备份数据
    backup_data
    
    # 拉取最新代码
    git pull origin main
    
    # 重新构建镜像
    build_images
    
    # 重启服务
    docker-compose up -d --force-recreate
    
    # 健康检查
    health_check
    
    log_success "服务更新完成"
}

# 主函数
main() {
    local command=${1:-deploy}
    
    case $command in
        "deploy")
            log_info "开始部署 $PROJECT_NAME..."
            check_dependencies
            create_directories
            check_env_file
            backup_data
            build_images
            start_services
            health_check
            show_deployment_info
            log_success "部署完成！"
            ;;
        "deploy-full")
            log_info "开始完整部署 $PROJECT_NAME (包含监控和日志)..."
            check_dependencies
            create_directories
            check_env_file
            backup_data
            build_images
            start_services
            start_monitoring
            start_logging
            health_check
            show_deployment_info
            log_success "完整部署完成！"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "update")
            update_services
            ;;
        "logs")
            view_logs $2
            ;;
        "status")
            docker-compose ps
            ;;
        "cleanup")
            cleanup
            ;;
        "health")
            health_check
            ;;
        "backup")
            backup_data
            ;;
        *)
            echo "使用方法: $0 {deploy|deploy-full|stop|restart|update|logs|status|cleanup|health|backup}"
            echo ""
            echo "命令说明:"
            echo "  deploy      - 基础部署（应用+数据库+缓存）"
            echo "  deploy-full - 完整部署（包含监控和日志服务）"
            echo "  stop        - 停止所有服务"
            echo "  restart     - 重启服务"
            echo "  update      - 更新服务"
            echo "  logs        - 查看日志 (可指定服务名)"
            echo "  status      - 查看服务状态"
            echo "  cleanup     - 清理临时文件"
            echo "  health      - 健康检查"
            echo "  backup      - 备份数据"
            exit 1
            ;;
    esac
}

# 捕获退出信号
trap cleanup EXIT

# 执行主函数
main "$@"