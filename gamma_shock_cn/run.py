#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gamma Shock 系统启动脚本

快速启动 A股期权策略跟踪系统

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import GammaShockApp
from utils import setup_logging
from config.settings import settings


def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🚀 Gamma Shock 系统                      ║
║                  A股期权策略跟踪系统                          ║
║                                                              ║
║  📊 实时监控 | 🤖 AI分析 | 📧 智能通知 | 📈 策略优化        ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查配置
    validation = settings.validate_config()
    if not validation['valid']:
        print("❌ 配置验证失败:")
        for error in validation['errors']:
            print(f"   - {error}")
        print("\n💡 请检查环境变量配置")
        sys.exit(1)
    
    if validation['warnings']:
        print("⚠️  配置警告:")
        for warning in validation['warnings']:
            print(f"   - {warning}")
    
    print("✅ 配置验证通过")
    
    # 检查必要目录
    required_dirs = ['data', 'logs']
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"📁 创建目录: {dir_name}")
    
    print("✅ 环境检查完成")

async def start_system():
    """启动系统"""
    try:
        # 打印横幅
        print_banner()
        
        # 检查环境
        check_environment()
        
        # 设置日志
        setup_logging(settings.system.LOGGING_CONFIG)
        
        print("\n🚀 启动 Gamma Shock 系统...")
        
        # 创建并启动应用
        app = GammaShockApp()
        await app.start()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  收到停止信号，正在关闭系统...")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        sys.exit(1)
    finally:
        print("\n👋 Gamma Shock 系统已停止")


def main():
    """主函数"""
    asyncio.run(start_system())

if __name__ == "__main__":
    main()