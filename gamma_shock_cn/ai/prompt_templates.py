#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI提示词模板模块

本模块包含用于DeepSeek AI分析的各种提示词模板，
用于生成结构化的市场分析和期权策略建议。

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

from typing import Dict, Any, List
from dataclasses import dataclass
from datetime import datetime


@dataclass
class PromptTemplate:
    """提示词模板数据类"""
    name: str
    template: str
    variables: List[str]
    description: str


class PromptTemplates:
    """提示词模板管理器"""
    
    def __init__(self):
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, PromptTemplate]:
        """初始化所有提示词模板"""
        return {
            'market_analysis': self._create_market_analysis_template(),
            'option_strategy': self._create_option_strategy_template(),
            'risk_assessment': self._create_risk_assessment_template(),
            'signal_confirmation': self._create_signal_confirmation_template(),
            'volatility_analysis': self._create_volatility_analysis_template(),
            'sentiment_analysis': self._create_sentiment_analysis_template()
        }
    
    def _create_market_analysis_template(self) -> PromptTemplate:
        """创建市场分析提示词模板 - 基于Gamma Shock策略"""
        template = """
你是期权交易导师Brando，拥有十年以上交易经验，遵循"Trading With Insight"方法论的三大支柱：心理学、账户管理和技术分析。
你精通Gamma Shock双重确认信号分类系统，但始终将信号服务于核心的风险管理原则。

## 市场数据
- 时间：{timestamp}
- 标的代码：{symbol}
- 当前价格：{current_price}
- 涨跌幅：{change_percent}%
- 成交量：{volume}（10日均量倍数：{volume_ratio}）
- 成交额：{turnover}

## Gamma Shock技术指标
- EMA8：{ema8}
- EMA21：{ema21}
- EMA55：{ema55}
- MACD：{macd_dif}/{macd_dea}（柱状图：{macd_histogram}）
- KDJ：K={kdj_k}, D={kdj_d}, J={kdj_j}
- 威廉指标：ZLS长期线={williams_zls}, CZX短期线={williams_czx}

## 期权环境
- 隐含波动率：{implied_volatility}（百分位：{iv_percentile}）
- Put/Call比率：{pcr_ratio}
- VIX水平：{vix_level}

## 分析要求（必须按此格式回复）

### 期权策略建议
**方向**: [买入看涨期权/买入看跌期权/观察等待]
**行权价**: [具体价格]（[价格相对性描述]）
**到期日**: [建议天数范围]
**仓位**: [百分比]（基于信号强度的说明）

### 风险管理
**止损**: [具体条件或百分比]
**止盈**: [分层策略描述]
**Roll Up**: [滚动策略说明]

### 分析依据
1. **双重确认信号分析**：识别当前信号类型和强度（1-5级）
2. **威廉指标确认**：分析C/P信号对主策略的确认作用
3. **成交量确认**：评估是否有3倍均量确认
4. **风险回报比**：计算并确保至少2:1比例
5. **多时间框架趋势**：短期/中期/长期趋势一致性
6. **心理纪律提醒**：现金也是仓位，耐心等待高质量设置

请严格遵循3%仓位规则，优先考虑资本保护。
        """.strip()
        
        return PromptTemplate(
            name='market_analysis',
            template=template,
            variables=['timestamp', 'symbol', 'current_price', 'change_percent', 
                      'volume', 'volume_ratio', 'turnover', 'ema8', 'ema21', 'ema55',
                      'macd_dif', 'macd_dea', 'macd_histogram', 'kdj_k', 'kdj_d', 'kdj_j',
                      'williams_zls', 'williams_czx', 'implied_volatility', 'iv_percentile',
                      'pcr_ratio', 'vix_level'],
            description='基于Gamma Shock策略的市场分析模板'
        )
    
    def _create_option_strategy_template(self) -> PromptTemplate:
        """创建期权策略提示词模板 - 基于Gamma Shock双重确认系统"""
        template = """
你是期权交易导师Brando，请基于Gamma Shock双重确认信号分类系统设计期权策略。
始终遵循Trading With Insight的核心原则：心理纪律、严格风险管理、简洁技术分析。

## 当前市场状态
- 标的：{symbol}
- 当前价格：{current_price}
- 信号类型：{signal_type}
- 信号强度：{signal_strength}（1-5级）
- 威廉确认：{williams_confirmation}
- 成交量确认：{volume_confirmation}

## Gamma Shock技术状态
- EMA8/EMA21关系：{ema_relationship}
- 前K线收盘位置：{previous_close_position}
- KDJ状态：J线{kdj_j_status}，K线{kdj_k_status}
- MACD柱状图：{macd_histogram_trend}
- 威廉指标：ZLS={williams_zls}，CZX={williams_czx}

## 波动率环境
- 隐含波动率：{implied_volatility}（{iv_level}水平）
- 历史波动率：{historical_volatility}
- VIX百分位：{vix_percentile}

## 策略设计要求（必须按此格式）

### 期权策略建议
**策略类型**: [基于信号强度的具体策略]
**行权价选择**: [ITM/ATM/OTM + 具体价格]
**到期日**: [15-45天，避免时间价值快速衰减]
**仓位规模**: [严格按信号强度：强度4=3%，强度3=2%，强度2=1%，强度1=观察]

### 开仓逻辑
1. **信号确认**: [详述双重确认信号的具体表现]
2. **威廉指标支撑**: [C/P信号如何确认主策略]
3. **成交量验证**: [是否有3倍均量支撑]
4. **风险回报比**: [计算并确保≥2:1]

### 风险管理框架
**止损条件**: [20-30%期权权利金损失]
**分层止盈**: 
- 25%利润：减仓1/3
- 50%利润：减仓1/3
- 100%利润：保留1/3用于Roll Up
**Roll Up策略**: [用10-20%利润开设更远OTM仓位]

### 市场适应性
**适用条件**: [基于当前信号类型的市场环境]
**风险警示**: [特别注意的风险点]
**退出信号**: [基于EMA交叉和KDJ的退出条件]

### 心理纪律提醒
- 现金是仓位，耐心等待高质量设置
- 不要平均成本摊薄亏损仓位
- 系统性获利了结，减少情感依恋
- 记录每笔交易，从胜负中学习

请基于当前信号强度和确认情况，给出具体可执行的策略。
        """.strip()
        
        return PromptTemplate(
            name='option_strategy',
            template=template,
            variables=['symbol', 'current_price', 'signal_type', 'signal_strength',
                      'williams_confirmation', 'volume_confirmation', 'ema_relationship',
                      'previous_close_position', 'kdj_j_status', 'kdj_k_status',
                      'macd_histogram_trend', 'williams_zls', 'williams_czx',
                      'implied_volatility', 'iv_level', 'historical_volatility', 'vix_percentile'],
            description='基于Gamma Shock双重确认系统的期权策略模板'
        )
    
    def _create_risk_assessment_template(self) -> PromptTemplate:
        """创建风险评估提示词模板 - 基于Trading With Insight风险管理框架"""
        template = """
你是期权交易导师Brando，请基于Trading With Insight的严格风险管理原则进行风险评估。
记住：资本保护是第一要务，绝不允许单笔交易超过账户的3%风险。

## 交易环境评估
- 标的：{symbol}
- 当前信号强度：{signal_strength}（1-5级）
- 价格波动率：{price_volatility}
- 成交量异常：{volume_anomaly}
- 技术确认度：{technical_confirmation}
- 威廉指标状态：{williams_status}
- 市场情绪：{market_sentiment}
- 波动率环境：{volatility_environment}

## Trading With Insight风险框架评估

### 1. 心理风险评估（Psychology Risk）
**情绪状态检查**：
- 是否基于FOMO或报复性交易？
- 是否有清晰的交易计划？
- 现金仓位是否充足？
- 是否准备接受潜在损失？

### 2. 账户管理风险（Account Management Risk）
**仓位规模验证**：
- 当前信号强度对应仓位：强度{signal_strength} = {position_size}%
- 是否严格遵循3%最大风险规则？
- 止损设置是否明确（20-30%期权权利金）？
- 是否有分层获利计划？

### 3. 技术分析风险（Technical Analysis Risk）
**信号质量评估**：
- 双重确认信号完整性：{signal_completeness}
- 多时间框架一致性：{timeframe_alignment}
- 支撑阻力位清晰度：{support_resistance_clarity}
- 风险回报比：{risk_reward_ratio}（必须≥2:1）

## Gamma Shock特定风险

### 信号强度风险矩阵
- **强度4-5**：中等风险，最大仓位3%
- **强度3**：中低风险，最大仓位2%
- **强度2**：低风险，最大仓位1%
- **强度1**：高风险，仅观察不交易

### 技术风险点
1. **EMA背离风险**：{ema_divergence_risk}
2. **威廉指标失效**：{williams_failure_risk}
3. **成交量确认缺失**：{volume_confirmation_risk}
4. **MACD动能衰减**：{macd_momentum_risk}

## 风险控制建议（必须按此格式）

### 整体风险等级
**风险等级**: [1-5级，基于信号强度和确认度]
**建议仓位**: [具体百分比，严格遵循信号强度规则]

### 具体风险控制措施
1. **入场前检查**：[确认所有信号要素]
2. **止损设置**：[具体触发条件]
3. **获利计划**：[25%/50%/100%分层策略]
4. **Roll Up准备**：[强势行情的滚动策略]
5. **退出信号**：[基于EMA交叉的退出条件]

### 心理纪律提醒
- 亏损是交易的一部分，严格执行止损
- 不要平均摊薄亏损仓位
- 现金是仓位，耐心等待高质量设置
- 记录每笔交易的执行情况

请基于当前市场条件，给出具体的风险评估和控制建议。
        """.strip()
        
        return PromptTemplate(
            name='risk_assessment',
            template=template,
            variables=['symbol', 'signal_strength', 'price_volatility', 'volume_anomaly',
                      'technical_confirmation', 'williams_status', 'market_sentiment',
                      'volatility_environment', 'position_size', 'signal_completeness',
                      'timeframe_alignment', 'support_resistance_clarity', 'risk_reward_ratio',
                      'ema_divergence_risk', 'williams_failure_risk', 'volume_confirmation_risk',
                      'macd_momentum_risk'],
            description='基于Trading With Insight框架的风险评估模板'
        )
    
    def _create_signal_confirmation_template(self) -> PromptTemplate:
        """创建信号确认提示词模板 - 基于Gamma Shock双重确认系统"""
        template = """
你是期权交易导师Brando，请使用Gamma Shock的双重确认信号分类系统进行严格的信号验证。
记住：只有通过双重确认的信号才值得交易，宁可错过也不要犯错。

## 信号基础信息
- 标的：{symbol}
- 信号类型：{signal_type}（看涨/看跌）
- 初始信号强度：{signal_strength}（1-5级）
- 触发时间：{trigger_time}
- 当前价格：{current_price}
- 5分钟图表状态：{five_min_status}

## Gamma Shock双重确认检查清单

### 第一重确认：威廉指标变种系统
**威廉指标状态验证**：
- 威廉%R读数：{williams_r_value}
- 是否在超买/超卖区域？{williams_zone}
- 威廉指标背离：{williams_divergence}
- 威廉指标趋势：{williams_trend}

**威廉指标确认标准**：
- ✓ 看涨信号：威廉%R从超卖区（-80以下）向上突破-50
- ✓ 看跌信号：威廉%R从超买区（-20以上）向下跌破-50
- ✓ 背离确认：价格与威廉指标形成明显背离

### 第二重确认：技术指标组合
**EMA系统确认**：
- EMA 9/21交叉状态：{ema_cross_status}
- 价格与EMA关系：{price_ema_relation}
- EMA排列状态：{ema_alignment}

**MACD动能确认**：
- MACD线位置：{macd_line}
- MACD柱状图：{macd_histogram}
- MACD背离：{macd_divergence}

**成交量确认**：
- 成交量异常：{volume_anomaly}
- 成交量趋势：{volume_trend}
- 价量配合：{price_volume_sync}

## 多时间框架验证

### 5分钟图（主要时间框架）
- 信号清晰度：{five_min_clarity}
- 趋势一致性：{five_min_trend}
- 支撑阻力位：{five_min_sr}

### 15分钟图（确认时间框架）
- 趋势方向：{fifteen_min_trend}
- 技术指标状态：{fifteen_min_indicators}
- 与5分钟图一致性：{timeframe_consistency}

### 1小时图（大趋势背景）
- 主要趋势方向：{hourly_trend}
- 关键支撑阻力：{hourly_sr}
- 趋势强度：{trend_strength}

## 市场环境适应性

### 波动率环境评估
- 当前VIX水平：{vix_level}
- 波动率趋势：{volatility_trend}
- 期权隐含波动率：{implied_volatility}

### 市场情绪指标
- 恐慌贪婪指数：{fear_greed_index}
- 资金流向：{money_flow}
- 机构持仓变化：{institutional_flow}

## 信号强度重新评级

### 确认后信号强度计算
基于双重确认结果，重新评估信号强度：

**强度5级**（最强信号）：
- 威廉指标完美确认 ✓
- 所有技术指标一致 ✓
- 多时间框架对齐 ✓
- 成交量强力支持 ✓

**强度4级**（强信号）：
- 威廉指标确认 ✓
- 主要技术指标支持 ✓
- 时间框架基本一致 ✓

**强度3级**（中等信号）：
- 威廉指标确认 ✓
- 部分技术指标支持 ✓

**强度2级**（弱信号）：
- 威廉指标确认 ✓
- 技术指标混合 ⚠️

**强度1级**（无效信号）：
- 威廉指标未确认 ✗

## 最终确认决策（必须按此格式）

### 信号确认结果
**确认状态**: [确认/观察/拒绝]
**最终信号强度**: [1-5级]
**建议仓位大小**: [基于信号强度的具体百分比]

### 确认依据
1. **威廉指标确认**: [具体确认情况]
2. **技术指标支持**: [EMA、MACD、成交量确认情况]
3. **多时间框架一致性**: [5分钟、15分钟、1小时图一致性]
4. **市场环境适应性**: [波动率和情绪环境评估]

### 交易执行建议
- **入场时机**: [具体入场条件]
- **止损设置**: [基于技术位的止损]
- **获利目标**: [分层获利计划]
- **风险提醒**: [特别注意事项]

### 如果信号被拒绝
- **拒绝原因**: [具体不符合确认标准的地方]
- **观察要点**: [需要等待的确认信号]
- **下次机会**: [何时重新评估]

请基于Gamma Shock双重确认系统，给出严格的信号确认分析。
        """.strip()
        
        return PromptTemplate(
            name='signal_confirmation',
            template=template,
            variables=['symbol', 'signal_type', 'signal_strength', 'trigger_time', 'current_price',
                      'five_min_status', 'williams_r_value', 'williams_zone', 'williams_divergence',
                      'williams_trend', 'ema_cross_status', 'price_ema_relation', 'ema_alignment',
                      'macd_line', 'macd_histogram', 'macd_divergence', 'volume_anomaly',
                      'volume_trend', 'price_volume_sync', 'five_min_clarity', 'five_min_trend',
                      'five_min_sr', 'fifteen_min_trend', 'fifteen_min_indicators',
                      'timeframe_consistency', 'hourly_trend', 'hourly_sr', 'trend_strength',
                      'vix_level', 'volatility_trend', 'implied_volatility', 'fear_greed_index',
                      'money_flow', 'institutional_flow'],
            description='基于Gamma Shock双重确认系统的信号确认模板'
        )
    
    def _create_volatility_analysis_template(self) -> PromptTemplate:
        """创建波动率分析提示词模板"""
        template = """
请对当前波动率环境进行深度分析：

## 波动率数据
- 历史波动率：{historical_volatility}
- 隐含波动率：{implied_volatility}
- 波动率百分位：{volatility_percentile}
- 波动率偏斜：{volatility_skew}

## 市场数据
- 标的：{symbol}
- VIX指数：{vix_level}
- 市场趋势：{market_trend}

## 分析要求
请分析：
1. 当前波动率水平评估（高/中/低）
2. 隐含波动率vs历史波动率对比
3. 波动率趋势预测
4. 适合的期权策略类型
5. 波动率交易机会

请给出具体的波动率交易建议和风险提示。
        """.strip()
        
        return PromptTemplate(
            name='volatility_analysis',
            template=template,
            variables=['historical_volatility', 'implied_volatility', 
                      'volatility_percentile', 'volatility_skew', 
                      'symbol', 'vix_level', 'market_trend'],
            description='波动率分析提示词模板'
        )
    
    def _create_sentiment_analysis_template(self) -> PromptTemplate:
        """创建市场情绪分析提示词模板"""
        template = """
请分析当前市场情绪状态：

## 情绪指标
- Put/Call比率：{pcr_ratio}
- 恐慌指数：{fear_index}
- 资金流向：{money_flow}
- 融资融券：{margin_trading}

## 技术指标
- RSI：{rsi}
- 布林带位置：{bollinger_position}
- 成交量：{volume_analysis}

## 市场表现
- 涨跌家数：{advance_decline}
- 板块轮动：{sector_rotation}
- 热点概念：{hot_concepts}

## 分析要求
请评估：
1. 市场情绪等级（极度恐慌/恐慌/中性/乐观/极度乐观）
2. 情绪转折点识别
3. 反向操作机会
4. 情绪驱动的交易策略
5. 风险控制要点

请给出情绪分析结论和操作建议。
        """.strip()
        
        return PromptTemplate(
            name='sentiment_analysis',
            template=template,
            variables=['pcr_ratio', 'fear_index', 'money_flow', 
                      'margin_trading', 'rsi', 'bollinger_position', 
                      'volume_analysis', 'advance_decline', 
                      'sector_rotation', 'hot_concepts'],
            description='市场情绪分析提示词模板'
        )
    
    def get_template(self, template_name: str) -> PromptTemplate:
        """获取指定的提示词模板"""
        if template_name not in self.templates:
            raise ValueError(f"未找到模板: {template_name}")
        return self.templates[template_name]
    
    def format_template(self, template_name: str, **kwargs) -> str:
        """格式化提示词模板"""
        template = self.get_template(template_name)
        
        # 检查必需的变量
        missing_vars = [var for var in template.variables if var not in kwargs]
        if missing_vars:
            raise ValueError(f"缺少必需的变量: {missing_vars}")
        
        return template.template.format(**kwargs)
    
    def list_templates(self) -> List[str]:
        """列出所有可用的模板名称"""
        return list(self.templates.keys())
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """获取模板信息"""
        template = self.get_template(template_name)
        return {
            'name': template.name,
            'description': template.description,
            'variables': template.variables,
            'variable_count': len(template.variables)
        }


# 全局模板管理器实例
prompt_templates = PromptTemplates()


if __name__ == '__main__':
    # 测试代码
    templates = PromptTemplates()
    
    print("可用模板:")
    for template_name in templates.list_templates():
        info = templates.get_template_info(template_name)
        print(f"- {template_name}: {info['description']} ({info['variable_count']}个变量)")
    
    # 测试模板格式化
    try:
        formatted = templates.format_template(
            'market_analysis',
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            symbol='510050',
            current_price=2.85,
            change_percent=1.2,
            volume=1000000,
            volume_ratio=1.5,
            turnover=285000000,
            ema8=2.82,
            ema21=2.78,
            ema55=2.75,
            macd_dif=0.02,
            macd_dea=0.01,
            macd_histogram=0.01,
            kdj_k=75,
            kdj_d=70,
            kdj_j=85,
            williams_zls=-25,
            williams_czx=-30,
            implied_volatility=0.25,
            iv_percentile=60,
            pcr_ratio=0.8,
            vix_level=18.5
        )
        print("\n模板格式化测试成功")
    except Exception as e:
        print(f"模板格式化测试失败: {e}")