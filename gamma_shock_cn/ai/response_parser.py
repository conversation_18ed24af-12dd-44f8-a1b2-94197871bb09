#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI响应解析器模块

本模块负责解析DeepSeek AI的响应内容，
提取结构化的分析结果和操作建议。

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

import re
import json
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum


class AnalysisType(Enum):
    """分析类型枚举"""
    MARKET_ANALYSIS = "market_analysis"
    OPTION_STRATEGY = "option_strategy"
    RISK_ASSESSMENT = "risk_assessment"
    SIGNAL_CONFIRMATION = "signal_confirmation"
    VOLATILITY_ANALYSIS = "volatility_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"


class SignalLevel(Enum):
    """Gamma Shock信号等级枚举（1-5级系统）"""
    INVALID = 1      # 无效信号 - 仅观察
    WEAK = 2         # 弱信号 - 最大仓位1%
    MODERATE = 3     # 中等信号 - 最大仓位2%
    STRONG = 4       # 强信号 - 最大仓位3%
    VERY_STRONG = 5  # 最强信号 - 最大仓位3%


class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    VERY_HIGH = 5


@dataclass
class OptionStrategy:
    """期权策略数据类"""
    name: str
    strategy_type: str
    operations: List[str]
    breakeven_points: List[float]
    max_profit: Optional[float]
    max_loss: Optional[float]
    market_conditions: List[str]
    risk_points: List[str]
    success_probability: Optional[float] = None


@dataclass
class TradingWithInsightRisk:
    """Trading With Insight风险评估数据类"""
    overall_risk_level: RiskLevel
    signal_strength_risk: RiskLevel  # 基于信号强度的风险
    
    # 三大支柱风险评估
    psychology_risk: RiskLevel  # 心理风险
    account_management_risk: RiskLevel  # 账户管理风险
    technical_analysis_risk: RiskLevel  # 技术分析风险
    
    # 具体风险因素
    psychology_factors: List[str]  # 心理风险因素
    account_factors: List[str]  # 账户管理风险因素
    technical_factors: List[str]  # 技术分析风险因素
    
    # 风险控制措施
    psychology_controls: List[str]  # 心理风险控制
    account_controls: List[str]  # 账户管理控制
    technical_controls: List[str]  # 技术风险控制
    
    # Gamma Shock特定风险
    ema_divergence_risk: str  # EMA背离风险
    williams_failure_risk: str  # 威廉指标失效风险
    volume_confirmation_risk: str  # 成交量确认风险
    macd_momentum_risk: str  # MACD动能风险
    
    position_size_recommendation: float  # 建议仓位大小
    max_risk_per_trade: float  # 单笔最大风险（3%规则）
    risk_score: float


@dataclass
class RiskAssessment:
    """传统风险评估数据类（保持向后兼容）"""
    overall_risk_level: RiskLevel
    systematic_risk: RiskLevel
    market_risk: RiskLevel
    technical_risk: RiskLevel
    time_risk: RiskLevel
    operational_risk: RiskLevel
    risk_factors: List[str]
    control_measures: List[str]
    risk_score: float


@dataclass
class GammaShockSignal:
    """Gamma Shock信号确认数据类"""
    signal_strength: SignalLevel  # 1-5级信号强度
    confirmation_status: str  # 确认/观察/拒绝
    williams_confirmation: bool  # 威廉指标确认
    technical_confirmation: bool  # 技术指标确认
    timeframe_alignment: bool  # 多时间框架一致性
    volume_confirmation: bool  # 成交量确认
    position_size_percent: float  # 建议仓位百分比
    entry_conditions: List[str]  # 入场条件
    exit_conditions: List[str]  # 出场条件
    risk_management: List[str]  # 风险管理措施
    confirmation_details: Dict[str, Any]  # 详细确认信息


@dataclass
class SignalConfirmation:
    """传统信号确认数据类（保持向后兼容）"""
    signal_validity: float  # 1-10分
    confidence_level: float  # 0-1
    recommended_action: str
    entry_timing: str
    stop_loss: Optional[float]
    take_profit: Optional[float]
    position_size: str
    confirmation_factors: List[str]
    risk_warnings: List[str]


@dataclass
class VolatilityAnalysis:
    """波动率分析数据类"""
    volatility_level: str  # 高/中/低
    iv_vs_hv_comparison: str
    volatility_trend: str
    suitable_strategies: List[str]
    trading_opportunities: List[str]
    volatility_forecast: str
    risk_warnings: List[str]


@dataclass
class SentimentAnalysis:
    """市场情绪分析数据类"""
    sentiment_level: str  # 极度恐慌/恐慌/中性/乐观/极度乐观
    sentiment_score: float  # -1到1
    turning_points: List[str]
    contrarian_opportunities: List[str]
    sentiment_strategies: List[str]
    market_outlook: str


@dataclass
class ParsedResponse:
    """解析后的响应数据类"""
    analysis_type: AnalysisType
    timestamp: datetime
    summary: str
    key_points: List[str]
    recommendations: List[str]
    confidence_score: float
    raw_response: str
    structured_data: Optional[Union[OptionStrategy, RiskAssessment, TradingWithInsightRisk,
                                   SignalConfirmation, GammaShockSignal, VolatilityAnalysis, 
                                   SentimentAnalysis]] = None


class ResponseParser:
    """AI响应解析器"""
    
    def __init__(self):
        self.patterns = self._initialize_patterns()
    
    def _initialize_patterns(self) -> Dict[str, Dict[str, str]]:
        """初始化正则表达式模式"""
        return {
            'general': {
                'summary': r'(?:总结|摘要|概述)[：:](.*?)(?=\n|$)',
                'key_points': r'(?:关键点|要点|重点)[：:]([\s\S]*?)(?=\n\n|$)',
                'recommendations': r'(?:建议|推荐|操作建议)[：:]([\s\S]*?)(?=\n\n|$)',
                'risk_warning': r'(?:风险提示|风险警告|注意事项)[：:]([\s\S]*?)(?=\n\n|$)',
                'confidence': r'(?:可信度|置信度|确信度)[：:]?\s*(\d+(?:\.\d+)?)',
                'score': r'(?:评分|得分|分数)[：:]?\s*(\d+(?:\.\d+)?)',
                'level': r'(?:等级|级别|水平)[：:]?\s*(\d+)'
            },
            'gamma_shock_signal': {
                'signal_strength': r'(?:最终信号强度|信号强度)[：:]?\s*(\d+)(?:级)?',
                'confirmation_status': r'(?:确认状态)[：:]?\s*([^\n]+)',
                'williams_confirmation': r'威廉指标确认[：:]?\s*([^\n]+)',
                'technical_confirmation': r'技术指标支持[：:]?\s*([^\n]+)',
                'timeframe_alignment': r'多时间框架一致性[：:]?\s*([^\n]+)',
                'volume_confirmation': r'成交量确认[：:]?\s*([^\n]+)',
                'position_size': r'建议仓位大小[：:]?\s*([\d.]+)%?',
                'entry_conditions': r'入场时机[：:]([\s\S]*?)(?=止损设置|获利目标|$)',
                'exit_conditions': r'(?:止损设置|获利目标)[：:]([\s\S]*?)(?=风险提醒|$)',
                'risk_management': r'风险提醒[：:]([\s\S]*?)(?=\n\n|$)'
            },
            'trading_insight_risk': {
                'overall_risk': r'风险等级[：:]?\s*(\d+)级?',
                'psychology_risk': r'心理风险评估[：:]([\s\S]*?)(?=账户管理风险|$)',
                'account_risk': r'账户管理风险[：:]([\s\S]*?)(?=技术分析风险|$)',
                'technical_risk': r'技术分析风险[：:]([\s\S]*?)(?=Gamma Shock|$)',
                'ema_divergence': r'EMA背离风险[：:]?\s*([^\n]+)',
                'williams_failure': r'威廉指标失效[：:]?\s*([^\n]+)',
                'volume_confirmation_risk': r'成交量确认缺失[：:]?\s*([^\n]+)',
                'macd_momentum': r'MACD动能衰减[：:]?\s*([^\n]+)',
                'position_recommendation': r'建议仓位[：:]?\s*([\d.]+)%?',
                'risk_controls': r'风险控制建议[：:]([\s\S]*?)(?=心理纪律|$)',
                'psychology_controls': r'心理纪律提醒[：:]([\s\S]*?)(?=\n\n|$)'
            },
            'option_strategy': {
                'strategy_name': r'策略名称[：:]\s*([^\n]+)',
                'strategy_type': r'策略类型[：:]\s*([^\n]+)',
                'operations': r'操作步骤[：:]([\s\S]*?)(?=盈亏平衡|最大盈利|$)',
                'breakeven': r'盈亏平衡点?[：:]\s*([\d.,\s]+)',
                'max_profit': r'最大盈利[：:]\s*([\d.,]+)',
                'max_loss': r'最大亏损[：:]\s*([\d.,]+)',
                'market_conditions': r'适用市场条件[：:]([\s\S]*?)(?=风险控制|$)',
                'risk_points': r'风险控制要点[：:]([\s\S]*?)(?=\n\n|$)'
            },
            'risk_assessment': {
                'overall_risk': r'总体风险等级[：:]\s*(\d+)',
                'systematic_risk': r'系统性风险[：:]\s*(\d+)',
                'market_risk': r'市场风险[：:]\s*(\d+)',
                'technical_risk': r'技术风险[：:]\s*(\d+)',
                'time_risk': r'时间风险[：:]\s*(\d+)',
                'operational_risk': r'操作风险[：:]\s*(\d+)',
                'risk_factors': r'风险因素[：:]([\s\S]*?)(?=风险控制|$)',
                'control_measures': r'风险控制建议[：:]([\s\S]*?)(?=\n\n|$)'
            },
            'signal_confirmation': {
                'validity_score': r'信号可信度[：:]\s*(\d+(?:\.\d+)?)',
                'recommended_action': r'建议操作方向[：:]\s*([^\n]+)',
                'entry_timing': r'入场时机建议[：:]\s*([^\n]+)',
                'stop_loss': r'止损[：:]\s*([\d.]+)',
                'take_profit': r'止盈[：:]\s*([\d.]+)',
                'position_size': r'仓位管理建议[：:]\s*([^\n]+)'
            },
            'volatility_analysis': {
                'volatility_level': r'波动率水平[：:]\s*([^\n]+)',
                'iv_hv_comparison': r'隐含波动率.*?历史波动率.*?对比[：:]([\s\S]*?)(?=波动率趋势|$)',
                'volatility_trend': r'波动率趋势预测[：:]\s*([^\n]+)',
                'suitable_strategies': r'适合的期权策略[：:]([\s\S]*?)(?=波动率交易|$)',
                'trading_opportunities': r'波动率交易机会[：:]([\s\S]*?)(?=\n\n|$)'
            },
            'sentiment_analysis': {
                'sentiment_level': r'市场情绪等级[：:]\s*([^\n]+)',
                'sentiment_score': r'情绪分数[：:]\s*([\d.-]+)',
                'turning_points': r'情绪转折点[：:]([\s\S]*?)(?=反向操作|$)',
                'contrarian_opportunities': r'反向操作机会[：:]([\s\S]*?)(?=情绪驱动|$)',
                'sentiment_strategies': r'情绪驱动.*?策略[：:]([\s\S]*?)(?=风险控制|$)'
            }
        }
    
    def parse_response(self, response: str, analysis_type: AnalysisType) -> ParsedResponse:
        """解析AI响应"""
        try:
            # 基础信息提取
            summary = self._extract_summary(response)
            key_points = self._extract_key_points(response)
            recommendations = self._extract_recommendations(response)
            confidence_score = self._extract_confidence_score(response)
            
            # 根据分析类型提取结构化数据
            structured_data = self._extract_structured_data(response, analysis_type)
            
            return ParsedResponse(
                analysis_type=analysis_type,
                timestamp=datetime.now(),
                summary=summary,
                key_points=key_points,
                recommendations=recommendations,
                confidence_score=confidence_score,
                raw_response=response,
                structured_data=structured_data
            )
        
        except Exception as e:
            # 如果解析失败，返回基础解析结果
            return ParsedResponse(
                analysis_type=analysis_type,
                timestamp=datetime.now(),
                summary=response[:200] + "..." if len(response) > 200 else response,
                key_points=["解析失败，请查看原始响应"],
                recommendations=["建议人工审核AI响应内容"],
                confidence_score=0.0,
                raw_response=response,
                structured_data=None
            )
    
    def _extract_summary(self, response: str) -> str:
        """提取摘要"""
        pattern = self.patterns['general']['summary']
        match = re.search(pattern, response, re.IGNORECASE)
        if match:
            return match.group(1).strip()
        
        # 如果没有找到明确的摘要，取前200个字符
        return response[:200] + "..." if len(response) > 200 else response
    
    def _extract_key_points(self, response: str) -> List[str]:
        """提取关键点"""
        pattern = self.patterns['general']['key_points']
        match = re.search(pattern, response, re.IGNORECASE)
        if match:
            points_text = match.group(1)
            # 按行分割并清理
            points = [line.strip() for line in points_text.split('\n') 
                     if line.strip() and not line.strip().startswith('#')]
            return [self._clean_bullet_point(point) for point in points if point]
        
        # 如果没有找到结构化的关键点，尝试提取数字列表
        numbered_points = re.findall(r'\d+[.、]\s*([^\n]+)', response)
        if numbered_points:
            return numbered_points[:5]  # 最多返回5个要点
        
        return ["未找到明确的关键点"]
    
    def _extract_recommendations(self, response: str) -> List[str]:
        """提取建议"""
        pattern = self.patterns['general']['recommendations']
        match = re.search(pattern, response, re.IGNORECASE)
        if match:
            recommendations_text = match.group(1)
            recommendations = [line.strip() for line in recommendations_text.split('\n') 
                             if line.strip() and not line.strip().startswith('#')]
            return [self._clean_bullet_point(rec) for rec in recommendations if rec]
        
        # 尝试查找其他可能的建议关键词
        suggestion_patterns = [
            r'建议[：:]([^\n]+)',
            r'推荐[：:]([^\n]+)',
            r'应该[：:]([^\n]+)'
        ]
        
        recommendations = []
        for pattern in suggestion_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            recommendations.extend(matches)
        
        return recommendations[:3] if recommendations else ["未找到明确的操作建议"]
    
    def _extract_confidence_score(self, response: str) -> float:
        """提取置信度分数"""
        patterns = [
            self.patterns['general']['confidence'],
            self.patterns['general']['score'],
            r'(\d+(?:\.\d+)?)分',
            r'(\d+(?:\.\d+)?)%'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                score = float(match.group(1))
                # 标准化到0-1范围
                if score > 1:
                    score = score / 10 if score <= 10 else score / 100
                return min(max(score, 0.0), 1.0)
        
        return 0.5  # 默认中等置信度
    
    def _extract_structured_data(self, response: str, analysis_type: AnalysisType) -> Optional[Any]:
        """根据分析类型提取结构化数据"""
        try:
            if analysis_type == AnalysisType.OPTION_STRATEGY:
                return self._parse_option_strategy(response)
            elif analysis_type == AnalysisType.RISK_ASSESSMENT:
                # 优先尝试解析Trading With Insight风险评估
                gamma_risk = self._parse_trading_insight_risk(response)
                if gamma_risk:
                    return gamma_risk
                # 回退到传统风险评估
                return self._parse_risk_assessment(response)
            elif analysis_type == AnalysisType.SIGNAL_CONFIRMATION:
                # 优先尝试解析Gamma Shock信号确认
                gamma_signal = self._parse_gamma_shock_signal(response)
                if gamma_signal:
                    return gamma_signal
                # 回退到传统信号确认
                return self._parse_signal_confirmation(response)
            elif analysis_type == AnalysisType.VOLATILITY_ANALYSIS:
                return self._parse_volatility_analysis(response)
            elif analysis_type == AnalysisType.SENTIMENT_ANALYSIS:
                return self._parse_sentiment_analysis(response)
        except Exception:
            pass
        
        return None
    
    def _parse_option_strategy(self, response: str) -> Optional[OptionStrategy]:
        """解析期权策略"""
        patterns = self.patterns['option_strategy']
        
        name = self._extract_pattern(response, patterns['strategy_name'])
        strategy_type = self._extract_pattern(response, patterns['strategy_type'])
        
        if not name:
            return None
        
        operations = self._extract_list_items(response, patterns['operations'])
        breakeven_text = self._extract_pattern(response, patterns['breakeven'])
        breakeven_points = self._parse_numbers(breakeven_text) if breakeven_text else []
        
        max_profit = self._extract_number(response, patterns['max_profit'])
        max_loss = self._extract_number(response, patterns['max_loss'])
        
        market_conditions = self._extract_list_items(response, patterns['market_conditions'])
        risk_points = self._extract_list_items(response, patterns['risk_points'])
        
        return OptionStrategy(
            name=name,
            strategy_type=strategy_type or "未指定",
            operations=operations,
            breakeven_points=breakeven_points,
            max_profit=max_profit,
            max_loss=max_loss,
            market_conditions=market_conditions,
            risk_points=risk_points
        )
    
    def _parse_risk_assessment(self, response: str) -> Optional[RiskAssessment]:
        """解析风险评估"""
        patterns = self.patterns['risk_assessment']
        
        overall_risk = self._extract_risk_level(response, patterns['overall_risk'])
        if overall_risk is None:
            return None
        
        return RiskAssessment(
            overall_risk_level=overall_risk,
            systematic_risk=self._extract_risk_level(response, patterns['systematic_risk']) or RiskLevel.MEDIUM,
            market_risk=self._extract_risk_level(response, patterns['market_risk']) or RiskLevel.MEDIUM,
            technical_risk=self._extract_risk_level(response, patterns['technical_risk']) or RiskLevel.MEDIUM,
            time_risk=self._extract_risk_level(response, patterns['time_risk']) or RiskLevel.MEDIUM,
            operational_risk=self._extract_risk_level(response, patterns['operational_risk']) or RiskLevel.MEDIUM,
            risk_factors=self._extract_list_items(response, patterns['risk_factors']),
            control_measures=self._extract_list_items(response, patterns['control_measures']),
            risk_score=overall_risk.value / 5.0
        )
    
    def _parse_signal_confirmation(self, response: str) -> Optional[SignalConfirmation]:
        """解析信号确认"""
        patterns = self.patterns['signal_confirmation']
        
        validity_score = self._extract_number(response, patterns['validity_score'])
        if validity_score is None:
            return None
        
        return SignalConfirmation(
            signal_validity=validity_score,
            confidence_level=validity_score / 10.0,
            recommended_action=self._extract_pattern(response, patterns['recommended_action']) or "未指定",
            entry_timing=self._extract_pattern(response, patterns['entry_timing']) or "未指定",
            stop_loss=self._extract_number(response, patterns['stop_loss']),
            take_profit=self._extract_number(response, patterns['take_profit']),
            position_size=self._extract_pattern(response, patterns['position_size']) or "未指定",
            confirmation_factors=self._extract_key_points(response),
            risk_warnings=self._extract_recommendations(response)
        )
    
    def _parse_volatility_analysis(self, response: str) -> Optional[VolatilityAnalysis]:
        """解析波动率分析"""
        patterns = self.patterns['volatility_analysis']
        
        volatility_level = self._extract_pattern(response, patterns['volatility_level'])
        if not volatility_level:
            return None
        
        return VolatilityAnalysis(
            volatility_level=volatility_level,
            iv_vs_hv_comparison=self._extract_pattern(response, patterns['iv_hv_comparison']) or "未分析",
            volatility_trend=self._extract_pattern(response, patterns['volatility_trend']) or "未预测",
            suitable_strategies=self._extract_list_items(response, patterns['suitable_strategies']),
            trading_opportunities=self._extract_list_items(response, patterns['trading_opportunities']),
            volatility_forecast="未提供",
            risk_warnings=self._extract_recommendations(response)
        )
    
    def _parse_gamma_shock_signal(self, response: str) -> Optional[GammaShockSignal]:
        """解析Gamma Shock信号确认"""
        patterns = self.patterns['gamma_shock_signal']
        
        # 提取信号强度
        signal_strength_num = self._extract_number(response, patterns['signal_strength'])
        if signal_strength_num is None:
            return None
        
        try:
            signal_strength = SignalLevel(int(signal_strength_num))
        except ValueError:
            return None
        
        # 提取确认状态
        confirmation_status = self._extract_pattern(response, patterns['confirmation_status']) or "未确定"
        
        # 提取各种确认信息
        williams_text = self._extract_pattern(response, patterns['williams_confirmation']) or ""
        williams_confirmation = "确认" in williams_text or "✓" in williams_text
        
        technical_text = self._extract_pattern(response, patterns['technical_confirmation']) or ""
        technical_confirmation = "确认" in technical_text or "支持" in technical_text or "✓" in technical_text
        
        timeframe_text = self._extract_pattern(response, patterns['timeframe_alignment']) or ""
        timeframe_alignment = "一致" in timeframe_text or "对齐" in timeframe_text or "✓" in timeframe_text
        
        volume_text = self._extract_pattern(response, patterns['volume_confirmation']) or ""
        volume_confirmation = "确认" in volume_text or "支持" in volume_text or "✓" in volume_text
        
        # 提取仓位建议
        position_size = self._extract_number(response, patterns['position_size']) or 0.0
        
        # 提取条件和管理措施
        entry_conditions = self._extract_list_items(response, patterns['entry_conditions'])
        exit_conditions = self._extract_list_items(response, patterns['exit_conditions'])
        risk_management = self._extract_list_items(response, patterns['risk_management'])
        
        # 构建详细确认信息
        confirmation_details = {
            'williams_details': williams_text,
            'technical_details': technical_text,
            'timeframe_details': timeframe_text,
            'volume_details': volume_text
        }
        
        return GammaShockSignal(
            signal_strength=signal_strength,
            confirmation_status=confirmation_status,
            williams_confirmation=williams_confirmation,
            technical_confirmation=technical_confirmation,
            timeframe_alignment=timeframe_alignment,
            volume_confirmation=volume_confirmation,
            position_size_percent=position_size,
            entry_conditions=entry_conditions,
            exit_conditions=exit_conditions,
            risk_management=risk_management,
            confirmation_details=confirmation_details
        )
    
    def _parse_trading_insight_risk(self, response: str) -> Optional[TradingWithInsightRisk]:
        """解析Trading With Insight风险评估"""
        patterns = self.patterns['trading_insight_risk']
        
        # 提取总体风险等级
        overall_risk_num = self._extract_number(response, patterns['overall_risk'])
        if overall_risk_num is None:
            return None
        
        try:
            overall_risk = RiskLevel(int(overall_risk_num))
        except ValueError:
            return None
        
        # 计算信号强度风险（基于总体风险的反向映射）
        signal_strength_risk = RiskLevel(6 - overall_risk.value) if overall_risk.value <= 5 else RiskLevel.VERY_LOW
        
        # 提取三大支柱风险因素
        psychology_text = self._extract_pattern(response, patterns['psychology_risk']) or ""
        account_text = self._extract_pattern(response, patterns['account_risk']) or ""
        technical_text = self._extract_pattern(response, patterns['technical_risk']) or ""
        
        # 解析风险因素列表
        psychology_factors = self._extract_list_items(response, patterns['psychology_risk'])
        account_factors = self._extract_list_items(response, patterns['account_risk'])
        technical_factors = self._extract_list_items(response, patterns['technical_risk'])
        
        # 提取Gamma Shock特定风险
        ema_divergence = self._extract_pattern(response, patterns['ema_divergence']) or "未评估"
        williams_failure = self._extract_pattern(response, patterns['williams_failure']) or "未评估"
        volume_risk = self._extract_pattern(response, patterns['volume_confirmation_risk']) or "未评估"
        macd_momentum = self._extract_pattern(response, patterns['macd_momentum']) or "未评估"
        
        # 提取仓位建议
        position_recommendation = self._extract_number(response, patterns['position_recommendation']) or 0.0
        
        # 提取控制措施
        risk_controls = self._extract_list_items(response, patterns['risk_controls'])
        psychology_controls = self._extract_list_items(response, patterns['psychology_controls'])
        
        return TradingWithInsightRisk(
            overall_risk_level=overall_risk,
            signal_strength_risk=signal_strength_risk,
            psychology_risk=overall_risk,  # 简化处理，实际可以单独解析
            account_management_risk=overall_risk,
            technical_analysis_risk=overall_risk,
            psychology_factors=psychology_factors,
            account_factors=account_factors,
            technical_factors=technical_factors,
            psychology_controls=psychology_controls,
            account_controls=risk_controls,
            technical_controls=risk_controls,
            ema_divergence_risk=ema_divergence,
            williams_failure_risk=williams_failure,
            volume_confirmation_risk=volume_risk,
            macd_momentum_risk=macd_momentum,
            position_size_recommendation=position_recommendation,
            max_risk_per_trade=3.0,  # Trading With Insight的3%规则
            risk_score=overall_risk.value / 5.0
        )
    
    def _parse_sentiment_analysis(self, response: str) -> Optional[SentimentAnalysis]:
        """解析市场情绪分析"""
        patterns = self.patterns['sentiment_analysis']
        
        sentiment_level = self._extract_pattern(response, patterns['sentiment_level'])
        if not sentiment_level:
            return None
        
        sentiment_score = self._extract_number(response, patterns['sentiment_score']) or 0.0
        
        return SentimentAnalysis(
            sentiment_level=sentiment_level,
            sentiment_score=sentiment_score,
            turning_points=self._extract_list_items(response, patterns['turning_points']),
            contrarian_opportunities=self._extract_list_items(response, patterns['contrarian_opportunities']),
            sentiment_strategies=self._extract_list_items(response, patterns['sentiment_strategies']),
            market_outlook="未提供"
        )
    
    def _extract_pattern(self, text: str, pattern: str) -> Optional[str]:
        """提取单个模式匹配"""
        match = re.search(pattern, text, re.IGNORECASE)
        return match.group(1).strip() if match else None
    
    def _extract_number(self, text: str, pattern: str) -> Optional[float]:
        """提取数字"""
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                pass
        return None
    
    def _extract_risk_level(self, text: str, pattern: str) -> Optional[RiskLevel]:
        """提取风险等级"""
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            try:
                level = int(match.group(1))
                if 1 <= level <= 5:
                    return RiskLevel(level)
            except ValueError:
                pass
        return None
    
    def _extract_list_items(self, text: str, pattern: str) -> List[str]:
        """提取列表项"""
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            items_text = match.group(1)
            items = [line.strip() for line in items_text.split('\n') 
                    if line.strip() and not line.strip().startswith('#')]
            return [self._clean_bullet_point(item) for item in items if item]
        return []
    
    def _parse_numbers(self, text: str) -> List[float]:
        """从文本中解析数字列表"""
        numbers = re.findall(r'\d+(?:\.\d+)?', text)
        return [float(num) for num in numbers]
    
    def _clean_bullet_point(self, text: str) -> str:
        """清理列表项的格式"""
        # 移除常见的列表标记
        text = re.sub(r'^[\s\-\*\•\d+\.\)]+\s*', '', text)
        return text.strip()
    
    def to_dict(self, parsed_response: ParsedResponse) -> Dict[str, Any]:
        """将解析结果转换为字典"""
        result = asdict(parsed_response)
        
        # 处理枚举类型
        result['analysis_type'] = parsed_response.analysis_type.value
        result['timestamp'] = parsed_response.timestamp.isoformat()
        
        # 处理结构化数据
        if parsed_response.structured_data:
            result['structured_data'] = asdict(parsed_response.structured_data)
            
            # 处理嵌套的枚举类型
            if isinstance(parsed_response.structured_data, RiskAssessment):
                risk_data = result['structured_data']
                for key, value in risk_data.items():
                    if isinstance(value, RiskLevel):
                        risk_data[key] = value.value
        
        return result
    
    def to_json(self, parsed_response: ParsedResponse) -> str:
        """将解析结果转换为JSON字符串"""
        return json.dumps(self.to_dict(parsed_response), ensure_ascii=False, indent=2)


# 全局解析器实例
response_parser = ResponseParser()


if __name__ == '__main__':
    # 测试代码
    parser = ResponseParser()
    
    # 测试市场分析响应
    test_response = """
    总结：当前市场处于震荡上行趋势，技术指标显示多头信号。
    
    关键点：
    1. EMA多头排列，支撑强劲
    2. MACD金叉，动能增强
    3. 成交量放大，资金流入
    
    建议：
    1. 可考虑买入看涨期权
    2. 设置止损位于2.80
    3. 目标位看向3.00
    
    信号可信度：8分
    """
    
    parsed = parser.parse_response(test_response, AnalysisType.MARKET_ANALYSIS)
    print("解析结果:")
    print(f"摘要: {parsed.summary}")
    print(f"关键点: {parsed.key_points}")
    print(f"建议: {parsed.recommendations}")
    print(f"置信度: {parsed.confidence_score}")
    
    # 转换为JSON
    json_result = parser.to_json(parsed)
    print("\nJSON格式:")
    print(json_result)