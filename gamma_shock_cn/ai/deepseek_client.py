#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek AI客户端模块

提供AI增强的市场分析和信号确认：
- 市场数据分析
- 交易信号确认
- 风险评估
- 策略建议

作者: AI Assistant
创建时间: 2024-12-11
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np

try:
    import requests
except ImportError:
    requests = None
    logging.warning("requests库未安装，AI功能将受限")

logger = logging.getLogger(__name__)

@dataclass
class AIAnalysisRequest:
    """AI分析请求数据结构"""
    symbol: str                    # 标的代码
    analysis_type: str            # 分析类型
    market_data: Dict[str, Any]   # 市场数据
    technical_indicators: Dict[str, Any]  # 技术指标
    option_sentiment: Optional[Dict[str, Any]] = None  # 期权情绪
    volatility_analysis: Optional[Dict[str, Any]] = None  # 波动率分析
    current_signals: Optional[List[Dict[str, Any]]] = None  # 当前信号
    context: Optional[str] = None  # 额外上下文
    timestamp: datetime = None     # 请求时间
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class AIAnalysisResponse:
    """AI分析响应数据结构"""
    request_id: str               # 请求ID
    symbol: str                   # 标的代码
    analysis_result: Dict[str, Any]  # 分析结果
    confidence_score: float       # 置信度分数
    risk_assessment: Dict[str, Any]  # 风险评估
    recommendations: List[str]    # 建议列表
    market_outlook: str          # 市场展望
    key_factors: List[str]       # 关键因素
    timestamp: datetime          # 响应时间
    processing_time: float       # 处理时间
    success: bool = True         # 是否成功
    error_message: Optional[str] = None  # 错误信息

class DeepSeekClient:
    """
    DeepSeek AI客户端
    
    提供AI增强的市场分析功能
    """
    
    def __init__(self, api_key: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        """
        初始化DeepSeek客户端
        
        Args:
            api_key: DeepSeek API密钥
            config: 配置参数
        """
        self.api_key = api_key
        self.config = config or self._get_default_config()
        
        # API配置
        self.base_url = self.config.get('base_url', 'https://api.deepseek.com/v1')
        self.model = self.config.get('model', 'deepseek-chat')
        self.max_tokens = self.config.get('max_tokens', 2000)
        self.temperature = self.config.get('temperature', 0.7)
        
        # 请求配置
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1)
        
        # 缓存配置
        self.enable_cache = self.config.get('enable_cache', True)
        self.cache_ttl = self.config.get('cache_ttl', 300)  # 5分钟
        self._cache = {} if self.enable_cache else None
        
        # 速率限制
        self.rate_limit = self.config.get('rate_limit', 60)  # 每分钟请求数
        self._request_times = []
        
        logger.info("DeepSeek客户端初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'base_url': 'https://api.deepseek.com/v1',
            'model': 'deepseek-chat',
            'max_tokens': 2000,
            'temperature': 0.7,
            'timeout': 30,
            'max_retries': 3,
            'retry_delay': 1,
            'enable_cache': True,
            'cache_ttl': 300,
            'rate_limit': 60
        }
    
    def analyze_market_data(self, request: AIAnalysisRequest) -> AIAnalysisResponse:
        """
        分析市场数据
        
        Args:
            request: AI分析请求
            
        Returns:
            AI分析响应
        """
        try:
            start_time = time.time()
            request_id = self._generate_request_id()
            
            logger.info(f"开始AI市场分析: {request.symbol} ({request_id})")
            
            # 检查缓存
            if self.enable_cache:
                cached_result = self._get_cached_result(request)
                if cached_result:
                    logger.info(f"使用缓存结果: {request_id}")
                    return cached_result
            
            # 检查速率限制
            if not self._check_rate_limit():
                logger.warning("触发速率限制，等待中...")
                time.sleep(60 / self.rate_limit)
            
            # 构建分析提示
            prompt = self._build_analysis_prompt(request)
            
            # 调用AI API
            ai_response = self._call_deepseek_api(prompt)
            
            # 解析响应
            analysis_result = self._parse_ai_response(ai_response)
            
            # 构建响应对象
            response = AIAnalysisResponse(
                request_id=request_id,
                symbol=request.symbol,
                analysis_result=analysis_result,
                confidence_score=analysis_result.get('confidence_score', 75.0),
                risk_assessment=analysis_result.get('risk_assessment', {}),
                recommendations=analysis_result.get('recommendations', []),
                market_outlook=analysis_result.get('market_outlook', '中性'),
                key_factors=analysis_result.get('key_factors', []),
                timestamp=datetime.now(),
                processing_time=time.time() - start_time
            )
            
            # 缓存结果
            if self.enable_cache:
                self._cache_result(request, response)
            
            logger.info(f"AI分析完成: {request_id} (耗时: {response.processing_time:.2f}s)")
            return response
            
        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            return AIAnalysisResponse(
                request_id=request_id if 'request_id' in locals() else 'error',
                symbol=request.symbol,
                analysis_result={},
                confidence_score=0.0,
                risk_assessment={'level': 'unknown'},
                recommendations=[],
                market_outlook='无法分析',
                key_factors=[],
                timestamp=datetime.now(),
                processing_time=time.time() - start_time if 'start_time' in locals() else 0,
                success=False,
                error_message=str(e)
            )
    
    def confirm_trading_signal(self, signal_data: Dict[str, Any], 
                             market_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        确认交易信号
        
        Args:
            signal_data: 信号数据
            market_context: 市场上下文
            
        Returns:
            确认结果字典
        """
        try:
            # 构建信号确认请求
            request = AIAnalysisRequest(
                symbol=signal_data.get('symbol', ''),
                analysis_type='signal_confirmation',
                market_data=market_context.get('market_data', {}),
                technical_indicators=market_context.get('technical_indicators', {}),
                option_sentiment=market_context.get('option_sentiment'),
                volatility_analysis=market_context.get('volatility_analysis'),
                current_signals=[signal_data],
                context=f"请确认交易信号: {signal_data.get('signal_type', 'unknown')}"
            )
            
            # 执行AI分析
            response = self.analyze_market_data(request)
            
            # 提取确认结果
            confirmation = {
                'confirmed': response.success and response.confidence_score > 70,
                'confidence': response.confidence_score,
                'ai_recommendation': response.analysis_result.get('signal_confirmation', 'hold'),
                'risk_level': response.risk_assessment.get('level', 'medium'),
                'key_reasons': response.key_factors,
                'market_outlook': response.market_outlook,
                'suggestions': response.recommendations,
                'timestamp': response.timestamp
            }
            
            return confirmation
            
        except Exception as e:
            logger.error(f"信号确认失败: {e}")
            return {
                'confirmed': False,
                'confidence': 0.0,
                'ai_recommendation': 'hold',
                'risk_level': 'high',
                'key_reasons': ['AI分析失败'],
                'market_outlook': '无法确定',
                'suggestions': ['建议人工确认'],
                'timestamp': datetime.now(),
                'error': str(e)
            }
    
    def _build_analysis_prompt(self, request: AIAnalysisRequest) -> str:
        """
        构建分析提示词
        
        Args:
            request: 分析请求
            
        Returns:
            提示词字符串
        """
        try:
            # 基础信息
            prompt_parts = [
                f"请分析标的 {request.symbol} 的市场数据，提供专业的期权交易建议。",
                f"分析类型: {request.analysis_type}",
                f"分析时间: {request.timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
                ""
            ]
            
            # 市场数据
            if request.market_data:
                prompt_parts.append("=== 市场数据 ===")
                market_data = request.market_data
                
                # 价格信息
                if 'price_info' in market_data:
                    price_info = market_data['price_info']
                    prompt_parts.append(f"当前价格: {price_info.get('current_price', 'N/A')}")
                    prompt_parts.append(f"涨跌幅: {price_info.get('change_pct', 'N/A')}%")
                    prompt_parts.append(f"成交量: {price_info.get('volume', 'N/A')}")
                
                # 趋势信息
                if 'trend_info' in market_data:
                    trend_info = market_data['trend_info']
                    prompt_parts.append(f"短期趋势: {trend_info.get('short_term', 'N/A')}")
                    prompt_parts.append(f"中期趋势: {trend_info.get('medium_term', 'N/A')}")
                    prompt_parts.append(f"长期趋势: {trend_info.get('long_term', 'N/A')}")
                
                prompt_parts.append("")
            
            # 技术指标
            if request.technical_indicators:
                prompt_parts.append("=== 技术指标 ===")
                tech_indicators = request.technical_indicators
                
                # 均线系统
                if 'ema' in tech_indicators:
                    ema_data = tech_indicators['ema']
                    prompt_parts.append(f"EMA8: {ema_data.get('ema8', 'N/A')}")
                    prompt_parts.append(f"EMA21: {ema_data.get('ema21', 'N/A')}")
                    prompt_parts.append(f"EMA55: {ema_data.get('ema55', 'N/A')}")
                
                # KDJ指标
                if 'kdj' in tech_indicators:
                    kdj_data = tech_indicators['kdj']
                    prompt_parts.append(f"KDJ: K={kdj_data.get('k', 'N/A')}, D={kdj_data.get('d', 'N/A')}, J={kdj_data.get('j', 'N/A')}")
                
                # MACD指标
                if 'macd' in tech_indicators:
                    macd_data = tech_indicators['macd']
                    prompt_parts.append(f"MACD: {macd_data.get('macd', 'N/A')}, Signal: {macd_data.get('signal', 'N/A')}")
                
                prompt_parts.append("")
            
            # 波动率分析
            if request.volatility_analysis:
                prompt_parts.append("=== 波动率分析 ===")
                vol_analysis = request.volatility_analysis
                
                prompt_parts.append(f"历史波动率: {vol_analysis.get('historical_vol', 'N/A')}")
                prompt_parts.append(f"波动率百分位: {vol_analysis.get('vol_percentile', 'N/A')}")
                prompt_parts.append(f"波动率制度: {vol_analysis.get('vol_regime', 'N/A')}")
                prompt_parts.append(f"VIX水平: {vol_analysis.get('vix_level', 'N/A')}")
                prompt_parts.append("")
            
            # 期权情绪
            if request.option_sentiment:
                prompt_parts.append("=== 期权情绪 ===")
                sentiment = request.option_sentiment
                
                prompt_parts.append(f"Put/Call Ratio: {sentiment.get('pcr', 'N/A')}")
                prompt_parts.append(f"情绪得分: {sentiment.get('sentiment_score', 'N/A')}")
                prompt_parts.append(f"情绪水平: {sentiment.get('sentiment_level', 'N/A')}")
                prompt_parts.append("")
            
            # 当前信号
            if request.current_signals:
                prompt_parts.append("=== 当前信号 ===")
                for i, signal in enumerate(request.current_signals, 1):
                    prompt_parts.append(f"信号{i}: {signal.get('signal_type', 'N/A')} - 等级{signal.get('level', 'N/A')} - 置信度{signal.get('confidence', 'N/A')}%")
                    prompt_parts.append(f"原因: {signal.get('reason', 'N/A')}")
                prompt_parts.append("")
            
            # 分析要求
            prompt_parts.extend([
                "=== 分析要求 ===",
                "请基于以上数据提供以下分析:",
                "1. 市场综合评估 (看多/看空/中性)",
                "2. 置信度评分 (0-100分)",
                "3. 风险等级评估 (低/中/高)",
                "4. 期权交易建议 (买入看涨/买入看跌/卖出期权/观望)",
                "5. 关键影响因素 (列出3-5个要点)",
                "6. 具体操作建议 (入场时机、止损止盈等)",
                "7. 市场展望 (短期1-3天预期)",
                "",
                "请以JSON格式返回分析结果，包含以下字段:",
                "{",
                '  "market_assessment": "看多/看空/中性",',
                '  "confidence_score": 85.0,',
                '  "risk_assessment": {"level": "中", "factors": []},',
                '  "signal_confirmation": "buy_call/buy_put/sell_option/hold",',
                '  "key_factors": [],',
                '  "recommendations": [],',
                '  "market_outlook": "短期展望描述"',
                "}"
            ])
            
            # 添加上下文
            if request.context:
                prompt_parts.extend(["", f"额外上下文: {request.context}"])
            
            return "\n".join(prompt_parts)
            
        except Exception as e:
            logger.error(f"构建提示词失败: {e}")
            return f"请分析标的 {request.symbol} 的市场数据并提供期权交易建议。"
    
    def _call_deepseek_api(self, prompt: str) -> Dict[str, Any]:
        """
        调用DeepSeek API
        
        Args:
            prompt: 提示词
            
        Returns:
            API响应字典
        """
        if not requests:
            raise ImportError("requests库未安装，无法调用API")
        
        if not self.api_key:
            # 模拟响应（用于测试）
            logger.warning("未配置API密钥，返回模拟响应")
            return self._get_mock_response(prompt)
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': self.model,
            'messages': [
                {
                    'role': 'system',
                    'content': '你是一个专业的期权交易分析师，具有丰富的市场分析经验。请基于提供的数据进行客观、专业的分析。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': self.max_tokens,
            'temperature': self.temperature
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=self.timeout
                )
                
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"API调用失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                else:
                    raise
    
    def _get_mock_response(self, prompt: str) -> Dict[str, Any]:
        """
        获取模拟响应（用于测试）
        
        Args:
            prompt: 提示词
            
        Returns:
            模拟响应字典
        """
        mock_analysis = {
            "market_assessment": "中性偏多",
            "confidence_score": 75.0,
            "risk_assessment": {
                "level": "中",
                "factors": ["市场波动率适中", "技术指标分歧", "期权情绪中性"]
            },
            "signal_confirmation": "hold",
            "key_factors": [
                "技术面显示震荡整理格局",
                "期权PCR比率处于正常范围",
                "波动率水平适中",
                "成交量无明显异常"
            ],
            "recommendations": [
                "建议观望等待明确方向",
                "可考虑卖出跨式期权策略",
                "密切关注突破信号"
            ],
            "market_outlook": "短期内预计继续震荡，等待方向性突破"
        }
        
        return {
            'choices': [{
                'message': {
                    'content': json.dumps(mock_analysis, ensure_ascii=False, indent=2)
                }
            }]
        }
    
    def _parse_ai_response(self, api_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析AI响应
        
        Args:
            api_response: API响应
            
        Returns:
            解析后的分析结果
        """
        try:
            # 提取响应内容
            content = api_response['choices'][0]['message']['content']
            
            # 尝试解析JSON
            try:
                # 查找JSON部分
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                
                if start_idx != -1 and end_idx > start_idx:
                    json_content = content[start_idx:end_idx]
                    analysis_result = json.loads(json_content)
                else:
                    # 如果没有找到JSON，尝试解析整个内容
                    analysis_result = json.loads(content)
                
            except json.JSONDecodeError:
                # JSON解析失败，使用文本解析
                logger.warning("JSON解析失败，使用文本解析")
                analysis_result = self._parse_text_response(content)
            
            # 验证和补充必要字段
            analysis_result = self._validate_analysis_result(analysis_result)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return self._get_default_analysis_result()
    
    def _parse_text_response(self, content: str) -> Dict[str, Any]:
        """
        解析文本响应
        
        Args:
            content: 响应文本
            
        Returns:
            解析结果字典
        """
        result = {
            "market_assessment": "中性",
            "confidence_score": 50.0,
            "risk_assessment": {"level": "中", "factors": []},
            "signal_confirmation": "hold",
            "key_factors": [],
            "recommendations": [],
            "market_outlook": "需要进一步观察"
        }
        
        # 简单的关键词匹配
        content_lower = content.lower()
        
        # 市场评估
        if any(word in content_lower for word in ['看多', '上涨', '买入', 'bullish']):
            result["market_assessment"] = "看多"
            result["confidence_score"] = 70.0
        elif any(word in content_lower for word in ['看空', '下跌', '卖出', 'bearish']):
            result["market_assessment"] = "看空"
            result["confidence_score"] = 70.0
        
        # 风险评估
        if any(word in content_lower for word in ['高风险', 'high risk', '谨慎']):
            result["risk_assessment"]["level"] = "高"
        elif any(word in content_lower for word in ['低风险', 'low risk', '安全']):
            result["risk_assessment"]["level"] = "低"
        
        # 提取建议（简单实现）
        lines = content.split('\n')
        recommendations = []
        for line in lines:
            if any(word in line for word in ['建议', '推荐', 'recommend', 'suggest']):
                recommendations.append(line.strip())
        
        if recommendations:
            result["recommendations"] = recommendations[:3]  # 最多3条建议
        
        return result
    
    def _validate_analysis_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和补充分析结果
        
        Args:
            result: 原始分析结果
            
        Returns:
            验证后的分析结果
        """
        default_result = self._get_default_analysis_result()
        
        # 确保所有必要字段存在
        for key, default_value in default_result.items():
            if key not in result:
                result[key] = default_value
        
        # 验证置信度分数
        confidence = result.get('confidence_score', 50.0)
        if not isinstance(confidence, (int, float)) or not (0 <= confidence <= 100):
            result['confidence_score'] = 50.0
        
        # 验证风险评估
        risk_assessment = result.get('risk_assessment', {})
        if not isinstance(risk_assessment, dict):
            result['risk_assessment'] = {"level": "中", "factors": []}
        elif 'level' not in risk_assessment:
            risk_assessment['level'] = "中"
        
        # 验证列表字段
        for list_field in ['key_factors', 'recommendations']:
            if not isinstance(result.get(list_field), list):
                result[list_field] = []
        
        return result
    
    def _get_default_analysis_result(self) -> Dict[str, Any]:
        """
        获取默认分析结果
        
        Returns:
            默认分析结果字典
        """
        return {
            "market_assessment": "中性",
            "confidence_score": 50.0,
            "risk_assessment": {"level": "中", "factors": []},
            "signal_confirmation": "hold",
            "key_factors": ["数据不足", "需要更多信息"],
            "recommendations": ["建议观望", "等待更多数据"],
            "market_outlook": "暂无明确方向"
        }
    
    def _generate_request_id(self) -> str:
        """
        生成请求ID
        
        Returns:
            请求ID字符串
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        import random
        random_suffix = random.randint(1000, 9999)
        return f"ai_req_{timestamp}_{random_suffix}"
    
    def _check_rate_limit(self) -> bool:
        """
        检查速率限制
        
        Returns:
            是否可以发送请求
        """
        now = time.time()
        
        # 清理过期的请求时间
        self._request_times = [t for t in self._request_times if now - t < 60]
        
        # 检查是否超过限制
        if len(self._request_times) >= self.rate_limit:
            return False
        
        # 记录当前请求时间
        self._request_times.append(now)
        return True
    
    def _get_cached_result(self, request: AIAnalysisRequest) -> Optional[AIAnalysisResponse]:
        """
        获取缓存结果
        
        Args:
            request: 分析请求
            
        Returns:
            缓存的响应或None
        """
        if not self._cache:
            return None
        
        cache_key = self._generate_cache_key(request)
        cached_item = self._cache.get(cache_key)
        
        if cached_item:
            cached_time, cached_response = cached_item
            if time.time() - cached_time < self.cache_ttl:
                return cached_response
            else:
                # 缓存过期，删除
                del self._cache[cache_key]
        
        return None
    
    def _cache_result(self, request: AIAnalysisRequest, response: AIAnalysisResponse):
        """
        缓存结果
        
        Args:
            request: 分析请求
            response: 分析响应
        """
        if not self._cache:
            return
        
        cache_key = self._generate_cache_key(request)
        self._cache[cache_key] = (time.time(), response)
        
        # 清理过期缓存
        self._cleanup_cache()
    
    def _generate_cache_key(self, request: AIAnalysisRequest) -> str:
        """
        生成缓存键
        
        Args:
            request: 分析请求
            
        Returns:
            缓存键字符串
        """
        # 使用主要参数生成缓存键
        key_parts = [
            request.symbol,
            request.analysis_type,
            str(hash(str(request.market_data))),
            str(hash(str(request.technical_indicators)))
        ]
        return "_".join(key_parts)
    
    def _cleanup_cache(self):
        """
        清理过期缓存
        """
        if not self._cache:
            return
        
        now = time.time()
        expired_keys = []
        
        for key, (cached_time, _) in self._cache.items():
            if now - cached_time >= self.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")


def create_ai_client(api_key: Optional[str] = None, config: Optional[Dict[str, Any]] = None) -> DeepSeekClient:
    """
    创建AI客户端的便捷函数
    
    Args:
        api_key: API密钥
        config: 配置参数
        
    Returns:
        DeepSeek客户端实例
    """
    return DeepSeekClient(api_key, config)


if __name__ == "__main__":
    # 测试代码
    print("测试DeepSeek AI客户端...")
    
    # 创建客户端（无API密钥，使用模拟响应）
    client = create_ai_client()
    
    # 创建测试请求
    test_request = AIAnalysisRequest(
        symbol="159915",
        analysis_type="market_analysis",
        market_data={
            'price_info': {
                'current_price': 1.234,
                'change_pct': 2.5,
                'volume': 1000000
            },
            'trend_info': {
                'short_term': '上涨',
                'medium_term': '震荡',
                'long_term': '上涨'
            }
        },
        technical_indicators={
            'ema': {'ema8': 1.230, 'ema21': 1.225, 'ema55': 1.220},
            'kdj': {'k': 65, 'd': 60, 'j': 75},
            'macd': {'macd': 0.005, 'signal': 0.003}
        },
        volatility_analysis={
            'historical_vol': 0.25,
            'vol_percentile': 45,
            'vol_regime': 'medium',
            'vix_level': 18.5
        },
        option_sentiment={
            'pcr': 1.1,
            'sentiment_score': 55,
            'sentiment_level': 'neutral'
        }
    )
    
    # 执行分析
    response = client.analyze_market_data(test_request)
    
    print(f"\n分析结果:")
    print(f"成功: {response.success}")
    print(f"市场评估: {response.analysis_result.get('market_assessment', 'N/A')}")
    print(f"置信度: {response.confidence_score}%")
    print(f"风险等级: {response.risk_assessment.get('level', 'N/A')}")
    print(f"处理时间: {response.processing_time:.2f}秒")
    
    if response.recommendations:
        print("\n建议:")
        for i, rec in enumerate(response.recommendations, 1):
            print(f"{i}. {rec}")
    
    # 测试信号确认
    print("\n测试信号确认...")
    test_signal = {
        'symbol': '159915',
        'signal_type': 'buy_call',
        'level': 3,
        'confidence': 75.0,
        'reason': '技术面偏多'
    }
    
    test_context = {
        'market_data': test_request.market_data,
        'technical_indicators': test_request.technical_indicators,
        'volatility_analysis': test_request.volatility_analysis,
        'option_sentiment': test_request.option_sentiment
    }
    
    confirmation = client.confirm_trading_signal(test_signal, test_context)
    
    print(f"信号确认结果:")
    print(f"确认: {confirmation['confirmed']}")
    print(f"AI建议: {confirmation['ai_recommendation']}")
    print(f"置信度: {confirmation['confidence']}%")
    print(f"风险等级: {confirmation['risk_level']}")