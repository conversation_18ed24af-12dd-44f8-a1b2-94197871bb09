#!/bin/bash
# Gamma Shock CN 备份脚本
# 用于备份重要数据和配置文件

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 项目配置
PROJECT_NAME="gamma-shock-cn"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_BASE_DIR="$PROJECT_DIR/backups"
DATE_FORMAT="%Y%m%d_%H%M%S"
TIMESTAMP=$(date +"$DATE_FORMAT")
BACKUP_DIR="$BACKUP_BASE_DIR/backup_$TIMESTAMP"
MAX_BACKUPS=10  # 保留最近10个备份

# 需要备份的目录和文件
BACKUP_ITEMS=(
    "data"
    "config"
    "logs"
    ".env"
    "requirements.txt"
    "run.py"
    "gamma_shock_us.py"
    "README.md"
    "development_plan.md"
)

# 需要排除的文件和目录
EXCLUDE_PATTERNS=(
    "*.pyc"
    "__pycache__"
    "*.tmp"
    "*.log.old"
    "data/cache/*"
    "*.pid"
    "venv"
    ".git"
    "node_modules"
)

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录..."
    
    mkdir -p "$BACKUP_BASE_DIR"
    mkdir -p "$BACKUP_DIR"
    
    log_success "备份目录创建完成: $BACKUP_DIR"
}

# 备份文件和目录
backup_files() {
    log_info "开始备份文件..."
    
    local backup_list_file="$BACKUP_DIR/backup_list.txt"
    local backup_log_file="$BACKUP_DIR/backup.log"
    
    # 创建备份列表文件
    echo "# Gamma Shock CN 备份列表" > "$backup_list_file"
    echo "# 备份时间: $(date)" >> "$backup_list_file"
    echo "# 备份目录: $BACKUP_DIR" >> "$backup_list_file"
    echo "" >> "$backup_list_file"
    
    # 开始备份日志
    echo "备份开始时间: $(date)" > "$backup_log_file"
    
    for item in "${BACKUP_ITEMS[@]}"; do
        local source_path="$PROJECT_DIR/$item"
        local dest_path="$BACKUP_DIR/$item"
        
        if [ -e "$source_path" ]; then
            log_info "备份: $item"
            
            if [ -d "$source_path" ]; then
                # 备份目录
                mkdir -p "$(dirname "$dest_path")"
                cp -r "$source_path" "$dest_path"
                
                # 清理排除的文件
                for pattern in "${EXCLUDE_PATTERNS[@]}"; do
                    find "$dest_path" -name "$pattern" -type f -delete 2>/dev/null || true
                    find "$dest_path" -name "$pattern" -type d -exec rm -rf {} + 2>/dev/null || true
                done
                
                echo "目录: $item" >> "$backup_list_file"
            else
                # 备份文件
                mkdir -p "$(dirname "$dest_path")"
                cp "$source_path" "$dest_path"
                echo "文件: $item" >> "$backup_list_file"
            fi
            
            echo "✓ 备份完成: $item" >> "$backup_log_file"
        else
            log_warning "跳过不存在的项目: $item"
            echo "✗ 不存在: $item" >> "$backup_log_file"
        fi
    done
    
    echo "备份结束时间: $(date)" >> "$backup_log_file"
    log_success "文件备份完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    local db_backup_dir="$BACKUP_DIR/database"
    mkdir -p "$db_backup_dir"
    
    # SQLite数据库备份
    if [ -f "$PROJECT_DIR/data/gamma_shock.db" ]; then
        cp "$PROJECT_DIR/data/gamma_shock.db" "$db_backup_dir/gamma_shock_$TIMESTAMP.db"
        log_success "SQLite数据库备份完成"
    fi
    
    # PostgreSQL数据库备份（如果使用Docker）
    if command -v docker &> /dev/null && docker ps | grep -q "gamma-shock-postgres"; then
        log_info "备份PostgreSQL数据库..."
        docker exec gamma-shock-postgres pg_dump -U postgres gamma_shock > "$db_backup_dir/postgres_$TIMESTAMP.sql"
        log_success "PostgreSQL数据库备份完成"
    fi
}

# 创建压缩包
create_archive() {
    log_info "创建压缩包..."
    
    local archive_name="gamma_shock_backup_$TIMESTAMP.tar.gz"
    local archive_path="$BACKUP_BASE_DIR/$archive_name"
    
    cd "$BACKUP_BASE_DIR"
    tar -czf "$archive_name" "backup_$TIMESTAMP"
    
    # 计算文件大小
    local archive_size=$(du -h "$archive_path" | cut -f1)
    
    log_success "压缩包创建完成: $archive_name (大小: $archive_size)"
    
    # 创建最新备份的符号链接
    ln -sf "$archive_name" "$BACKUP_BASE_DIR/latest_backup.tar.gz"
    
    echo "$archive_path"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    cd "$BACKUP_BASE_DIR"
    
    # 清理旧的备份目录
    local backup_dirs=($(ls -1d backup_* 2>/dev/null | sort -r))
    if [ ${#backup_dirs[@]} -gt $MAX_BACKUPS ]; then
        for ((i=$MAX_BACKUPS; i<${#backup_dirs[@]}; i++)); do
            log_info "删除旧备份目录: ${backup_dirs[$i]}"
            rm -rf "${backup_dirs[$i]}"
        done
    fi
    
    # 清理旧的压缩包
    local archive_files=($(ls -1 gamma_shock_backup_*.tar.gz 2>/dev/null | sort -r))
    if [ ${#archive_files[@]} -gt $MAX_BACKUPS ]; then
        for ((i=$MAX_BACKUPS; i<${#archive_files[@]}; i++)); do
            log_info "删除旧备份文件: ${archive_files[$i]}"
            rm -f "${archive_files[$i]}"
        done
    fi
    
    log_success "旧备份清理完成"
}

# 验证备份
verify_backup() {
    log_info "验证备份完整性..."
    
    local archive_path="$1"
    
    if [ ! -f "$archive_path" ]; then
        log_error "备份文件不存在: $archive_path"
        return 1
    fi
    
    # 测试压缩包完整性
    if tar -tzf "$archive_path" >/dev/null 2>&1; then
        log_success "备份文件完整性验证通过"
    else
        log_error "备份文件损坏"
        return 1
    fi
    
    # 检查备份内容
    local file_count=$(tar -tzf "$archive_path" | wc -l)
    log_info "备份包含 $file_count 个文件/目录"
    
    return 0
}

# 生成备份报告
generate_report() {
    log_info "生成备份报告..."
    
    local report_file="$BACKUP_BASE_DIR/backup_report_$TIMESTAMP.txt"
    local archive_path="$1"
    
    cat > "$report_file" << EOF
# Gamma Shock CN 备份报告

## 备份信息
- 备份时间: $(date)
- 备份版本: $TIMESTAMP
- 项目目录: $PROJECT_DIR
- 备份目录: $BACKUP_DIR
- 压缩包: $(basename "$archive_path")
- 文件大小: $(du -h "$archive_path" | cut -f1)

## 备份内容
EOF
    
    for item in "${BACKUP_ITEMS[@]}"; do
        if [ -e "$PROJECT_DIR/$item" ]; then
            echo "✓ $item" >> "$report_file"
        else
            echo "✗ $item (不存在)" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

## 系统信息
- 操作系统: $(uname -s)
- 主机名: $(hostname)
- 用户: $(whoami)
- Python版本: $(python3 --version 2>/dev/null || echo "未安装")
- Git版本: $(git --version 2>/dev/null || echo "未安装")

## 备份统计
- 总文件数: $(tar -tzf "$archive_path" | wc -l)
- 压缩比: $(echo "scale=2; $(stat -f%z "$archive_path") * 100 / $(du -sb "$BACKUP_DIR" | cut -f1)" | bc 2>/dev/null || echo "N/A")%

## 恢复说明
1. 解压备份文件: tar -xzf $(basename "$archive_path")
2. 进入备份目录: cd backup_$TIMESTAMP
3. 复制文件到项目目录
4. 恢复数据库（如果需要）
5. 重新安装依赖: pip install -r requirements.txt
6. 启动应用: ./start.sh

EOF
    
    log_success "备份报告生成完成: $report_file"
}

# 发送备份通知
send_notification() {
    local archive_path="$1"
    local archive_size=$(du -h "$archive_path" | cut -f1)
    
    log_info "发送备份通知..."
    
    # 这里可以添加邮件通知或其他通知方式
    # 例如：发送邮件、Slack通知、微信通知等
    
    log_info "备份完成通知:"
    echo "  项目: $PROJECT_NAME"
    echo "  时间: $(date)"
    echo "  文件: $(basename "$archive_path")"
    echo "  大小: $archive_size"
}

# 列出备份
list_backups() {
    log_info "备份列表:"
    
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        log_warning "备份目录不存在"
        return
    fi
    
    cd "$BACKUP_BASE_DIR"
    
    echo "压缩包备份:"
    ls -lh gamma_shock_backup_*.tar.gz 2>/dev/null | while read -r line; do
        echo "  $line"
    done
    
    echo ""
    echo "目录备份:"
    ls -ld backup_* 2>/dev/null | while read -r line; do
        echo "  $line"
    done
}

# 恢复备份
restore_backup() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "请指定要恢复的备份文件"
        return 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_warning "恢复备份将覆盖现有文件，是否继续？(y/N)"
    read -r confirm
    
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
        log_info "恢复操作已取消"
        return 0
    fi
    
    log_info "开始恢复备份: $backup_file"
    
    # 创建当前状态的备份
    log_info "创建当前状态备份..."
    local current_backup="$BACKUP_BASE_DIR/before_restore_$(date +"$DATE_FORMAT").tar.gz"
    tar -czf "$current_backup" -C "$PROJECT_DIR" . --exclude="backups" --exclude="venv" --exclude=".git"
    
    # 解压备份文件
    local temp_dir="/tmp/gamma_shock_restore_$$"
    mkdir -p "$temp_dir"
    
    tar -xzf "$backup_file" -C "$temp_dir"
    
    # 恢复文件
    local backup_content_dir=$(find "$temp_dir" -name "backup_*" -type d | head -1)
    
    if [ -z "$backup_content_dir" ]; then
        log_error "备份文件格式错误"
        rm -rf "$temp_dir"
        return 1
    fi
    
    cp -r "$backup_content_dir"/* "$PROJECT_DIR/"
    
    # 清理临时目录
    rm -rf "$temp_dir"
    
    log_success "备份恢复完成"
    log_info "当前状态已备份到: $current_backup"
}

# 主函数
main() {
    local command=${1:-backup}
    
    case $command in
        "backup")
            log_info "开始备份 $PROJECT_NAME..."
            create_backup_dir
            backup_files
            backup_database
            local archive_path=$(create_archive)
            cleanup_old_backups
            
            if verify_backup "$archive_path"; then
                generate_report "$archive_path"
                send_notification "$archive_path"
                log_success "备份完成: $archive_path"
            else
                log_error "备份验证失败"
                exit 1
            fi
            ;;
        "list")
            list_backups
            ;;
        "restore")
            restore_backup "$2"
            ;;
        "verify")
            if [ -z "$2" ]; then
                log_error "请指定要验证的备份文件"
                exit 1
            fi
            verify_backup "$2"
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        *)
            echo "使用方法: $0 {backup|list|restore|verify|cleanup}"
            echo ""
            echo "命令说明:"
            echo "  backup           - 创建新备份"
            echo "  list             - 列出所有备份"
            echo "  restore <file>   - 恢复指定备份"
            echo "  verify <file>    - 验证备份文件"
            echo "  cleanup          - 清理旧备份"
            echo ""
            echo "示例:"
            echo "  $0 backup                                    # 创建备份"
            echo "  $0 list                                      # 列出备份"
            echo "  $0 restore backups/gamma_shock_backup_*.tar.gz  # 恢复备份"
            echo "  $0 verify backups/gamma_shock_backup_*.tar.gz   # 验证备份"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"