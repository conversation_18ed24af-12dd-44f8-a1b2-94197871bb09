#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 测试配置模块

本模块提供测试框架的基础配置，包括：
- Pytest fixtures
- 测试数据生成
- 模拟对象创建
- 测试环境配置

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

# 可选依赖处理
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    pytest = None

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

import pandas as pd
import numpy as np
from datetime import datetime as dt, timedelta
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, MagicMock, patch
import tempfile
import shutil
from pathlib import Path
import logging
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from config.settings import Settings
from data.data_fetcher import MarketData, DataFetcher
from data.cache_manager import CacheManager
from data.data_processor import DataProcessor

# 可选模块导入
try:
    from indicators.technical_indicators import TechnicalIndicators
except ImportError:
    try:
        from signals.technical_indicators import TechnicalIndicators
    except ImportError:
        TechnicalIndicators = None

try:
    from signals.signal_generator import SignalGenerator
except ImportError:
    SignalGenerator = None

try:
    from ai.deepseek_analyzer import DeepSeekAnalyzer
except ImportError:
    DeepSeekAnalyzer = None

try:
    from notifications.email_sender import EmailSender
except ImportError:
    EmailSender = None


@pytest.fixture(scope="session")
def test_config():
    """测试配置fixture"""
    config = Settings()
    
    # 覆盖配置为测试模式
    config.system.DEBUG_MODE = True
    config.system.PRODUCTION_MODE = False
    config.data.DATA_STORAGE['enable_cache'] = False  # 测试时禁用缓存
    config.email.RECIPIENTS = ['<EMAIL>']
    config.ai.DEEPSEEK_API_KEY = 'test_api_key'
    
    return config


@pytest.fixture(scope="session")
def temp_dir():
    """临时目录fixture"""
    temp_path = tempfile.mkdtemp(prefix="gamma_shock_test_")
    yield Path(temp_path)
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def sample_ohlcv_data():
    """样本OHLCV数据fixture"""
    # 生成240个数据点（4小时的分钟数据）
    dates = pd.date_range(
        start=dt.now().replace(hour=9, minute=30, second=0, microsecond=0),
        periods=240,
        freq='1min'
    )
    
    # 生成模拟价格数据
    np.random.seed(42)  # 确保测试结果可重现
    
    # 基础价格
    base_price = 100.0
    
    # 生成价格走势（随机游走 + 趋势）
    returns = np.random.normal(0, 0.002, 240)  # 0.2%的标准差
    trend = np.linspace(0, 0.05, 240)  # 5%的上升趋势
    cumulative_returns = np.cumsum(returns + trend/240)
    
    prices = base_price * (1 + cumulative_returns)
    
    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 模拟日内波动
        volatility = np.random.uniform(0.005, 0.02)  # 0.5%-2%的波动
        
        high = close * (1 + volatility * np.random.uniform(0.3, 1.0))
        low = close * (1 - volatility * np.random.uniform(0.3, 1.0))
        
        if i == 0:
            open_price = close
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.001))
        
        # 确保OHLC逻辑正确
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # 生成成交量（基于价格变化）
        price_change = abs(close - open_price) / open_price
        base_volume = 1000000
        volume = int(base_volume * (1 + price_change * 10) * np.random.uniform(0.5, 2.0))
        
        data.append({
            'datetime': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_market_data(sample_ohlcv_data):
    """样本市场数据fixture"""
    latest = sample_ohlcv_data.iloc[-1]
    
    return MarketData(
        symbol='TEST001',
        name='测试标的',
        open_price=latest['open'],
        high_price=latest['high'],
        low_price=latest['low'],
        close_price=latest['close'],
        volume=latest['volume'],
        amount=latest['volume'] * latest['close'],
        change_percent=(latest['close'] - latest['open']) / latest['open'] * 100,
        timestamp=latest['datetime']
    )


@pytest.fixture
def sample_signal_data():
    """样本信号数据fixture"""
    return {
        'symbol': 'TEST001',
        'timestamp': dt.now(),
        'signal_type': 'BUY',
        'signal_level': 'LEVEL_2',
        'signal_score': 75.5,
        'confidence': 0.85,
        'indicators': {
            'ema_signal': 1,
            'macd_signal': 1,
            'kdj_signal': 0,
            'williams_signal': 1,
            'volume_signal': 1
        },
        'market_data': {
            'current_price': 102.50,
            'change_pct': 2.5,
            'volume_ratio': 1.8
        },
        'ai_analysis': {
            'sentiment': 'positive',
            'risk_level': 'medium',
            'recommendation': '适量买入'
        }
    }


@pytest.fixture
def mock_akshare():
    """模拟AKShare数据源"""
    with patch('akshare.stock_zh_a_spot_em') as mock_spot, \
         patch('akshare.stock_zh_a_hist') as mock_hist, \
         patch('akshare.option_finance_board') as mock_option:
        
        # 模拟实时数据
        mock_spot.return_value = pd.DataFrame({
            '代码': ['TEST001'],
            '名称': ['测试股票'],
            '最新价': [102.50],
            '涨跌幅': [2.5],
            '涨跌额': [2.50],
            '成交量': [1500000],
            '成交额': [153750000],
            '最高': [103.00],
            '最低': [100.50],
            '今开': [100.00]
        })
        
        # 模拟历史数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        mock_hist.return_value = pd.DataFrame({
            '日期': dates,
            '开盘': np.random.uniform(95, 105, 100),
            '收盘': np.random.uniform(95, 105, 100),
            '最高': np.random.uniform(100, 110, 100),
            '最低': np.random.uniform(90, 100, 100),
            '成交量': np.random.randint(1000000, 5000000, 100)
        })
        
        # 模拟期权数据
        mock_option.return_value = pd.DataFrame({
            '认购期权成交量': [100000],
            '认沽期权成交量': [80000],
            '认购期权成交额': [50000000],
            '认沽期权成交额': [40000000]
        })
        
        yield {
            'spot': mock_spot,
            'hist': mock_hist,
            'option': mock_option
        }


@pytest.fixture
def mock_deepseek_api():
    """模拟DeepSeek API"""
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message = Mock()
    mock_response.choices[0].message.content = """
    {
        "analysis": "基于当前技术指标分析，市场呈现积极信号",
        "sentiment": "positive",
        "confidence": 0.85,
        "risk_level": "medium",
        "recommendation": "适量买入",
        "key_factors": [
            "EMA多头排列",
            "MACD金叉",
            "成交量放大"
        ]
    }
    """
    
    with patch('openai.OpenAI') as mock_client:
        mock_client.return_value.chat.completions.create.return_value = mock_response
        yield mock_client


@pytest.fixture
def mock_email_server():
    """模拟邮件服务器"""
    with patch('smtplib.SMTP') as mock_smtp:
        mock_server = Mock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        mock_server.send_message.return_value = {}
        yield mock_server


@pytest.fixture
def data_fetcher(test_config, mock_akshare):
    """数据获取器fixture"""
    return DataFetcher()


@pytest.fixture
def cache_manager(temp_dir):
    """缓存管理器fixture"""
    return CacheManager(
        backend='memory',
        max_size=100,
        default_ttl=300
    )


@pytest.fixture
def data_processor(test_config):
    """数据处理器fixture"""
    return DataProcessor()


@pytest.fixture
def technical_indicators(test_config):
    """技术指标计算器fixture"""
    if TechnicalIndicators is not None:
        return TechnicalIndicators()
    else:
        mock = Mock()
        mock.calculate_rsi.return_value = pd.Series([50.0] * 100)
        mock.calculate_macd.return_value = {
            'macd': pd.Series([0.0] * 100),
            'signal': pd.Series([0.0] * 100),
            'histogram': pd.Series([0.0] * 100)
        }
        return mock


@pytest.fixture
def signal_generator(test_config, technical_indicators):
    """信号生成器fixture"""
    if SignalGenerator is not None:
        return SignalGenerator(technical_indicators)
    else:
        mock = Mock()
        mock.generate_signals.return_value = {
            'signal_type': 'buy',
            'strength': 0.75,
            'confidence': 0.85,
            'timestamp': dt.now()
        }
        return mock


@pytest.fixture
def deepseek_analyzer(test_config, mock_deepseek_api):
    """DeepSeek分析器fixture"""
    if DeepSeekAnalyzer is not None:
        return DeepSeekAnalyzer()
    else:
        mock = Mock()
        mock.analyze_market_data.return_value = {
            'sentiment': 'positive',
            'confidence': 0.85,
            'risk_level': 'medium',
            'recommendation': '适量买入'
        }
        return mock


@pytest.fixture
def email_sender(test_config, mock_email_server):
    """邮件发送器fixture"""
    if EmailSender is not None:
        return EmailSender()
    else:
        mock = Mock()
        mock.send_signal_alert.return_value = True
        mock.send_daily_report.return_value = True
        return mock


@pytest.fixture
def sample_technical_indicators():
    """样本技术指标数据fixture"""
    return {
        'ema_5': 102.1,
        'ema_10': 101.8,
        'ema_20': 101.2,
        'ema_60': 100.5,
        'macd': 0.15,
        'macd_signal': 0.12,
        'macd_histogram': 0.03,
        'kdj_k': 75.2,
        'kdj_d': 72.8,
        'kdj_j': 80.0,
        'williams_r': -25.5,
        'williams_smooth': -28.2,
        'rsi': 68.5,
        'bollinger_upper': 105.0,
        'bollinger_middle': 102.0,
        'bollinger_lower': 99.0,
        'volume_ma': 1200000,
        'volume_ratio': 1.8
    }


@pytest.fixture
def sample_volatility_data():
    """样本波动率数据fixture"""
    return {
        'symbol': 'TEST001',
        'timestamp': dt.now(),
        'realized_volatility': 0.25,
        'historical_volatility': 0.22,
        'volatility_percentile': 75.0,
        'volatility_trend': 'increasing',
        'vix_level': 18.5
    }


@pytest.fixture
def sample_pcr_data():
    """样本PCR数据fixture"""
    return {
        'timestamp': dt.now(),
        'put_call_ratio': 0.85,
        'put_volume': 80000,
        'call_volume': 94118,  # 80000 / 0.85
        'put_amount': 40000000,
        'call_amount': 47059000,  # 40000000 / 0.85
        'market_sentiment': 'neutral',
        'sentiment_score': 0.15  # (0.85 - 1.0) * -1
    }


@pytest.fixture(autouse=True)
def setup_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    # 降低第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    yield
    
    # 清理日志处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)


@pytest.fixture
def mock_trading_hours():
    """模拟交易时间"""
    with patch('config.trading_hours.is_trading_time') as mock_trading:
        mock_trading.return_value = True
        yield mock_trading


@pytest.fixture
def performance_monitor():
    """性能监控fixture"""
    import time
    import threading
    
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.start_memory = None
            self.end_memory = None
            if PSUTIL_AVAILABLE and psutil is not None:
                self.process = psutil.Process()
            else:
                self.process = None
        
        def start(self):
            self.start_time = time.time()
            if self.process is not None:
                self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            else:
                self.start_memory = 0
        
        def stop(self):
            self.end_time = time.time()
            if self.process is not None:
                self.end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            else:
                self.end_memory = 0
        
        @property
        def execution_time(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
        
        @property
        def memory_usage(self):
            if self.start_memory is not None and self.end_memory is not None:
                return self.end_memory - self.start_memory
            return None
        
        def report(self):
            return {
                'execution_time': self.execution_time,
                'memory_usage': self.memory_usage,
                'start_memory': self.start_memory,
                'end_memory': self.end_memory
            }
    
    return PerformanceMonitor()


# 测试标记
pytest_plugins = []

# 自定义标记
if PYTEST_AVAILABLE and pytest is not None:
    def pytest_configure(config):
        """配置pytest标记"""
        config.addinivalue_line(
            "markers", "unit: 单元测试"
        )
        config.addinivalue_line(
            "markers", "integration: 集成测试"
        )
        config.addinivalue_line(
            "markers", "performance: 性能测试"
        )
        config.addinivalue_line(
            "markers", "slow: 慢速测试"
        )
        config.addinivalue_line(
            "markers", "network: 需要网络连接的测试"
        )
else:
    def pytest_configure(config):
        pass


# 测试收集钩子
if PYTEST_AVAILABLE and pytest is not None:
    def pytest_collection_modifyitems(config, items):
        """修改测试收集"""
        # 为没有标记的测试添加unit标记
        for item in items:
            if not any(item.iter_markers()):
                item.add_marker(pytest.mark.unit)
else:
    def pytest_collection_modifyitems(config, items):
        pass


# 测试报告钩子
if PYTEST_AVAILABLE and pytest is not None:
    def pytest_runtest_makereport(item, call):
        """生成测试报告"""
        if call.when == "call":
            # 记录测试执行信息
            if hasattr(item, 'performance_data'):
                print(f"\n性能数据: {item.performance_data}")
else:
    def pytest_runtest_makereport(item, call):
        pass


if __name__ == '__main__':
    # 测试fixture功能
    print("=== 测试Fixture功能 ===")
    
    # 测试配置
    config = Settings()
    print(f"配置加载成功: {type(config)}")
    
    # 测试数据生成
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"临时目录: {temp_dir}")
    
    print("\n=== Fixture测试完成 ===")