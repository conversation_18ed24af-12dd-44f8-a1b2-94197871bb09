#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试模块

测试各模块之间的集成和数据流

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import unittest
from unittest.mock import Mock, patch, MagicMock, AsyncMock
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import tempfile
import time

# 尝试导入可选依赖
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    pytest = None

# 导入被测试模块
try:
    from data.data_fetcher import DataFetcher
    from data.data_processor import DataProcessor
    from indicators.technical_indicators import TechnicalIndicators
    from strategy.signal_generator import SignalGenerator
    from strategy.signal_classifier import SignalClassifier
    from ai.deepseek_client import DeepSeekClient
    from notifications.notification_system import NotificationSystem
    from config.config_manager import ConfigManager
    from utils.logger import Logger
except ImportError as e:
    # 如果导入失败，创建Mock对象
    print(f"导入警告: {e}")
    DataFetcher = Mock()
    DataProcessor = Mock()
    TechnicalIndicators = Mock()
    SignalGenerator = Mock()
    SignalClassifier = Mock()
    DeepSeekClient = Mock()
    NotificationSystem = Mock()
    ConfigManager = Mock()
    Logger = Mock()


class TestDataFlow(unittest.TestCase):
    """数据流集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_symbol = 'AAPL'
        self.test_timeframe = '1d'
        self.test_period = 100
        
        # 创建测试数据
        self.sample_data = self._create_sample_data()
        
        # 初始化组件
        self.config_manager = ConfigManager() if ConfigManager != Mock else Mock()
        self.data_fetcher = DataFetcher() if DataFetcher != Mock else Mock()
        self.data_processor = DataProcessor() if DataProcessor != Mock else Mock()
        self.technical_indicators = TechnicalIndicators() if TechnicalIndicators != Mock else Mock()
    
    def _create_sample_data(self) -> pd.DataFrame:
        """创建样本数据"""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)  # 确保可重复性
        
        # 生成模拟价格数据
        base_price = 150.0
        price_changes = np.random.normal(0, 2, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] + change
            prices.append(max(new_price, 1.0))  # 确保价格为正
        
        # 生成OHLCV数据
        data = {
            'timestamp': dates,
            'open': [p * (1 + np.random.uniform(-0.01, 0.01)) for p in prices],
            'high': [p * (1 + np.random.uniform(0, 0.02)) for p in prices],
            'low': [p * (1 + np.random.uniform(-0.02, 0)) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 100)
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def test_data_fetching_and_processing(self):
        """测试数据获取和处理流程"""
        # 模拟数据获取
        if hasattr(self.data_fetcher, 'fetch_data'):
            with patch.object(self.data_fetcher, 'fetch_data', return_value=self.sample_data):
                fetched_data = self.data_fetcher.fetch_data(
                    symbol=self.test_symbol,
                    timeframe=self.test_timeframe,
                    period=self.test_period
                )
                
                self.assertIsInstance(fetched_data, pd.DataFrame)
                self.assertGreater(len(fetched_data), 0)
                
                # 测试数据处理
                if hasattr(self.data_processor, 'process_data'):
                    with patch.object(self.data_processor, 'process_data', return_value=fetched_data):
                        processed_data = self.data_processor.process_data(fetched_data)
                        
                        self.assertIsInstance(processed_data, pd.DataFrame)
                        self.assertEqual(len(processed_data), len(fetched_data))
    
    def test_indicator_calculation_flow(self):
        """测试指标计算流程"""
        if hasattr(self.technical_indicators, 'calculate_all_indicators'):
            # 模拟指标计算
            mock_indicators = {
                'sma_20': np.random.random(len(self.sample_data)),
                'ema_12': np.random.random(len(self.sample_data)),
                'rsi': np.random.uniform(0, 100, len(self.sample_data)),
                'macd': np.random.random(len(self.sample_data)),
                'bollinger_upper': np.random.random(len(self.sample_data)),
                'bollinger_lower': np.random.random(len(self.sample_data))
            }
            
            with patch.object(self.technical_indicators, 'calculate_all_indicators', return_value=mock_indicators):
                indicators = self.technical_indicators.calculate_all_indicators(self.sample_data)
                
                self.assertIsInstance(indicators, dict)
                self.assertIn('sma_20', indicators)
                self.assertIn('rsi', indicators)
                self.assertIn('macd', indicators)
                
                # 验证指标数据长度
                for indicator_name, values in indicators.items():
                    if values is not None:
                        self.assertEqual(len(values), len(self.sample_data))


class TestSignalGenerationFlow(unittest.TestCase):
    """信号生成流程集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.sample_data = self._create_sample_data()
        self.sample_indicators = self._create_sample_indicators()
        
        self.signal_generator = SignalGenerator() if SignalGenerator != Mock else Mock()
        self.signal_classifier = SignalClassifier() if SignalClassifier != Mock else Mock()
        self.ai_client = DeepSeekClient() if DeepSeekClient != Mock else Mock()
    
    def _create_sample_data(self) -> pd.DataFrame:
        """创建样本数据"""
        dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        data = {
            'timestamp': dates,
            'open': np.random.uniform(145, 155, 50),
            'high': np.random.uniform(150, 160, 50),
            'low': np.random.uniform(140, 150, 50),
            'close': np.random.uniform(145, 155, 50),
            'volume': np.random.randint(1000000, 5000000, 50)
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _create_sample_indicators(self) -> Dict[str, np.ndarray]:
        """创建样本指标数据"""
        length = len(self.sample_data)
        return {
            'sma_20': np.random.uniform(145, 155, length),
            'ema_12': np.random.uniform(145, 155, length),
            'rsi': np.random.uniform(30, 70, length),
            'macd': np.random.uniform(-2, 2, length),
            'macd_signal': np.random.uniform(-2, 2, length),
            'bollinger_upper': np.random.uniform(155, 165, length),
            'bollinger_lower': np.random.uniform(135, 145, length)
        }
    
    def test_signal_generation_and_classification(self):
        """测试信号生成和分类流程"""
        # 模拟信号生成
        mock_signals = [
            {
                'timestamp': datetime.now(),
                'symbol': 'AAPL',
                'signal_type': 'BUY',
                'strength': 0.8,
                'price': 150.0,
                'indicators': self.sample_indicators
            },
            {
                'timestamp': datetime.now(),
                'symbol': 'AAPL',
                'signal_type': 'SELL',
                'strength': 0.6,
                'price': 148.0,
                'indicators': self.sample_indicators
            }
        ]
        
        if hasattr(self.signal_generator, 'generate_signals'):
            with patch.object(self.signal_generator, 'generate_signals', return_value=mock_signals):
                signals = self.signal_generator.generate_signals(
                    data=self.sample_data,
                    indicators=self.sample_indicators
                )
                
                self.assertIsInstance(signals, list)
                self.assertGreater(len(signals), 0)
                
                # 测试信号分类
                if hasattr(self.signal_classifier, 'classify_signal'):
                    for signal in signals:
                        mock_classification = {
                            'category': 'strong_buy' if signal['signal_type'] == 'BUY' else 'weak_sell',
                            'confidence': 0.85,
                            'risk_level': 'medium'
                        }
                        
                        with patch.object(self.signal_classifier, 'classify_signal', return_value=mock_classification):
                            classification = self.signal_classifier.classify_signal(signal)
                            
                            self.assertIsInstance(classification, dict)
                            self.assertIn('category', classification)
                            self.assertIn('confidence', classification)
    
    async def test_ai_signal_confirmation(self):
        """测试AI信号确认流程"""
        test_signal = {
            'timestamp': datetime.now(),
            'symbol': 'AAPL',
            'signal_type': 'BUY',
            'strength': 0.8,
            'price': 150.0,
            'indicators': self.sample_indicators
        }
        
        if hasattr(self.ai_client, 'analyze_signal'):
            mock_ai_response = {
                'confirmation': True,
                'confidence': 0.75,
                'reasoning': 'Strong technical indicators support the buy signal',
                'risk_assessment': 'Medium risk with good reward potential'
            }
            
            with patch.object(self.ai_client, 'analyze_signal', return_value=mock_ai_response):
                ai_analysis = await self.ai_client.analyze_signal(test_signal)
                
                self.assertIsInstance(ai_analysis, dict)
                self.assertIn('confirmation', ai_analysis)
                self.assertIn('confidence', ai_analysis)
                self.assertIsInstance(ai_analysis['confirmation'], bool)


class TestNotificationFlow(unittest.TestCase):
    """通知流程集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.notification_system = NotificationSystem() if NotificationSystem != Mock else Mock()
        
        self.test_signal = {
            'timestamp': datetime.now(),
            'symbol': 'AAPL',
            'signal_type': 'BUY',
            'strength': 0.9,
            'price': 150.0,
            'confidence': 0.85,
            'ai_confirmation': True
        }
    
    async def test_signal_to_notification_flow(self):
        """测试从信号到通知的完整流程"""
        if hasattr(self.notification_system, 'process_signal'):
            mock_notification_result = {
                'sent': True,
                'notification_id': 'notif_001',
                'timestamp': datetime.now(),
                'recipients': ['<EMAIL>']
            }
            
            with patch.object(self.notification_system, 'process_signal', return_value=mock_notification_result):
                result = await self.notification_system.process_signal(self.test_signal)
                
                self.assertIsInstance(result, dict)
                self.assertIn('sent', result)
                self.assertTrue(result['sent'])
    
    def test_notification_filtering(self):
        """测试通知过滤逻辑"""
        if hasattr(self.notification_system, 'should_notify'):
            # 测试高强度信号
            high_strength_signal = self.test_signal.copy()
            high_strength_signal['strength'] = 0.9
            
            with patch.object(self.notification_system, 'should_notify', return_value=True):
                should_notify = self.notification_system.should_notify(high_strength_signal)
                self.assertTrue(should_notify)
            
            # 测试低强度信号
            low_strength_signal = self.test_signal.copy()
            low_strength_signal['strength'] = 0.3
            
            with patch.object(self.notification_system, 'should_notify', return_value=False):
                should_notify = self.notification_system.should_notify(low_strength_signal)
                self.assertFalse(should_notify)


class TestEndToEndFlow(unittest.TestCase):
    """端到端流程集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 初始化所有组件
        self.config_manager = ConfigManager() if ConfigManager != Mock else Mock()
        self.data_fetcher = DataFetcher() if DataFetcher != Mock else Mock()
        self.data_processor = DataProcessor() if DataProcessor != Mock else Mock()
        self.technical_indicators = TechnicalIndicators() if TechnicalIndicators != Mock else Mock()
        self.signal_generator = SignalGenerator() if SignalGenerator != Mock else Mock()
        self.signal_classifier = SignalClassifier() if SignalClassifier != Mock else Mock()
        self.ai_client = DeepSeekClient() if DeepSeekClient != Mock else Mock()
        self.notification_system = NotificationSystem() if NotificationSystem != Mock else Mock()
        
        self.test_symbol = 'AAPL'
        self.sample_data = self._create_sample_data()
    
    def _create_sample_data(self) -> pd.DataFrame:
        """创建样本数据"""
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        np.random.seed(42)
        
        data = {
            'timestamp': dates,
            'open': np.random.uniform(145, 155, 30),
            'high': np.random.uniform(150, 160, 30),
            'low': np.random.uniform(140, 150, 30),
            'close': np.random.uniform(145, 155, 30),
            'volume': np.random.randint(1000000, 5000000, 30)
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    async def test_complete_trading_pipeline(self):
        """测试完整的交易流水线"""
        # 1. 数据获取
        if hasattr(self.data_fetcher, 'fetch_data'):
            with patch.object(self.data_fetcher, 'fetch_data', return_value=self.sample_data):
                raw_data = self.data_fetcher.fetch_data(self.test_symbol)
                self.assertIsNotNone(raw_data)
        
        # 2. 数据处理
        if hasattr(self.data_processor, 'process_data'):
            with patch.object(self.data_processor, 'process_data', return_value=self.sample_data):
                processed_data = self.data_processor.process_data(raw_data)
                self.assertIsNotNone(processed_data)
        
        # 3. 技术指标计算
        mock_indicators = {
            'sma_20': np.random.random(len(self.sample_data)),
            'rsi': np.random.uniform(30, 70, len(self.sample_data)),
            'macd': np.random.random(len(self.sample_data))
        }
        
        if hasattr(self.technical_indicators, 'calculate_all_indicators'):
            with patch.object(self.technical_indicators, 'calculate_all_indicators', return_value=mock_indicators):
                indicators = self.technical_indicators.calculate_all_indicators(processed_data)
                self.assertIsNotNone(indicators)
        
        # 4. 信号生成
        mock_signals = [{
            'timestamp': datetime.now(),
            'symbol': self.test_symbol,
            'signal_type': 'BUY',
            'strength': 0.8,
            'price': 150.0
        }]
        
        if hasattr(self.signal_generator, 'generate_signals'):
            with patch.object(self.signal_generator, 'generate_signals', return_value=mock_signals):
                signals = self.signal_generator.generate_signals(processed_data, indicators)
                self.assertIsNotNone(signals)
                self.assertGreater(len(signals), 0)
        
        # 5. 信号分类
        if hasattr(self.signal_classifier, 'classify_signal'):
            for signal in signals:
                mock_classification = {
                    'category': 'strong_buy',
                    'confidence': 0.85
                }
                
                with patch.object(self.signal_classifier, 'classify_signal', return_value=mock_classification):
                    classification = self.signal_classifier.classify_signal(signal)
                    self.assertIsNotNone(classification)
        
        # 6. AI确认
        if hasattr(self.ai_client, 'analyze_signal'):
            for signal in signals:
                mock_ai_response = {
                    'confirmation': True,
                    'confidence': 0.75
                }
                
                with patch.object(self.ai_client, 'analyze_signal', return_value=mock_ai_response):
                    ai_analysis = await self.ai_client.analyze_signal(signal)
                    self.assertIsNotNone(ai_analysis)
        
        # 7. 通知发送
        if hasattr(self.notification_system, 'send_signal_notification'):
            for signal in signals:
                mock_notification_result = {
                    'sent': True,
                    'notification_id': 'notif_001'
                }
                
                with patch.object(self.notification_system, 'send_signal_notification', return_value=mock_notification_result):
                    notification_result = await self.notification_system.send_signal_notification(signal)
                    self.assertIsNotNone(notification_result)
                    self.assertTrue(notification_result['sent'])
    
    def test_error_handling_in_pipeline(self):
        """测试流水线中的错误处理"""
        # 测试数据获取失败
        if hasattr(self.data_fetcher, 'fetch_data'):
            with patch.object(self.data_fetcher, 'fetch_data', side_effect=Exception("数据获取失败")):
                with self.assertRaises(Exception):
                    self.data_fetcher.fetch_data(self.test_symbol)
        
        # 测试指标计算失败
        if hasattr(self.technical_indicators, 'calculate_all_indicators'):
            with patch.object(self.technical_indicators, 'calculate_all_indicators', side_effect=Exception("指标计算失败")):
                with self.assertRaises(Exception):
                    self.technical_indicators.calculate_all_indicators(self.sample_data)
    
    def test_performance_benchmarks(self):
        """测试性能基准"""
        # 测试数据处理性能
        start_time = time.time()
        
        if hasattr(self.data_processor, 'process_data'):
            with patch.object(self.data_processor, 'process_data', return_value=self.sample_data):
                for _ in range(10):
                    self.data_processor.process_data(self.sample_data)
        
        processing_time = time.time() - start_time
        
        # 确保处理时间在合理范围内（10次处理应该在1秒内完成）
        self.assertLess(processing_time, 1.0, "数据处理性能不达标")
    
    def test_data_consistency(self):
        """测试数据一致性"""
        # 确保数据在各个阶段保持一致性
        original_length = len(self.sample_data)
        
        # 模拟数据处理后长度保持一致
        if hasattr(self.data_processor, 'process_data'):
            with patch.object(self.data_processor, 'process_data', return_value=self.sample_data):
                processed_data = self.data_processor.process_data(self.sample_data)
                self.assertEqual(len(processed_data), original_length)
        
        # 模拟指标计算后数据点数量一致
        mock_indicators = {
            'sma_20': np.random.random(original_length),
            'rsi': np.random.uniform(30, 70, original_length)
        }
        
        if hasattr(self.technical_indicators, 'calculate_all_indicators'):
            with patch.object(self.technical_indicators, 'calculate_all_indicators', return_value=mock_indicators):
                indicators = self.technical_indicators.calculate_all_indicators(self.sample_data)
                
                for indicator_name, values in indicators.items():
                    if values is not None:
                        self.assertEqual(len(values), original_length, 
                                       f"指标 {indicator_name} 的数据点数量不一致")


class TestConfigurationIntegration(unittest.TestCase):
    """配置集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager() if ConfigManager != Mock else Mock()
        
        # 创建临时配置文件
        self.temp_config = {
            'data_source': {
                'provider': 'yfinance',
                'api_key': 'test_key'
            },
            'trading': {
                'symbols': ['AAPL', 'GOOGL'],
                'timeframes': ['1d', '1h']
            },
            'notifications': {
                'email': {
                    'enabled': True,
                    'smtp_server': 'smtp.test.com'
                }
            }
        }
    
    def test_config_loading_and_validation(self):
        """测试配置加载和验证"""
        if hasattr(self.config_manager, 'load_config'):
            with patch.object(self.config_manager, 'load_config', return_value=self.temp_config):
                config = self.config_manager.load_config()
                
                self.assertIsInstance(config, dict)
                self.assertIn('data_source', config)
                self.assertIn('trading', config)
                self.assertIn('notifications', config)
        
        if hasattr(self.config_manager, 'validate_config'):
            with patch.object(self.config_manager, 'validate_config', return_value=True):
                is_valid = self.config_manager.validate_config(self.temp_config)
                self.assertTrue(is_valid)
    
    def test_config_updates(self):
        """测试配置更新"""
        if hasattr(self.config_manager, 'update_config'):
            new_settings = {
                'trading': {
                    'symbols': ['AAPL', 'GOOGL', 'MSFT']
                }
            }
            
            with patch.object(self.config_manager, 'update_config', return_value=True):
                result = self.config_manager.update_config(new_settings)
                self.assertTrue(result)


if __name__ == '__main__':
    # 设置日志
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    if PYTEST_AVAILABLE:
        pytest.main([__file__, '-v'])
    else:
        unittest.main(verbosity=2)