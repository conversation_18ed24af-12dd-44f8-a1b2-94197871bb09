#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试模块

测试系统各组件的性能和响应时间

作者: Gamma Shock Team
创建时间: 2024-12-19
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import unittest
from unittest.mock import Mock, patch, MagicMock
import time
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import threading
import multiprocessing
import psutil
import gc
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import memory_profiler

# 尝试导入可选依赖
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    pytest = None

try:
    import cProfile
    import pstats
    PROFILING_AVAILABLE = True
except ImportError:
    PROFILING_AVAILABLE = False
    cProfile = None
    pstats = None

# 导入被测试模块
try:
    from data.data_fetcher import DataFetcher
    from data.data_processor import DataProcessor
    from indicators.technical_indicators import TechnicalIndicators
    from strategy.signal_generator import SignalGenerator
    from strategy.signal_classifier import SignalClassifier
    from ai.deepseek_client import DeepSeekClient
    from notifications.notification_system import NotificationSystem
    from config.config_manager import ConfigManager
except ImportError as e:
    print(f"导入警告: {e}")
    DataFetcher = Mock()
    DataProcessor = Mock()
    TechnicalIndicators = Mock()
    SignalGenerator = Mock()
    SignalClassifier = Mock()
    DeepSeekClient = Mock()
    NotificationSystem = Mock()
    ConfigManager = Mock()


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self, name: str = "Operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.perf_counter()
        self.duration = self.end_time - self.start_time
    
    def get_duration(self) -> float:
        """获取执行时间（秒）"""
        return getattr(self, 'duration', 0.0)


class MemoryProfiler:
    """内存分析器"""
    
    def __init__(self):
        self.initial_memory = None
        self.peak_memory = None
        self.final_memory = None
    
    def start(self):
        """开始内存监控"""
        gc.collect()  # 强制垃圾回收
        self.initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.initial_memory
    
    def update_peak(self):
        """更新峰值内存"""
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory
    
    def stop(self):
        """停止内存监控"""
        gc.collect()
        self.final_memory = psutil.Process().memory_info().rss / 1024 / 1024
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        return {
            'initial_mb': self.initial_memory or 0,
            'peak_mb': self.peak_memory or 0,
            'final_mb': self.final_memory or 0,
            'increase_mb': (self.final_memory or 0) - (self.initial_memory or 0)
        }


class TestDataProcessingPerformance(unittest.TestCase):
    """数据处理性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.data_fetcher = DataFetcher() if DataFetcher != Mock else Mock()
        self.data_processor = DataProcessor() if DataProcessor != Mock else Mock()
        
        # 创建不同大小的测试数据集
        self.small_dataset = self._create_test_data(100)  # 100条记录
        self.medium_dataset = self._create_test_data(1000)  # 1000条记录
        self.large_dataset = self._create_test_data(10000)  # 10000条记录
    
    def _create_test_data(self, size: int) -> pd.DataFrame:
        """创建测试数据"""
        dates = pd.date_range(start='2020-01-01', periods=size, freq='D')
        np.random.seed(42)
        
        data = {
            'timestamp': dates,
            'open': np.random.uniform(100, 200, size),
            'high': np.random.uniform(150, 250, size),
            'low': np.random.uniform(50, 150, size),
            'close': np.random.uniform(100, 200, size),
            'volume': np.random.randint(1000000, 10000000, size)
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def test_data_fetching_performance(self):
        """测试数据获取性能"""
        test_cases = [
            ('small', self.small_dataset),
            ('medium', self.medium_dataset),
            ('large', self.large_dataset)
        ]
        
        for case_name, dataset in test_cases:
            with self.subTest(case=case_name):
                memory_profiler = MemoryProfiler()
                memory_profiler.start()
                
                with PerformanceTimer(f"Data fetching - {case_name}") as timer:
                    if hasattr(self.data_fetcher, 'fetch_data'):
                        with patch.object(self.data_fetcher, 'fetch_data', return_value=dataset):
                            result = self.data_fetcher.fetch_data('AAPL')
                            self.assertIsNotNone(result)
                
                memory_profiler.stop()
                
                # 性能断言
                duration = timer.get_duration()
                memory_usage = memory_profiler.get_memory_usage()
                
                print(f"\n{case_name} 数据获取性能:")
                print(f"  执行时间: {duration:.4f}秒")
                print(f"  内存使用: {memory_usage['increase_mb']:.2f}MB")
                
                # 性能要求
                if case_name == 'small':
                    self.assertLess(duration, 0.1, "小数据集获取应在0.1秒内完成")
                elif case_name == 'medium':
                    self.assertLess(duration, 0.5, "中等数据集获取应在0.5秒内完成")
                elif case_name == 'large':
                    self.assertLess(duration, 2.0, "大数据集获取应在2秒内完成")
    
    def test_data_processing_performance(self):
        """测试数据处理性能"""
        test_cases = [
            ('small', self.small_dataset),
            ('medium', self.medium_dataset),
            ('large', self.large_dataset)
        ]
        
        for case_name, dataset in test_cases:
            with self.subTest(case=case_name):
                memory_profiler = MemoryProfiler()
                memory_profiler.start()
                
                with PerformanceTimer(f"Data processing - {case_name}") as timer:
                    if hasattr(self.data_processor, 'process_data'):
                        with patch.object(self.data_processor, 'process_data', return_value=dataset):
                            result = self.data_processor.process_data(dataset)
                            self.assertIsNotNone(result)
                
                memory_profiler.stop()
                
                duration = timer.get_duration()
                memory_usage = memory_profiler.get_memory_usage()
                
                print(f"\n{case_name} 数据处理性能:")
                print(f"  执行时间: {duration:.4f}秒")
                print(f"  内存使用: {memory_usage['increase_mb']:.2f}MB")
                
                # 性能要求
                if case_name == 'small':
                    self.assertLess(duration, 0.05, "小数据集处理应在0.05秒内完成")
                elif case_name == 'medium':
                    self.assertLess(duration, 0.2, "中等数据集处理应在0.2秒内完成")
                elif case_name == 'large':
                    self.assertLess(duration, 1.0, "大数据集处理应在1秒内完成")
    
    def test_concurrent_data_processing(self):
        """测试并发数据处理性能"""
        def process_data_task(dataset):
            """数据处理任务"""
            if hasattr(self.data_processor, 'process_data'):
                with patch.object(self.data_processor, 'process_data', return_value=dataset):
                    return self.data_processor.process_data(dataset)
            return dataset
        
        # 测试不同并发级别
        concurrency_levels = [1, 2, 4, 8]
        datasets = [self.medium_dataset] * 8
        
        for concurrency in concurrency_levels:
            with self.subTest(concurrency=concurrency):
                with PerformanceTimer(f"Concurrent processing - {concurrency} threads") as timer:
                    with ThreadPoolExecutor(max_workers=concurrency) as executor:
                        futures = [executor.submit(process_data_task, dataset) for dataset in datasets[:concurrency]]
                        results = [future.result() for future in futures]
                
                duration = timer.get_duration()
                throughput = concurrency / duration
                
                print(f"\n并发级别 {concurrency}:")
                print(f"  执行时间: {duration:.4f}秒")
                print(f"  吞吐量: {throughput:.2f} 任务/秒")
                
                self.assertEqual(len(results), concurrency)
                self.assertLess(duration, 5.0, f"并发级别{concurrency}应在5秒内完成")


class TestIndicatorCalculationPerformance(unittest.TestCase):
    """技术指标计算性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.technical_indicators = TechnicalIndicators() if TechnicalIndicators != Mock else Mock()
        
        # 创建测试数据
        self.test_data = self._create_test_data(5000)
        
        # 模拟指标计算结果
        self.mock_indicators = {
            'sma_20': np.random.random(len(self.test_data)),
            'ema_12': np.random.random(len(self.test_data)),
            'rsi': np.random.uniform(0, 100, len(self.test_data)),
            'macd': np.random.random(len(self.test_data)),
            'macd_signal': np.random.random(len(self.test_data)),
            'bollinger_upper': np.random.random(len(self.test_data)),
            'bollinger_lower': np.random.random(len(self.test_data)),
            'stochastic_k': np.random.uniform(0, 100, len(self.test_data)),
            'stochastic_d': np.random.uniform(0, 100, len(self.test_data)),
            'williams_r': np.random.uniform(-100, 0, len(self.test_data))
        }
    
    def _create_test_data(self, size: int) -> pd.DataFrame:
        """创建测试数据"""
        dates = pd.date_range(start='2020-01-01', periods=size, freq='D')
        np.random.seed(42)
        
        data = {
            'timestamp': dates,
            'open': np.random.uniform(100, 200, size),
            'high': np.random.uniform(150, 250, size),
            'low': np.random.uniform(50, 150, size),
            'close': np.random.uniform(100, 200, size),
            'volume': np.random.randint(1000000, 10000000, size)
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def test_individual_indicator_performance(self):
        """测试单个指标计算性能"""
        indicator_methods = [
            'calculate_sma',
            'calculate_ema',
            'calculate_rsi',
            'calculate_macd',
            'calculate_bollinger_bands',
            'calculate_stochastic',
            'calculate_williams_r'
        ]
        
        for method_name in indicator_methods:
            with self.subTest(indicator=method_name):
                if hasattr(self.technical_indicators, method_name):
                    method = getattr(self.technical_indicators, method_name)
                    
                    # 模拟方法返回值
                    mock_result = np.random.random(len(self.test_data))
                    
                    with patch.object(self.technical_indicators, method_name, return_value=mock_result):
                        with PerformanceTimer(f"Indicator - {method_name}") as timer:
                            result = method(self.test_data)
                            self.assertIsNotNone(result)
                        
                        duration = timer.get_duration()
                        print(f"\n{method_name} 性能:")
                        print(f"  执行时间: {duration:.4f}秒")
                        
                        # 单个指标计算应在0.1秒内完成
                        self.assertLess(duration, 0.1, f"{method_name}应在0.1秒内完成")
    
    def test_batch_indicator_calculation(self):
        """测试批量指标计算性能"""
        memory_profiler = MemoryProfiler()
        memory_profiler.start()
        
        with PerformanceTimer("Batch indicator calculation") as timer:
            if hasattr(self.technical_indicators, 'calculate_all_indicators'):
                with patch.object(self.technical_indicators, 'calculate_all_indicators', return_value=self.mock_indicators):
                    result = self.technical_indicators.calculate_all_indicators(self.test_data)
                    self.assertIsNotNone(result)
                    self.assertIsInstance(result, dict)
        
        memory_profiler.stop()
        
        duration = timer.get_duration()
        memory_usage = memory_profiler.get_memory_usage()
        
        print(f"\n批量指标计算性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  内存使用: {memory_usage['increase_mb']:.2f}MB")
        print(f"  指标数量: {len(self.mock_indicators)}")
        
        # 批量计算应在0.5秒内完成
        self.assertLess(duration, 0.5, "批量指标计算应在0.5秒内完成")
        
        # 内存使用应合理
        self.assertLess(memory_usage['increase_mb'], 100, "内存增长应小于100MB")
    
    def test_indicator_calculation_scalability(self):
        """测试指标计算可扩展性"""
        data_sizes = [100, 500, 1000, 2000, 5000]
        
        for size in data_sizes:
            with self.subTest(size=size):
                test_data = self._create_test_data(size)
                mock_indicators = {k: np.random.random(size) for k in self.mock_indicators.keys()}
                
                with PerformanceTimer(f"Indicators - {size} records") as timer:
                    if hasattr(self.technical_indicators, 'calculate_all_indicators'):
                        with patch.object(self.technical_indicators, 'calculate_all_indicators', return_value=mock_indicators):
                            result = self.technical_indicators.calculate_all_indicators(test_data)
                            self.assertIsNotNone(result)
                
                duration = timer.get_duration()
                records_per_second = size / duration if duration > 0 else float('inf')
                
                print(f"\n数据量 {size}:")
                print(f"  执行时间: {duration:.4f}秒")
                print(f"  处理速度: {records_per_second:.0f} 记录/秒")
                
                # 处理速度应保持在合理范围内
                self.assertGreater(records_per_second, 1000, f"处理速度应大于1000记录/秒，当前: {records_per_second:.0f}")


class TestSignalGenerationPerformance(unittest.TestCase):
    """信号生成性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.signal_generator = SignalGenerator() if SignalGenerator != Mock else Mock()
        self.signal_classifier = SignalClassifier() if SignalClassifier != Mock else Mock()
        
        self.test_data = self._create_test_data(1000)
        self.test_indicators = self._create_test_indicators(1000)
    
    def _create_test_data(self, size: int) -> pd.DataFrame:
        """创建测试数据"""
        dates = pd.date_range(start='2024-01-01', periods=size, freq='H')
        np.random.seed(42)
        
        data = {
            'timestamp': dates,
            'open': np.random.uniform(100, 200, size),
            'high': np.random.uniform(150, 250, size),
            'low': np.random.uniform(50, 150, size),
            'close': np.random.uniform(100, 200, size),
            'volume': np.random.randint(1000000, 10000000, size)
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _create_test_indicators(self, size: int) -> Dict[str, np.ndarray]:
        """创建测试指标"""
        return {
            'sma_20': np.random.uniform(100, 200, size),
            'ema_12': np.random.uniform(100, 200, size),
            'rsi': np.random.uniform(20, 80, size),
            'macd': np.random.uniform(-5, 5, size),
            'macd_signal': np.random.uniform(-5, 5, size),
            'bollinger_upper': np.random.uniform(180, 220, size),
            'bollinger_lower': np.random.uniform(80, 120, size)
        }
    
    def test_signal_generation_performance(self):
        """测试信号生成性能"""
        # 模拟信号生成结果
        mock_signals = [
            {
                'timestamp': datetime.now() - timedelta(hours=i),
                'symbol': 'AAPL',
                'signal_type': np.random.choice(['BUY', 'SELL', 'HOLD']),
                'strength': np.random.uniform(0.1, 1.0),
                'price': np.random.uniform(100, 200)
            }
            for i in range(50)  # 生成50个信号
        ]
        
        memory_profiler = MemoryProfiler()
        memory_profiler.start()
        
        with PerformanceTimer("Signal generation") as timer:
            if hasattr(self.signal_generator, 'generate_signals'):
                with patch.object(self.signal_generator, 'generate_signals', return_value=mock_signals):
                    signals = self.signal_generator.generate_signals(
                        data=self.test_data,
                        indicators=self.test_indicators
                    )
                    self.assertIsNotNone(signals)
                    self.assertIsInstance(signals, list)
        
        memory_profiler.stop()
        
        duration = timer.get_duration()
        memory_usage = memory_profiler.get_memory_usage()
        signals_per_second = len(mock_signals) / duration if duration > 0 else float('inf')
        
        print(f"\n信号生成性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  生成信号数: {len(mock_signals)}")
        print(f"  生成速度: {signals_per_second:.0f} 信号/秒")
        print(f"  内存使用: {memory_usage['increase_mb']:.2f}MB")
        
        # 性能要求
        self.assertLess(duration, 1.0, "信号生成应在1秒内完成")
        self.assertGreater(signals_per_second, 50, "信号生成速度应大于50信号/秒")
    
    def test_signal_classification_performance(self):
        """测试信号分类性能"""
        # 创建测试信号
        test_signals = [
            {
                'timestamp': datetime.now(),
                'symbol': 'AAPL',
                'signal_type': 'BUY',
                'strength': 0.8,
                'price': 150.0,
                'indicators': self.test_indicators
            }
            for _ in range(100)
        ]
        
        mock_classification = {
            'category': 'strong_buy',
            'confidence': 0.85,
            'risk_level': 'medium'
        }
        
        with PerformanceTimer("Signal classification") as timer:
            if hasattr(self.signal_classifier, 'classify_signal'):
                with patch.object(self.signal_classifier, 'classify_signal', return_value=mock_classification):
                    classifications = []
                    for signal in test_signals:
                        classification = self.signal_classifier.classify_signal(signal)
                        classifications.append(classification)
                    
                    self.assertEqual(len(classifications), len(test_signals))
        
        duration = timer.get_duration()
        classifications_per_second = len(test_signals) / duration if duration > 0 else float('inf')
        
        print(f"\n信号分类性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  分类信号数: {len(test_signals)}")
        print(f"  分类速度: {classifications_per_second:.0f} 信号/秒")
        
        # 性能要求
        self.assertLess(duration, 0.5, "信号分类应在0.5秒内完成")
        self.assertGreater(classifications_per_second, 200, "信号分类速度应大于200信号/秒")
    
    def test_real_time_signal_processing(self):
        """测试实时信号处理性能"""
        def simulate_real_time_data():
            """模拟实时数据流"""
            for i in range(100):
                yield {
                    'timestamp': datetime.now(),
                    'symbol': 'AAPL',
                    'price': 150.0 + np.random.uniform(-5, 5),
                    'volume': np.random.randint(1000, 10000)
                }
                time.sleep(0.01)  # 模拟10ms间隔
        
        processed_count = 0
        start_time = time.perf_counter()
        
        for data_point in simulate_real_time_data():
            # 模拟信号处理
            if hasattr(self.signal_generator, 'process_real_time_data'):
                with patch.object(self.signal_generator, 'process_real_time_data', return_value=True):
                    result = self.signal_generator.process_real_time_data(data_point)
                    if result:
                        processed_count += 1
            else:
                processed_count += 1
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        processing_rate = processed_count / duration
        
        print(f"\n实时信号处理性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  处理数据点: {processed_count}")
        print(f"  处理速度: {processing_rate:.0f} 数据点/秒")
        
        # 实时处理要求
        self.assertGreater(processing_rate, 50, "实时处理速度应大于50数据点/秒")


class TestAIPerformance(unittest.TestCase):
    """AI模块性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.ai_client = DeepSeekClient() if DeepSeekClient != Mock else Mock()
        
        self.test_signal = {
            'timestamp': datetime.now(),
            'symbol': 'AAPL',
            'signal_type': 'BUY',
            'strength': 0.8,
            'price': 150.0,
            'indicators': {
                'rsi': 65.0,
                'macd': 1.5,
                'sma_20': 148.0
            }
        }
    
    async def test_ai_analysis_performance(self):
        """测试AI分析性能"""
        mock_ai_response = {
            'confirmation': True,
            'confidence': 0.75,
            'reasoning': 'Strong technical indicators support the signal',
            'risk_assessment': 'Medium risk'
        }
        
        # 测试单次分析
        with PerformanceTimer("Single AI analysis") as timer:
            if hasattr(self.ai_client, 'analyze_signal'):
                with patch.object(self.ai_client, 'analyze_signal', return_value=mock_ai_response):
                    result = await self.ai_client.analyze_signal(self.test_signal)
                    self.assertIsNotNone(result)
        
        duration = timer.get_duration()
        print(f"\n单次AI分析性能:")
        print(f"  执行时间: {duration:.4f}秒")
        
        # AI分析应在2秒内完成
        self.assertLess(duration, 2.0, "AI分析应在2秒内完成")
    
    async def test_batch_ai_analysis_performance(self):
        """测试批量AI分析性能"""
        # 创建多个测试信号
        test_signals = [self.test_signal.copy() for _ in range(10)]
        
        mock_ai_response = {
            'confirmation': True,
            'confidence': 0.75,
            'reasoning': 'Analysis complete'
        }
        
        with PerformanceTimer("Batch AI analysis") as timer:
            if hasattr(self.ai_client, 'analyze_signal'):
                with patch.object(self.ai_client, 'analyze_signal', return_value=mock_ai_response):
                    tasks = [self.ai_client.analyze_signal(signal) for signal in test_signals]
                    results = await asyncio.gather(*tasks)
                    
                    self.assertEqual(len(results), len(test_signals))
        
        duration = timer.get_duration()
        analysis_rate = len(test_signals) / duration if duration > 0 else float('inf')
        
        print(f"\n批量AI分析性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  分析信号数: {len(test_signals)}")
        print(f"  分析速度: {analysis_rate:.1f} 信号/秒")
        
        # 批量分析性能要求
        self.assertLess(duration, 10.0, "批量AI分析应在10秒内完成")
        self.assertGreater(analysis_rate, 1.0, "AI分析速度应大于1信号/秒")
    
    async def test_ai_response_time_consistency(self):
        """测试AI响应时间一致性"""
        response_times = []
        mock_ai_response = {'confirmation': True, 'confidence': 0.8}
        
        # 进行多次测试
        for i in range(20):
            with PerformanceTimer(f"AI analysis {i+1}") as timer:
                if hasattr(self.ai_client, 'analyze_signal'):
                    with patch.object(self.ai_client, 'analyze_signal', return_value=mock_ai_response):
                        result = await self.ai_client.analyze_signal(self.test_signal)
                        self.assertIsNotNone(result)
            
            response_times.append(timer.get_duration())
        
        # 计算统计信息
        avg_time = np.mean(response_times)
        std_time = np.std(response_times)
        min_time = np.min(response_times)
        max_time = np.max(response_times)
        
        print(f"\nAI响应时间统计:")
        print(f"  平均时间: {avg_time:.4f}秒")
        print(f"  标准差: {std_time:.4f}秒")
        print(f"  最小时间: {min_time:.4f}秒")
        print(f"  最大时间: {max_time:.4f}秒")
        
        # 一致性要求
        self.assertLess(std_time, avg_time * 0.5, "响应时间标准差应小于平均时间的50%")
        self.assertLess(max_time - min_time, 1.0, "最大最小响应时间差应小于1秒")


class TestNotificationPerformance(unittest.TestCase):
    """通知系统性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.notification_system = NotificationSystem() if NotificationSystem != Mock else Mock()
        
        self.test_notification = {
            'id': 'test_001',
            'title': '测试通知',
            'content': '这是一个测试通知',
            'type': 'email',
            'recipients': ['<EMAIL>'],
            'timestamp': datetime.now()
        }
    
    async def test_single_notification_performance(self):
        """测试单个通知发送性能"""
        mock_result = {'sent': True, 'notification_id': 'notif_001'}
        
        with PerformanceTimer("Single notification") as timer:
            if hasattr(self.notification_system, 'send_notification'):
                with patch.object(self.notification_system, 'send_notification', return_value=mock_result):
                    result = await self.notification_system.send_notification(self.test_notification)
                    self.assertIsNotNone(result)
        
        duration = timer.get_duration()
        print(f"\n单个通知发送性能:")
        print(f"  执行时间: {duration:.4f}秒")
        
        # 单个通知应在3秒内发送完成
        self.assertLess(duration, 3.0, "单个通知应在3秒内发送完成")
    
    async def test_bulk_notification_performance(self):
        """测试批量通知发送性能"""
        # 创建多个通知
        notifications = []
        for i in range(50):
            notification = self.test_notification.copy()
            notification['id'] = f'test_{i:03d}'
            notification['title'] = f'测试通知 {i+1}'
            notifications.append(notification)
        
        mock_results = [{'sent': True, 'notification_id': f'notif_{i:03d}'} for i in range(50)]
        
        with PerformanceTimer("Bulk notifications") as timer:
            if hasattr(self.notification_system, 'send_bulk_notifications'):
                with patch.object(self.notification_system, 'send_bulk_notifications', return_value=mock_results):
                    results = await self.notification_system.send_bulk_notifications(notifications)
                    self.assertEqual(len(results), len(notifications))
        
        duration = timer.get_duration()
        notification_rate = len(notifications) / duration if duration > 0 else float('inf')
        
        print(f"\n批量通知发送性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  通知数量: {len(notifications)}")
        print(f"  发送速度: {notification_rate:.1f} 通知/秒")
        
        # 批量通知性能要求
        self.assertLess(duration, 30.0, "批量通知应在30秒内发送完成")
        self.assertGreater(notification_rate, 1.0, "通知发送速度应大于1通知/秒")
    
    def test_notification_queue_performance(self):
        """测试通知队列性能"""
        # 模拟高频通知入队
        queue_operations = 1000
        
        with PerformanceTimer("Notification queue operations") as timer:
            if hasattr(self.notification_system, 'enqueue_notification'):
                for i in range(queue_operations):
                    notification = self.test_notification.copy()
                    notification['id'] = f'queue_test_{i:04d}'
                    
                    with patch.object(self.notification_system, 'enqueue_notification', return_value=True):
                        result = self.notification_system.enqueue_notification(notification)
                        self.assertTrue(result)
        
        duration = timer.get_duration()
        queue_rate = queue_operations / duration if duration > 0 else float('inf')
        
        print(f"\n通知队列性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  队列操作数: {queue_operations}")
        print(f"  入队速度: {queue_rate:.0f} 操作/秒")
        
        # 队列性能要求
        self.assertLess(duration, 1.0, "队列操作应在1秒内完成")
        self.assertGreater(queue_rate, 1000, "入队速度应大于1000操作/秒")


class TestSystemIntegrationPerformance(unittest.TestCase):
    """系统集成性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 初始化所有组件
        self.data_fetcher = DataFetcher() if DataFetcher != Mock else Mock()
        self.data_processor = DataProcessor() if DataProcessor != Mock else Mock()
        self.technical_indicators = TechnicalIndicators() if TechnicalIndicators != Mock else Mock()
        self.signal_generator = SignalGenerator() if SignalGenerator != Mock else Mock()
        self.ai_client = DeepSeekClient() if DeepSeekClient != Mock else Mock()
        self.notification_system = NotificationSystem() if NotificationSystem != Mock else Mock()
        
        self.test_symbol = 'AAPL'
    
    async def test_end_to_end_pipeline_performance(self):
        """测试端到端流水线性能"""
        memory_profiler = MemoryProfiler()
        memory_profiler.start()
        
        with PerformanceTimer("End-to-end pipeline") as timer:
            # 1. 数据获取
            sample_data = pd.DataFrame({
                'open': np.random.uniform(100, 200, 100),
                'high': np.random.uniform(150, 250, 100),
                'low': np.random.uniform(50, 150, 100),
                'close': np.random.uniform(100, 200, 100),
                'volume': np.random.randint(1000000, 10000000, 100)
            })
            
            if hasattr(self.data_fetcher, 'fetch_data'):
                with patch.object(self.data_fetcher, 'fetch_data', return_value=sample_data):
                    raw_data = self.data_fetcher.fetch_data(self.test_symbol)
            
            # 2. 数据处理
            if hasattr(self.data_processor, 'process_data'):
                with patch.object(self.data_processor, 'process_data', return_value=sample_data):
                    processed_data = self.data_processor.process_data(raw_data)
            
            # 3. 指标计算
            mock_indicators = {
                'sma_20': np.random.random(100),
                'rsi': np.random.uniform(30, 70, 100)
            }
            
            if hasattr(self.technical_indicators, 'calculate_all_indicators'):
                with patch.object(self.technical_indicators, 'calculate_all_indicators', return_value=mock_indicators):
                    indicators = self.technical_indicators.calculate_all_indicators(processed_data)
            
            # 4. 信号生成
            mock_signals = [{
                'timestamp': datetime.now(),
                'symbol': self.test_symbol,
                'signal_type': 'BUY',
                'strength': 0.8
            }]
            
            if hasattr(self.signal_generator, 'generate_signals'):
                with patch.object(self.signal_generator, 'generate_signals', return_value=mock_signals):
                    signals = self.signal_generator.generate_signals(processed_data, indicators)
            
            # 5. AI分析
            mock_ai_response = {'confirmation': True, 'confidence': 0.8}
            
            if hasattr(self.ai_client, 'analyze_signal'):
                with patch.object(self.ai_client, 'analyze_signal', return_value=mock_ai_response):
                    for signal in signals:
                        ai_analysis = await self.ai_client.analyze_signal(signal)
            
            # 6. 通知发送
            mock_notification_result = {'sent': True}
            
            if hasattr(self.notification_system, 'send_notification'):
                with patch.object(self.notification_system, 'send_notification', return_value=mock_notification_result):
                    for signal in signals:
                        notification_result = await self.notification_system.send_notification(signal)
        
        memory_profiler.stop()
        
        duration = timer.get_duration()
        memory_usage = memory_profiler.get_memory_usage()
        
        print(f"\n端到端流水线性能:")
        print(f"  总执行时间: {duration:.4f}秒")
        print(f"  内存使用: {memory_usage['increase_mb']:.2f}MB")
        print(f"  处理符号: {self.test_symbol}")
        
        # 端到端性能要求
        self.assertLess(duration, 10.0, "端到端流水线应在10秒内完成")
        self.assertLess(memory_usage['increase_mb'], 200, "内存增长应小于200MB")
    
    def test_concurrent_symbol_processing(self):
        """测试并发符号处理性能"""
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']
        
        async def process_symbol(symbol):
            """处理单个符号"""
            # 模拟完整的处理流程
            await asyncio.sleep(0.1)  # 模拟处理时间
            return f"Processed {symbol}"
        
        with PerformanceTimer("Concurrent symbol processing") as timer:
            async def run_concurrent_processing():
                tasks = [process_symbol(symbol) for symbol in symbols]
                results = await asyncio.gather(*tasks)
                return results
            
            results = asyncio.run(run_concurrent_processing())
            self.assertEqual(len(results), len(symbols))
        
        duration = timer.get_duration()
        symbols_per_second = len(symbols) / duration if duration > 0 else float('inf')
        
        print(f"\n并发符号处理性能:")
        print(f"  执行时间: {duration:.4f}秒")
        print(f"  处理符号数: {len(symbols)}")
        print(f"  处理速度: {symbols_per_second:.1f} 符号/秒")
        
        # 并发处理性能要求
        self.assertLess(duration, 1.0, "并发符号处理应在1秒内完成")
        self.assertGreater(symbols_per_second, 5.0, "符号处理速度应大于5符号/秒")


if __name__ == '__main__':
    # 设置日志
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("开始性能测试...")
    print(f"CPU核心数: {multiprocessing.cpu_count()}")
    print(f"可用内存: {psutil.virtual_memory().available / 1024 / 1024 / 1024:.1f}GB")
    print("-" * 50)
    
    # 运行测试
    if PYTEST_AVAILABLE:
        pytest.main([__file__, '-v', '-s'])
    else:
        unittest.main(verbosity=2)