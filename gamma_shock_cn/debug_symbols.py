#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试各个标的数据获取情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.data_fetcher import DataFetcher
from config.settings import settings
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_symbol_data_fetching():
    """测试各个标的的数据获取"""
    
    # 初始化数据获取器
    data_fetcher = DataFetcher()
    
    # 主要监控标的
    primary_targets = {
        'CSI1000': '399852',  # 中证1000指数
        'CSI500': '510500',   # 中证500ETF
        'ChiNext': '159915'   # 创业板ETF
    }
    
    print("\n=== 测试主要监控标的数据获取 ===")
    
    for name, symbol in primary_targets.items():
        print(f"\n--- 测试 {name} ({symbol}) ---")
        
        # 测试实时数据
        print(f"获取实时数据...")
        realtime_data = data_fetcher.get_realtime_data(symbol)
        if realtime_data:
            print(f"✅ 实时数据获取成功: {realtime_data.name}, 价格: {realtime_data.close_price}")
        else:
            print(f"❌ 实时数据获取失败")
        
        # 测试历史数据
        print(f"获取历史数据...")
        historical_data = data_fetcher.get_historical_data(symbol, period='1min', limit=10)
        if historical_data is not None and not historical_data.empty:
            print(f"✅ 历史数据获取成功: {len(historical_data)} 条记录")
            print(f"   数据列: {list(historical_data.columns)}")
            if len(historical_data) > 0:
                print(f"   最新数据: {historical_data.iloc[-1].to_dict()}")
        else:
            print(f"❌ 历史数据获取失败")
        
        print("-" * 50)
    
    # 测试辅助监控标的
    auxiliary_targets = {
        'SSE50': '510050',    # 上证50ETF
        'CSI300': '510300',   # 沪深300ETF
        'SZSE100': '159901'   # 深证100ETF
    }
    
    print("\n=== 测试辅助监控标的数据获取 ===")
    
    for name, symbol in auxiliary_targets.items():
        print(f"\n--- 测试 {name} ({symbol}) ---")
        
        # 测试实时数据
        realtime_data = data_fetcher.get_realtime_data(symbol)
        if realtime_data:
            print(f"✅ 实时数据获取成功: {realtime_data.name}")
        else:
            print(f"❌ 实时数据获取失败")
    
    print("\n=== 数据获取测试完成 ===")

if __name__ == "__main__":
    test_symbol_data_fetching()