# A股期权策略跟踪系统分析
这是一个基于技术分析的A股期权交易策略系统，主要特点如下：

### 🎯 核心策略框架
双重确认升级版策略 - 结合多种技术指标进行信号确认，避免假信号

### 📊 技术指标体系 
1. 多均线系统
- EMA8/21/55/125 : 构建多层次趋势判断
- 短期趋势 : EMA8 vs EMA21
- 中期趋势 : EMA21 vs EMA55
- 长期趋势 : EMA55 vs EMA125 

2. KDJ随机指标
- 14周期KDJ，用于超买超卖判断
- J线穿越K线作为预警信号 

3. MACD指标
- 标准12/26/9参数配置
- DIF、DEA、MACD柱状图 

4. 威廉指标变种 （核心创新）
- ZLS线 : 21日EMA平滑的长期线
- CZX线 : 5日EMA平滑的短期线
- C信号 : CZX上穿ZLS且ZLS<0.1（超卖反弹）
- P信号 : ZLS上穿CZX且ZLS>0.25（超买回调） 

5. 成交量分析
- 10根K线成交量（or 成交额）均量
- 异常放量确认：当前成交量（or 成交额） > 前10根K线均值 × 1.3 

6. 波动率分析
- 20日历史波动率计算
- 波动率百分位排名

### 🚦 信号分级系统 
一级信号（强度3-5）- 进场信号
1. 多头进场 : EMA8上穿EMA21 + 当前收盘>EMA8
2. 空头进场 : EMA8下穿EMA21 + 当前收盘<EMA8 

二级信号（强度2-3）- 预警信号
3. 多头预警 : 收盘价介于EMA8和EMA21之间 + J线上穿K线
4. 空头预警 : 收盘价介于EMA8和EMA21之间 + K线上穿J线 

三级信号（强度2-3）- 威廉信号
5. 威廉超卖反弹 : 纯C信号
6. 威廉超买回调 : 纯P信号 四级信号（强度1）- 异常信号
7. 成交量异常 : 仅成交量放大但无明确技术信号

### ⚡ 信号增强机制
- 成交量确认 : +1强度
- 威廉指标确认 : +1强度，可将预警升级为进场信号
- 最低触发阈值 : 信号强度≥2

### 🎯 监控标的
创业板ETF (映射关系：创业板etf，cyb，sz159915，159915)，
中证500ETF (映射关系：中证500，中证500ETF，500etf,zz500，sh510500,399905),
中证1000股指期货(映射关系：中证1000，zz1000，sz399852，399852)

### 🤖 AI增强功能
- 集成DeepSeek大模型进行信号分析
- 自动生成期权策略建议（方向、行权价、到期日、仓位）
- 提供风险管理建议（止损、止盈、滚动策略）
### ⏰ 运行机制
- 数据周期 : 5分钟K线
- 运行时间 : 北京时间9:30-11:30 & 13:00-15:00交易时段
- 缓存机制 : 本地数据缓存，提高效率
- 通知系统 : 邮件 or 企业微信自动推送交易信号
### 💡 策略优势
1. 多重确认 : 避免单一指标的假信号
2. 分级管理 : 根据信号强度调整仓位
3. 威廉创新 : 独特的超买超卖判断机制
4. AI辅助 : 智能化的交易决策支持
5. 实时监控 : 自动化的信号检测和推送

### 分析格式
#### 期权策略建议
**方向**: [买入看涨/看跌期权/观察等待]
**行权价**: [具体价格]
**到期日**: [建议天数范围]
**仓位**: [百分比]

#### 风险管理
**止损**: [具体条件]
**止盈**: [分层策略]
**Roll Up**: [滚动策略]

# 1、数据来源接口

## 1.1 中证1000数据接口

### 1.1.1 中证1000指数的日线数据
stock_zh_index_daily_df = ak.stock_zh_index_daily(symbol="sz399852")
print(stock_zh_index_daily_df)

### 以下代码也可以实现上述功能-腾讯接口
stock_zh_index_daily_tx_df = ak.stock_zh_index_daily_tx(symbol="sz399852")
print(stock_zh_index_daily_tx_df)
#### 注意：stock_zh_index_daily_df获取的数据格式
           date      open      high       low     close       volume
441  2025-07-11  6411.313  6487.440  6388.402  6461.100  28256128300
stock_zh_index_daily_tx_df获取的数据格式
           date     open    close     high      low       amount
441  2025-07-11  6411.31  6461.10  6487.44  6388.40  282561283.0

stock_zh_index_daily_df获取“volume ” 与stock_zh_index_daily_tx_df获取的“amount”都是成交量vol，但是数据格式不一致，volume/1000 = amount


### 1.1.2 中证1000 5分钟和60分钟的技术指标计算
# 接口信息如下：单次返回具体指数指定 period 从 start_date 到 end_date 的之间的近期数据"1","5","15","30","60"分钟
# 5minK线数据源
index_zh_a_hist_min_em_df = ak.index_zh_a_hist_min_em(symbol="399852", period="5", start_date="2025-01-01 09:30:00", end_date="2025-12-11 19:00:00")
# 60minK线数据源
index_zh_a_hist_min_em_df = ak.index_zh_a_hist_min_em(symbol="399852", period="60", start_date="2025-01-01 09:30:00", end_date="2025-12-11 19:00:00")

### 1.1.3 获取过往20天的波动率
#### 中证1000历史波动率
index_option_1000index_qvix_df = ak.index_option_1000index_qvix()
print(index_option_1000index_qvix_df)

### 1.1.4 中证1000分时波动率
index_option_1000index_min_qvix_df = ak.index_option_1000index_min_qvix()
print(index_option_1000index_min_qvix_df)

获取信息，end_date程序运行当日，start_date= end_date - 20days


## 1.2 中证500ETF(股票代码：510500)数据接口

### 1.2.1 中证500ETF的日线数据
stock_zh_index_daily_tx_df = ak.stock_zh_index_daily_tx(symbol="sh510500")
print(stock_zh_index_daily_tx_df)
# 获得的数据格式为
            date   open  close   high    low      amount
2992  2025-07-11  6.035  6.090  6.125  6.017  2491185.00

 Amount = “2491185.00”，amount即成交量vol，如果使用备用接口需要做数据格式归一。
### 备用接口
index_zh_a_hist_df = ak.index_zh_a_hist(symbol="510500", period="daily", start_date="20200101", end_date="20260101")
print(index_zh_a_hist_df)

### 1.2.2 中证500ETF 5分钟和60分钟的数据接口
fund_etf_hist_min_em_df = ak.fund_etf_hist_min_em(symbol="510500", period="5", adjust="", start_date="2025-06-26 09:30:00", end_date="2025-06-26 17:40:00")
print(fund_etf_hist_min_em_df)
fund_etf_hist_min_em_df = ak.fund_etf_hist_min_em(symbol="510500", period="60", adjust="", start_date="2025-06-26 09:30:00", end_date="2025-06-26 17:40:00")
print(fund_etf_hist_min_em_df)

### 1.2.3 中证500etf历史波动率
index_option_500etf_qvix_df = ak.index_option_500etf_qvix()
print(index_option_500etf_qvix_df)
index_option_500etf_qvix_df .to_csv("index_option_500etf_qvix.csv", index=False)

### 1.2.4 中证500etf分时波动率
index_option_500etf_min_qvix_df = ak.index_option_500etf_min_qvix()
print(index_option_500etf_min_qvix_df)
index_option_500etf_min_qvix_df .to_csv("index_option_500etf_min_qvix.csv", index=False)    


## 1.3 创业板ETF(股票代码：159915)数据接口
### 1.3.1 创业板ETF的日线数据
stock_zh_index_daily_tx_df = ak.stock_zh_index_daily_tx(symbol="sz159915")
print(stock_zh_index_daily_tx_df)
### 创业板ETF的日线数据备用接口
stock_zh_index_daily_em_df = ak.stock_zh_index_daily_em(symbol="sz159915")
print(stock_zh_index_daily_em_df)
### 注意：stock_zh_index_daily_tx_df 获得的数据格式为
”        date      open  close   high    low       amount“
3297  2025-07-11  2.169  2.185  2.202  2.159  11419299.00 
stock_zh_index_daily_em_df 获得的数据格式为
”            date   open  close   high    low    volume        amount“
3297   2025-07-11  2.169  2.185  2.202  2.159  11419299  2.493790e+09

stock_zh_index_daily_tx_df获得的amount = stock_zh_index_daily_em_df 获得的volume，且格式上多了”.00",如果使用备用接口，需要做数据格式统一。amount & volume 都是vol


### 1.3.2 创业板ETF 5分钟和60分钟的数据接口
#### 5min接口
index_zh_a_hist_min_em_df = ak.index_zh_a_hist_min_em(symbol="159915", period="5", start_date="2025-06-26 09:30:00", end_date="2025-06-26 19:00:00")
print(index_zh_a_hist_min_em_df)

#### 60min接口
index_zh_a_hist_min_em_df = ak.index_zh_a_hist_min_em(symbol="159915", period="60", start_date="2025-06-26 09:30:00", end_date="2025-06-26 19:00:00")
print(index_zh_a_hist_min_em_df)

### 1.3.3 创业板历史波动率
index_option_cyb_qvix_df = ak.index_option_cyb_qvix()
print(index_option_cyb_qvix_df)


### 1.3.4 创业板分时波动率
index_option_cyb_min_qvix_df = ak.index_option_cyb_min_qvix()
print(index_option_cyb_min_qvix_df)
 


## 5. 基于波动率（IV）的策略

### 5.1 高波动率策略（IV > 平均值 + 1.5倍标准差）
- **买入看涨期权（Call）**: EMA8上穿EMA21，且价格突破布林带上轨
- **买入看跌期权（Put）**: EMA8下穿EMA21，且价格跌破布林带下轨

### 5.2 低波动率策略（IV < 平均值 - 1倍标准差）
- **买入看涨期权（Call）**: 突破EMA21，成交量增加，MACD柱体由绿转红
- **买入看跌期权（Put）**: 跌破EMA21，成交量放大，MACD柱体翻绿

## 6. 基于沽购比（Put/Call Ratio）的策略

### 6.1 高沽购比策略（Put/Call Ratio > 1.2）
- **买入看涨期权（Call）**: EMA8上穿EMA21，KDJ的J值从超卖区域（<20）快速反弹

### 6.2 低沽购比策略（Put/Call Ratio < 0.8）
- **买入看跌期权（Put）**: EMA8下穿EMA21，KDJ的J值从超买区域（>80）快速回落

## 数据接口 for "认沽/认购持仓比" PCR计算

#### 每日统计-上海证券交易所
option_daily_stats_sse_df = ak.option_daily_stats_sse(date="20250711")
print(option_daily_stats_sse_df)
option_daily_stats_sse_df.to_csv("option_daily_stats_sse_df.csv", index=False)
可以获取“未平仓认购合约数,未平仓认沽合约数”，计算得到“认沽/认购持仓比”

#### 每日统计-深圳证券交易所
option_daily_stats_szse_df = ak.option_daily_stats_szse(date="20250711")
print(option_daily_stats_szse_df)
option_daily_stats_szse_df .to_csv("option_daily_stats_szse_df.csv", index=False)
可以获取“认沽/认购持仓比”


#### csi1000
option_finance_board_df = ak.option_finance_board(symbol="中证1000股指期权", end_month="2507")
print(option_finance_board_df)
option_finance_board_df.to_csv("option_finance_board_df.csv", index=False)
可以获得”MOXXXX-csi1000-XXXX“所有合约的”position“之和，除以”MOXXXX-C-XXXX“所有合约的”position“之和，可以得到“认沽/认购持仓比”
# 🔍 技术指标体系

## 2. 策略技术指标
日线，5minK线，60minK线，都需要计算以下技术指标

### 2.1 均线系统
```python
# 多条EMA均线系统
stock_data['EMA8'] = stock_data['close'].ewm(span=8, adjust=False).mean()
stock_data['EMA21'] = stock_data['close'].ewm(span=21, adjust=False).mean()
stock_data['EMA55'] = stock_data['close'].ewm(span=55, adjust=False).mean()
stock_data['EMA125'] = stock_data['close'].ewm(span=125, adjust=False).mean()
```
- **作用**: 多时间框架趋势分析
- **特点**: 四条均线构成完整的趋势判断体系
- **应用**: 短期(EMA8)、中短期(EMA21)、中期(EMA55)、长期(EMA125)趋势确认

### 2.2 KDJ指标系统
```python
# 改进的KDJ指标（14周期）
low_list = stock_data['low'].rolling(window=14, min_periods=1).min()
high_list = stock_data['high'].rolling(window=14, min_periods=1).max()
rsv = (stock_data['close'] - low_list) / (high_list - low_list) * 100
stock_data['K'] = rsv.ewm(com=2, adjust=False).mean()
stock_data['D'] = stock_data['K'].ewm(com=2, adjust=False).mean()
stock_data['J'] = 3 * stock_data['K'] - 2 * stock_data['D']
```
- **周期**: 14天
- **平滑**: 使用EWM指数加权移动平均
- **应用**: 超买超卖判断和交叉信号

### 2.3 威廉指标变种
```python
# 威廉指标变种（核心创新）
N = 19
stock_data['VAR1'] = stock_data['high'].rolling(window=N, min_periods=1).max()
stock_data['VAR2'] = stock_data['low'].rolling(window=N, min_periods=1).min()
williams_base = (stock_data['close'] - stock_data['VAR2']) / (stock_data['VAR1'] - stock_data['VAR2'])

# ZLS: 21日EMA平滑后减0.5 (长期线)
stock_data['ZLS'] = williams_base.ewm(span=21, adjust=False).mean() - 0.5

# CZX: 5日EMA平滑后减0.5 (短期线)  
stock_data['CZX'] = williams_base.ewm(span=5, adjust=False).mean() - 0.5

# HLB: 两线差值
stock_data['HLB'] = stock_data['CZX'] - stock_data['ZLS']
```
- **创新点**: 自定义威廉指标变种，提供更敏感的超买超卖信号
- **C信号**: `CZX上穿ZLS且ZLS < 0.1` - 超卖反弹信号
- **P信号**: `ZLS上穿CZX且ZLS > 0.25` - 超买回调信号

### 2.4 MACD指标
```python
# 标准MACD计算
stock_data['EMA12'] = stock_data['close'].ewm(span=12, adjust=False).mean()
stock_data['EMA26'] = stock_data['close'].ewm(span=26, adjust=False).mean()
stock_data['DIF'] = stock_data['EMA12'] - stock_data['EMA26']
stock_data['DEA'] = stock_data['DIF'].ewm(span=9, adjust=False).mean()
stock_data['MACD'] = (stock_data['DIF'] - stock_data['DEA']) * 2
```

### 2.5 个股波动率
### 通过以下接口可以获取过往20天的波动率和运行当日的分时波动率
## 2.5.1 中证1000历史波动率
index_option_1000index_qvix_df = ak.index_option_1000index_qvix()
print(index_option_1000index_qvix_df)

## 2.5.2 中证1000分时波动率
index_option_1000index_min_qvix_df = ak.index_option_1000index_min_qvix()
print(index_option_1000index_min_qvix_df)

## 2.5.3 如果接口无法获取，可以考虑用K线数据来计算
```python
def calculate_stock_volatility(stock_data: pd.DataFrame, period: int = 20) -> float:
    returns = stock_data['close'].pct_change().dropna()
    recent_returns = returns.tail(period)
    volatility = recent_returns.std() * (252 ** 0.5)  # 年化
    return volatility
```
- **特点**: 替代VIX，使用个股历史波动率
- **计算**: 20日历史波动率年化

### 3. 技术指标模块化系统 

#### 3.1 模块化指标系统
```python
# 调用模块化指标计算函数
indicators = calculate_technical_indicators(df)
```
- **优势**: 统一的指标计算接口，便于维护和扩展
- **包含**: EMA、KDJ、MACD、布林带等标准指标

#### 3.2 VIX波动率体系
```python
# VIX百分位计算
```
- **数据源**: 在1、数据来源接口
- **百分位**: 当前VIX在历史数据中的相对位置
- **制度判断**: 低波动率/高波动率制度识别

#### 3.3 期权情绪分析
```python
# 期权情绪分析器
option_analyzer = OptionSentimentAnalyzer()
sentiment_summary = option_analyzer.scenario_strategy()
```
- **功能**: 期权市场情绪量化分析
- **输出**: 市场焦点分析和综合得分

#### 3.4 期权偏度数据
```python
# 期权偏度分析
option_skew_data = get_option_skew()
```
- **作用**: 衡量市场对上涨和下跌的预期差异
- **应用**: 辅助判断市场情绪和方向偏好


## 4.交易策略框架

### 4.1. 策略核心逻辑

#### 4.1 信号分类与优先级
```python
# 信号强度分级系统
signal_strength = 0

# 1. 多头进场信号 (最高优先级)
if (current_row['EMA8'] > current_row['EMA21'] and 
    prev_row['EMA8'] < prev_row['EMA21'] and 
    prev_row['close'] > prev_row['EMA8']):
    signal_type = "多头进场信号"
    signal_strength = 3

# 2. 空头进场信号
elif (current_row['EMA21'] > current_row['EMA8'] and 
      prev_row['EMA8'] > prev_row['EMA21'] and 
      prev_row['close'] < prev_row['EMA8']):
    signal_type = "空头进场信号"
    signal_strength = 3
```

**信号优先级排序**:
1. **进场信号** (强度3): 明确的均线交叉配合价格确认
2. **预警信号** (强度2): 满足部分条件，需要额外确认
3. **威廉信号** (强度2): 纯威廉指标信号
4. **成交量异常** (强度1): 仅成交量放大

#### 4.2 多重确认机制
```python
# 成交量确认
if current_row['volume'] > current_row['VOL_MA10'] * 1.3:
    volume_confirmation = True
    signal_strength += 1

# 威廉指标确认
if williams_c_signal:  # 超卖反弹确认
    signal_strength += 1
    williams_confirmation = "威廉C信号确认"
```

#### 4.3 信号升级逻辑
```python
# 预警信号可升级为进场信号
if williams_c_signal:  # 威廉指标确认可升级
    signal_type = "多头进场信号(威廉确认)"
    signal_strength += 1
    williams_confirmation = "威廉C信号升级"
```

## 5. AI导师双重确认机制

### 1. 共同特征

#### 5.1 AI决策触发条件
```python
# 基于信号强度
signal_detected = signal_strength >= 2

#### 1.2 AI分析数据结构
```python
market_data = {
    'symbol': symbol,
    'current_price': current_price,
    'signal_type': signal_type,
    'indicators': technical_indicators,
    'vix_data': volatility_data,
    'williams_data': williams_indicators,
    # 其他市场数据...
}
```

#### 1.3 AI响应解析
```python
def parse_ai_trading_recommendation(analysis_text):
    # 检查观察等待建议
    if "观察等待" in analysis_text or "观望" in analysis_text:
        return False, "观察等待", "0%"
    
    # 检查仓位为0%的情况
    if "0%" in analysis_text and "仓位" in analysis_text:
        return False, "观察等待", "0%"
    
    # 提取交易方向和仓位
    # ...
    return is_trading, direction, position_size
```

### 2. AI确认流程

```mermaid
graph TD
    A[技术信号产生] --> B{满足AI分析条件?}
    B -->|是| C[构建市场数据]
    B -->|否| D[跳过AI分析]
    C --> E[调用DeepSeek API]
    E --> F[解析AI建议]
    F --> G{AI确认交易?}
    G -->|是| H[发送交易通知]
    G -->|否| I[忽略技术信号]
    D --> J[继续监控]
    H --> J
    I --> J
```

## 📈 策略参数配置

### 2. 中国策略配置
```python
CONFIG = {
    'symbol': {
        'zz1000': "IM0",           # 中证1000期货
        'zz500_etf': "510500",     # 中证500ETF
        'cyb_etf': "159915"        # 创业板ETF
    },
    'period': "5",  # 5分钟K线
    'period': "60",  # 60分钟K线
    'data_dir': "/Users/<USER>/Documents/1.2 Quant/volatility_data"
}
```

## ⏰ 交易时间管理

### 2. 交易时间
```python
def is_trading_hours():
    # 中国交易时段: 9:30-11:30, 13:00-15:00
    morning_start = datetime.strptime("09:25", "%H:%M").time()
    morning_end = datetime.strptime("11:35", "%H:%M").time()
    afternoon_start = datetime.strptime("12:55", "%H:%M").time()
    afternoon_end = datetime.strptime("15:05", "%H:%M").time()
```

## 🔄 定时任务调度

### 1. 调度器配置
```python

# 中国策略: 5分钟执行一次
schedule.every(5).minutes.do(job)
```

### 2. 任务执行流程
```python
def job():
    # 1. 检查交易时间
    if not is_trading_hours():
        return
    
    # 2. 获取市场数据
    # 3. 计算技术指标
    # 4. 生成交易信号
    # 5. AI二次确认
    # 6. 发送交易通知
```


