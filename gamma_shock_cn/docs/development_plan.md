# A股期权策略跟踪系统开发计划

## 📋 项目概述

基于技术分析的A股期权交易策略系统，采用双重确认升级版策略，结合多种技术指标和AI增强功能，实现自动化的期权交易信号生成和推送。

## 🎯 开发目标

- 构建稳定可靠的技术指标计算系统
- 实现多重确认的信号生成机制
- 集成AI大模型进行智能分析
- 建立完整的通知推送系统
- 提供实时监控和风险管理功能

## 🏗️ 系统架构设计

```
gamma_shock_cn/
├── main.py                    # 主程序入口
├── run.py                     # 运行脚本
├── gamma_shock_us.py          # 美股版本
├── .env                       # 环境变量配置
├── requirements.txt           # 依赖包列表
├── README.md                  # 项目说明
├── config/
│   ├── __init__.py
│   ├── settings.py           # 配置文件
│   └── trading_hours.py      # 交易时间管理
├── data/
│   ├── __init__.py
│   ├── data_fetcher.py       # 数据获取模块
│   ├── data_processor.py     # 数据处理模块
│   ├── cache_manager.py      # 缓存管理
│   ├── data_cache.py         # 数据缓存实现
│   ├── data_storage.py       # 数据存储
│   └── data_validator.py     # 数据验证
├── indicators/
│   ├── __init__.py
│   ├── technical_indicators.py # 技术指标计算
│   ├── williams_variant.py   # 威廉指标变种
│   ├── volatility_analyzer.py # 波动率分析
│   └── option_sentiment.py   # 期权情绪指标
├── strategy/
│   ├── __init__.py
│   ├── signal_generator.py   # 信号生成器
│   ├── signal_classifier.py  # 信号分类器
│   └── portfolio_manager.py  # 组合管理
├── ai/
│   ├── __init__.py
│   ├── deepseek_client.py    # DeepSeek API客户端
│   ├── prompt_templates.py   # 提示词模板
│   ├── response_parser.py    # 响应解析器
│   └── options_trading_prompt.json # 期权交易提示配置
├── notification/
│   ├── __init__.py
│   └── email_sender.py       # 邮件发送
├── notifications/
│   └── notification_system.py # 通知系统
├── modules/
│   ├── email_sender.py       # 邮件发送模块
│   └── option_sentiment.py   # 期权情绪模块
├── monitor/
│   └── market_monitor.py     # 市场监控
├── utils/
│   ├── __init__.py
│   ├── logger.py            # 日志系统
│   ├── scheduler.py         # 定时任务
│   └── helpers.py           # 工具函数
├── docs/
│   ├── development_plan.md   # 开发计划
│   └── project_design.md     # 项目设计
├── tests/
│   ├── __init__.py
│   ├── conftest.py          # 测试配置
│   ├── test_data.py         # 数据测试
│   ├── test_indicators.py   # 指标测试
│   └── test_strategy.py     # 策略测试
└── .trae/
    └── rules/
        └── project_rules.md  # 项目规则
```

## 📅 开发阶段规划

### 第一阶段：基础架构搭建 (1-2周) ✅ 已完成

#### 1.1 项目初始化
- [x] 创建项目目录结构
- [x] 配置开发环境
- [x] 设置依赖管理
- [x] 建立版本控制

#### 1.2 配置系统
- [x] 实现配置文件管理 (config/settings.py)
- [x] 交易时间管理模块 (config/trading_hours.py)
- [x] 环境变量配置 (.env)
- [x] 日志系统搭建 (utils/logger.py)

#### 1.3 数据获取模块
- [x] AKShare接口封装 (data/data_fetcher.py)
- [x] 数据缓存机制 (data/cache_manager.py)
- [x] 数据格式标准化 (data/data_processor.py)
- [x] 错误处理和重试机制

### 第二阶段：技术指标系统 (2-3周) ✅ 已完成

#### 2.1 基础技术指标
- [x] EMA均线系统 (8/21/55/125) (indicators/technical_indicators.py)
- [x] KDJ随机指标 (14周期) (indicators/technical_indicators.py)
- [x] MACD指标 (12/26/9) (indicators/technical_indicators.py)
- [x] 成交量分析 (indicators/technical_indicators.py)

#### 2.2 威廉指标变种
- [x] 威廉指标基础计算 (indicators/williams_variant.py)
- [x] ZLS长期线实现 (indicators/williams_variant.py)
- [x] CZX短期线实现 (indicators/williams_variant.py)
- [x] C/P信号生成逻辑 (indicators/williams_variant.py)

#### 2.3 波动率分析
- [x] 历史波动率计算 (indicators/volatility_analyzer.py)
- [x] 分时波动率获取 (indicators/volatility_analyzer.py)
- [x] 波动率百分位排名 (indicators/volatility_analyzer.py)
- [x] VIX数据集成 (indicators/volatility_analyzer.py)
- [x] 布林带指标计算 (indicators/technical_indicators.py)
- [x] IV波动率统计分析（均值、标准差） (indicators/volatility_analyzer.py)

#### 2.4 沽购比（PCR）分析
- [x] 期权持仓数据获取 (indicators/option_sentiment.py)
- [x] Put/Call Ratio计算 (indicators/option_sentiment.py)
- [x] PCR历史数据统计 (indicators/option_sentiment.py)
- [x] PCR阈值判断逻辑 (indicators/option_sentiment.py)

### 第三阶段：策略信号系统 (2-3周) ✅ 已完成

#### 3.1 信号生成器
- [x] 一级信号：多空进场信号 (strategy/signal_generator.py)
- [x] 二级信号：预警信号 (strategy/signal_generator.py)
- [x] 三级信号：威廉信号 (strategy/signal_generator.py)
- [x] 四级信号：异常信号 (strategy/signal_generator.py)
- [x] 五级信号：基于波动率（IV）的策略信号 (strategy/signal_generator.py)
- [x] 六级信号：基于沽购比（PCR）的策略信号 (strategy/signal_generator.py)

#### 3.2 信号分类器
- [x] 信号强度计算 (strategy/signal_classifier.py)
- [x] 信号优先级排序 (strategy/signal_classifier.py)
- [x] 信号过滤机制 (strategy/signal_classifier.py)
- [x] 历史信号记录 (strategy/signal_classifier.py)

#### 3.3 多重确认系统
- [x] 成交量确认机制 (strategy/signal_generator.py)
- [x] 威廉指标确认 (strategy/signal_generator.py)
- [x] 信号升级逻辑 (strategy/signal_generator.py)
- [x] 最低触发阈值 (strategy/signal_generator.py)
- [x] 波动率制度确认（高/低波动率环境） (strategy/signal_generator.py)
- [x] 市场情绪确认（PCR指标） (strategy/signal_generator.py)
- [x] 布林带突破确认 (strategy/signal_generator.py)

### 第四阶段：AI增强功能 (2-3周) ✅ 已完成

#### 4.1 DeepSeek集成
- [x] API客户端封装 (ai/deepseek_client.py)
- [x] 提示词模板设计 (ai/deepseek_client.py)
- [x] 响应解析器 (ai/deepseek_client.py)
- [x] 错误处理机制 (ai/deepseek_client.py)

#### 4.2 AI分析模块
- [x] 市场数据结构化 (ai/deepseek_client.py)
- [x] 技术指标解读 (ai/deepseek_client.py)
- [x] 期权策略建议 (ai/deepseek_client.py)
- [x] 风险管理建议 (ai/deepseek_client.py)

#### 4.3 双重确认机制
- [x] AI决策触发条件 (ai/deepseek_client.py)
- [x] 技术信号与AI确认结合 (ai/deepseek_client.py)
- [x] 确认结果处理 (ai/deepseek_client.py)
- [x] 决策日志记录 (ai/deepseek_client.py)

### 第五阶段：通知推送系统 (1-2周) ✅ 已完成

#### 5.1 邮件通知
- [x] SMTP配置 (notifications/notification_system.py)
- [x] 邮件模板设计 (notifications/notification_system.py)
- [x] 附件支持 (notifications/notification_system.py)
- [x] 发送状态跟踪 (notifications/notification_system.py)

#### 5.2 企业微信通知
- [x] 企业微信API集成 (notifications/notification_system.py - 占位符)
- [x] 消息格式化 (notifications/notification_system.py)
- [x] 群组推送 (notifications/notification_system.py)
- [x] 消息状态确认 (notifications/notification_system.py)

#### 5.3 通知管理
- [x] 通知频率控制 (notifications/notification_system.py)
- [x] 重要性分级 (notifications/notification_system.py)
- [x] 通知历史记录 (notifications/notification_system.py)
- [x] 失败重试机制 (notifications/notification_system.py)

### 第六阶段：监控与调度 (1-2周) ✅ 已完成

#### 6.1 定时任务系统
- [x] 5分钟K线监控 (monitor/market_monitor.py)
- [x] 交易时间检查 (monitor/market_monitor.py)
- [x] 任务调度器 (monitor/market_monitor.py)
- [x] 任务状态监控 (monitor/market_monitor.py)

#### 6.2 实时监控
- [x] 系统健康检查 (monitor/market_monitor.py)
- [x] 数据更新监控 (monitor/market_monitor.py)
- [x] 错误告警机制 (monitor/market_monitor.py)
- [x] 性能监控 (monitor/market_monitor.py)

#### 6.3 风险管理
- [x] 止损止盈逻辑 (monitor/market_monitor.py)
- [x] 仓位管理 (monitor/market_monitor.py)
- [x] 滚动策略 (monitor/market_monitor.py)
- [x] 风险预警 (monitor/market_monitor.py)

### 第七阶段：测试与优化 (2-3周) 🔄 进行中

#### 7.1 单元测试
- [x] 测试配置 (tests/conftest.py)
- [x] 数据处理测试 (tests/test_data.py)
- [x] 技术指标测试 (tests/test_indicators.py)
- [x] 策略测试 (tests/test_strategy.py)
- [x] AI模块测试 (tests/test_ai.py)
- [x] 通知系统测试 (tests/test_notifications.py)

#### 7.2 集成测试
- [x] 端到端流程测试 (tests/test_integration.py)
- [ ] 异常情况测试 (tests/test_exceptions.py)
- [x] 性能压力测试 (tests/test_performance.py)
- [ ] 数据一致性测试 (tests/test_data_consistency.py)

#### 7.3 回测验证
- [ ] 历史数据回测 (tests/test_backtest.py)
- [ ] 策略效果评估 (tests/test_strategy_performance.py)
- [ ] 参数优化 (tests/test_parameter_optimization.py)
- [ ] 风险评估 (tests/test_risk_management.py)

### 第八阶段：部署与运维 (1周) ✅ 已完成

#### 8.1 生产环境部署
- [ ] 服务器配置 (deploy/server_setup.sh)
- [ ] 环境变量设置 (deploy/.env.production)
- [ ] 依赖包安装 (deploy/requirements.txt)
- [ ] 服务启动脚本 (deploy/start_service.sh)
- [x] Docker容器化 (Dockerfile, docker-compose.yml)
- [ ] 系统服务配置 (deploy/gamma_shock.service)

#### 8.2 监控运维
- [ ] 日志监控 (deploy/log_monitor.py)
- [ ] 性能监控 (deploy/performance_monitor.py)
- [ ] 告警机制 (deploy/alert_system.py)
- [ ] 备份策略 (deploy/backup_strategy.py)
- [ ] 健康检查 (deploy/health_check.py)
- [ ] 自动重启机制 (deploy/auto_restart.py)

## 🔄 当前开发重点

### 第九阶段：测试系统完善 (1-2周) ✅ 已完成

#### 9.1 配置文件完善
- [x] config/settings.py - 主配置文件（已存在）
- [x] config/trading_hours.py - 交易时间管理（已存在）
- [x] .env.example - 环境变量模板（已创建）
- [x] 实现配置验证和加载机制优化

#### 9.2 数据模块完善
- [x] data/data_fetcher.py - 数据获取接口（已存在）
- [x] data/data_processor.py - 数据处理逻辑（已存在）
- [x] data/cache_manager.py - 缓存管理系统（已存在）
- [x] data/data_cache.py - 数据缓存实现（已存在）
- [x] data/data_storage.py - 数据存储（已存在）
- [x] data/data_validator.py - 数据验证（已存在）
- [ ] 实现数据源切换和容错机制优化

#### 9.3 测试框架搭建
- [x] tests/conftest.py - pytest配置（已存在）
- [x] tests/test_indicators.py - 技术指标测试（已存在）
- [x] tests/test_strategy.py - 策略测试（已存在）
- [x] tests/test_data.py - 数据处理测试（已存在）
- [x] tests/test_ai.py - AI模块测试（已创建）
- [x] tests/test_notifications.py - 通知系统测试（已创建）
- [x] tests/test_integration.py - 集成测试（已创建）
- [x] tests/test_performance.py - 性能测试（已创建）

#### 9.4 部署准备
- [x] requirements.txt - 依赖管理（已存在）
- [x] Dockerfile - 容器化配置（已创建）
- [x] docker-compose.yml - 服务编排（已创建）
- [x] deploy/ 目录和部署脚本（已创建）
- [x] start.sh - 启动脚本（已创建）
- [x] backup.sh - 备份脚本（已创建）

### 第十阶段：生产环境部署 (1周) ⏳ 即将开始

#### 10.1 容器化部署
- [ ] Docker镜像构建和优化
- [ ] 多阶段构建减少镜像大小
- [ ] 健康检查和重启策略
- [ ] 环境变量和配置管理

#### 10.2 服务编排
- [ ] docker-compose生产配置
- [ ] 数据持久化配置
- [ ] 网络和安全配置
- [ ] 日志收集和轮转

#### 10.3 监控和运维
- [ ] 系统监控指标收集
- [ ] 应用性能监控
- [ ] 错误告警和通知
- [ ] 自动备份和恢复

## 🛠️ 技术栈选择

### 核心技术
- **Python 3.8+**: 主要开发语言
- **AKShare**: 金融数据获取
- **Pandas**: 数据处理和分析
- **NumPy**: 数值计算
- **TA-Lib**: 技术指标计算

### AI集成
- **DeepSeek API**: 大模型分析
- **OpenAI**: 备用AI服务
- **Requests**: HTTP客户端

### 数据存储
- **SQLite**: 轻量级数据库
- **Redis**: 缓存系统
- **CSV**: 数据导出格式

### 通知服务
- **SMTP**: 邮件发送
- **企业微信API**: 即时通知
- **Jinja2**: 模板引擎

### 任务调度
- **Schedule**: 定时任务
- **APScheduler**: 高级调度器
- **Threading**: 多线程处理

### 开发工具
- **pytest**: 单元测试
- **black**: 代码格式化
- **flake8**: 代码检查
- **pre-commit**: Git钩子

## 📊 数据流设计

```mermaid
graph TD
    A[AKShare数据源] --> B[数据获取模块]
    B --> C[数据缓存]
    C --> D[技术指标计算]
    D --> E[信号生成器]
    E --> F[信号分类器]
    F --> G{信号强度>=2?}
    G -->|是| H[AI分析模块]
    G -->|否| I[继续监控]
    H --> J{AI确认交易?}
    J -->|是| K[通知推送]
    J -->|否| I
    K --> L[交易记录]
    L --> I
```

## 🔧 待实现模块详细设计

### 配置管理模块 (config/settings.py)
```python
from dataclasses import dataclass
from typing import Dict, List, Tuple
import os
from pathlib import Path

@dataclass
class TradingConfig:
    """交易配置"""
    symbols: Dict[str, Dict[str, str]]
    trading_hours: Dict[str, Tuple[str, str]]
    monitor_interval: int = 300  # 5分钟
    
@dataclass
class IndicatorConfig:
    """技术指标配置"""
    ema_periods: List[int] = None
    kdj_period: int = 14
    williams_periods: Dict[str, int] = None
    bollinger_period: int = 20
    bollinger_std: float = 2.0
    
@dataclass
class AIConfig:
    """AI配置"""
    enabled: bool = True
    api_key: str = ""
    model: str = "deepseek-chat"
    max_retries: int = 3
    timeout: int = 30
    
@dataclass
class NotificationConfig:
    """通知配置"""
    email_enabled: bool = True
    wechat_enabled: bool = False
    smtp_server: str = ""
    smtp_port: int = 587
    email_user: str = ""
    email_password: str = ""
    
class Settings:
    """主配置类"""
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        # 从环境变量和配置文件加载
        pass
    
    def validate_config(self):
        """验证配置"""
        pass
```

### 数据获取模块 (data/data_fetcher.py)
```python
import akshare as ak
import pandas as pd
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import time
import logging

class DataFetcher:
    """数据获取器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.symbols = {
            'zz1000': {
                'name': '中证1000',
                'code': '399852',
                'futures_code': 'IM0'
            },
            'zz500_etf': {
                'name': '中证500ETF', 
                'code': '510500'
            },
            'cyb_etf': {
                'name': '创业板ETF',
                'code': '159915'
            }
        }
    
    def get_daily_data(self, symbol: str, period: int = 250) -> Optional[pd.DataFrame]:
        """获取日线数据"""
        try:
            # 实现日线数据获取逻辑
            pass
        except Exception as e:
            self.logger.error(f"获取日线数据失败: {e}")
            return None
    
    def get_minute_data(self, symbol: str, period: str = "5") -> Optional[pd.DataFrame]:
        """获取分钟级数据"""
        try:
            # 实现分钟数据获取逻辑
            pass
        except Exception as e:
            self.logger.error(f"获取分钟数据失败: {e}")
            return None
    
    def get_volatility_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取波动率数据"""
        try:
            # 实现波动率数据获取逻辑
            pass
        except Exception as e:
            self.logger.error(f"获取波动率数据失败: {e}")
            return None
    
    def get_option_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取期权数据"""
        try:
            # 实现期权数据获取逻辑
            pass
        except Exception as e:
            self.logger.error(f"获取期权数据失败: {e}")
            return None
```

### 数据处理模块 (data/data_processor.py)
```python
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import logging

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化数据格式"""
        try:
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"缺少必要列: {col}")
            
            # 数据类型转换
            df = df.copy()
            for col in required_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 处理缺失值
            df = df.dropna()
            
            # 确保时间索引
            if 'datetime' in df.columns:
                df['datetime'] = pd.to_datetime(df['datetime'])
                df.set_index('datetime', inplace=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"数据标准化失败: {e}")
            raise
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """验证数据质量"""
        try:
            # 检查数据完整性
            if df.empty:
                return False
            
            # 检查价格合理性
            if (df['high'] < df['low']).any():
                return False
            
            if (df['close'] > df['high']).any() or (df['close'] < df['low']).any():
                return False
            
            # 检查成交量
            if (df['volume'] < 0).any():
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return False
    
    def resample_data(self, df: pd.DataFrame, freq: str) -> pd.DataFrame:
        """重采样数据"""
        try:
            resampled = df.resample(freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            return resampled
            
        except Exception as e:
            self.logger.error(f"数据重采样失败: {e}")
            raise
```

### 缓存管理模块 (data/cache_manager.py)
```python
import pickle
import os
import time
from typing import Any, Optional
from pathlib import Path
import hashlib
import logging

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache", default_ttl: int = 300):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.default_ttl = default_ttl
        self.logger = logging.getLogger(__name__)
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名过长
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{hash_key}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        try:
            cache_path = self._get_cache_path(key)
            
            if not cache_path.exists():
                return None
            
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
            
            # 检查是否过期
            if time.time() > cache_data['expires_at']:
                cache_path.unlink()  # 删除过期缓存
                return None
            
            return cache_data['data']
            
        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            return None
    
    def set(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存数据"""
        try:
            cache_path = self._get_cache_path(key)
            ttl = ttl or self.default_ttl
            
            cache_data = {
                'data': data,
                'created_at': time.time(),
                'expires_at': time.time() + ttl
            }
            
            with open(cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置缓存失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            cache_path = self._get_cache_path(key)
            if cache_path.exists():
                cache_path.unlink()
            return True
        except Exception as e:
            self.logger.error(f"删除缓存失败: {e}")
            return False
    
    def clear_expired(self) -> int:
        """清理过期缓存"""
        cleared_count = 0
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    with open(cache_file, 'rb') as f:
                        cache_data = pickle.load(f)
                    
                    if time.time() > cache_data['expires_at']:
                        cache_file.unlink()
                        cleared_count += 1
                        
                except Exception:
                    # 如果文件损坏，也删除
                    cache_file.unlink()
                    cleared_count += 1
            
            return cleared_count
            
        except Exception as e:
            self.logger.error(f"清理过期缓存失败: {e}")
            return cleared_count
```

### 测试框架设计 (tests/conftest.py)
```python
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

@pytest.fixture
def sample_ohlcv_data():
    """生成样本OHLCV数据"""
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    np.random.seed(42)
    
    # 生成模拟价格数据
    base_price = 100
    returns = np.random.normal(0, 0.02, len(dates))
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    df = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, len(dates))
    }, index=dates)
    
    # 确保high >= low
    df['high'] = np.maximum(df['high'], df['low'])
    
    return df

@pytest.fixture
def sample_signal_data():
    """生成样本信号数据"""
    return {
        'signal_type': 'multi_entry',
        'signal_strength': 3,
        'confidence': 0.85,
        'timestamp': datetime.now(),
        'indicators': {
            'ema8': 100.5,
            'ema21': 99.8,
            'kdj_k': 65.2,
            'kdj_d': 62.1,
            'macd': 0.5
        }
    }

@pytest.fixture
def mock_config():
    """模拟配置数据"""
    return {
        'symbols': {
            'test_symbol': {
                'name': '测试标的',
                'code': 'TEST001'
            }
        },
        'indicators': {
            'ema_periods': [8, 21, 55, 125],
            'kdj_period': 14
        },
        'ai': {
            'enabled': False,  # 测试时禁用AI
            'api_key': 'test_key'
        }
    }
```

### 信号生成器测试 (tests/test_signals.py)
```python
import pytest
import pandas as pd
from datetime import datetime
from signals.signal_generator import SignalGenerator
from signals.signal_classifier import SignalClassifier

class TestSignalGenerator:
    """信号生成器测试"""
    
    def test_multi_entry_signal_generation(self, sample_ohlcv_data, mock_config):
        """测试多重进场信号生成"""
        generator = SignalGenerator(mock_config)
        signals = generator.generate_signals(sample_ohlcv_data)
        
        assert isinstance(signals, list)
        for signal in signals:
            assert 'signal_type' in signal
            assert 'signal_strength' in signal
            assert 'confidence' in signal
            assert 'timestamp' in signal
    
    def test_signal_classification(self, sample_signal_data):
        """测试信号分类"""
        classifier = SignalClassifier()
        classified = classifier.classify_signal(sample_signal_data)
        
        assert 'priority' in classified
        assert 'category' in classified
        assert classified['priority'] in ['high', 'medium', 'low']
    
    def test_signal_validation(self, sample_signal_data):
        """测试信号验证"""
        generator = SignalGenerator({})
        is_valid = generator.validate_signal(sample_signal_data)
        
        assert isinstance(is_valid, bool)
```

### 技术指标测试 (tests/test_indicators.py)
```python
import pytest
import pandas as pd
import numpy as np
from indicators.technical_indicators import TechnicalIndicators
from indicators.williams_indicators import WilliamsIndicators

class TestTechnicalIndicators:
    """技术指标测试"""
    
    def test_ema_calculation(self, sample_ohlcv_data):
        """测试EMA计算"""
        indicators = TechnicalIndicators()
        result = indicators.calculate_ema_system(sample_ohlcv_data)
        
        assert 'ema8' in result.columns
        assert 'ema21' in result.columns
        assert 'ema55' in result.columns
        assert not result['ema8'].isna().all()
    
    def test_kdj_calculation(self, sample_ohlcv_data):
        """测试KDJ计算"""
        indicators = TechnicalIndicators()
        result = indicators.calculate_kdj(sample_ohlcv_data)
        
        assert 'kdj_k' in result.columns
        assert 'kdj_d' in result.columns
        assert 'kdj_j' in result.columns
        assert (result['kdj_k'] >= 0).all()
        assert (result['kdj_k'] <= 100).all()
    
    def test_macd_calculation(self, sample_ohlcv_data):
        """测试MACD计算"""
        indicators = TechnicalIndicators()
        result = indicators.calculate_macd(sample_ohlcv_data)
        
        assert 'macd' in result.columns
        assert 'macd_signal' in result.columns
        assert 'macd_histogram' in result.columns
    
    def test_williams_indicators(self, sample_ohlcv_data):
        """测试威廉指标变种"""
        williams = WilliamsIndicators()
        result = williams.calculate_all_variants(sample_ohlcv_data)
        
        assert 'zls' in result.columns
        assert 'czx' in result.columns
        assert 'hlb' in result.columns
        assert 'zls_c' in result.columns
        assert 'zls_p' in result.columns
```

### 数据处理测试 (tests/test_data.py)
```python
import pytest
import pandas as pd
import numpy as np
from data.data_fetcher import DataFetcher
from data.data_processor import DataProcessor
from data.cache_manager import CacheManager

class TestDataProcessor:
    """数据处理测试"""
    
    def test_data_standardization(self):
        """测试数据标准化"""
        processor = DataProcessor()
        
        # 创建测试数据
        raw_data = pd.DataFrame({
            'open': ['100.5', '101.0', '99.8'],
            'high': ['102.0', '103.5', '101.2'],
            'low': ['99.0', '100.2', '98.5'],
            'close': ['101.0', '99.8', '100.5'],
            'volume': ['1000000', '1200000', '800000']
        })
        
        result = processor.standardize_data(raw_data)
        
        assert result['open'].dtype in [np.float64, np.float32]
        assert not result.isna().any().any()
    
    def test_data_validation(self, sample_ohlcv_data):
        """测试数据验证"""
        processor = DataProcessor()
        
        # 测试有效数据
        assert processor.validate_data(sample_ohlcv_data) == True
        
        # 测试无效数据
        invalid_data = sample_ohlcv_data.copy()
        invalid_data.loc[invalid_data.index[0], 'high'] = invalid_data.loc[invalid_data.index[0], 'low'] - 1
        
        assert processor.validate_data(invalid_data) == False
    
    def test_data_resampling(self, sample_ohlcv_data):
        """测试数据重采样"""
        processor = DataProcessor()
        
        # 重采样为周线
        weekly_data = processor.resample_data(sample_ohlcv_data, 'W')
        
        assert len(weekly_data) < len(sample_ohlcv_data)
        assert 'open' in weekly_data.columns
        assert 'high' in weekly_data.columns
        assert 'low' in weekly_data.columns
        assert 'close' in weekly_data.columns
        assert 'volume' in weekly_data.columns

class TestCacheManager:
    """缓存管理测试"""
    
    def test_cache_operations(self, tmp_path):
        """测试缓存操作"""
        cache = CacheManager(cache_dir=str(tmp_path), default_ttl=60)
        
        # 测试设置和获取
        test_data = {'test': 'data'}
        assert cache.set('test_key', test_data) == True
        assert cache.get('test_key') == test_data
        
        # 测试删除
        assert cache.delete('test_key') == True
        assert cache.get('test_key') is None
    
    def test_cache_expiration(self, tmp_path):
        """测试缓存过期"""
        cache = CacheManager(cache_dir=str(tmp_path), default_ttl=1)
        
        cache.set('expire_test', 'data')
        assert cache.get('expire_test') == 'data'
        
        # 等待过期
        import time
        time.sleep(2)
        
        assert cache.get('expire_test') is None
```

### AI模块测试 (tests/test_ai.py)
```python
import pytest
from unittest.mock import Mock, patch
from ai.ai_advisor import AIAdvisor
from ai.deepseek_client import DeepSeekClient

class TestAIAdvisor:
    """AI顾问测试"""
    
    def test_ai_analysis_disabled(self, mock_config):
        """测试AI分析禁用状态"""
        config = mock_config.copy()
        config['ai']['enabled'] = False
        
        advisor = AIAdvisor(config)
        result = advisor.analyze_signal({'test': 'signal'})
        
        assert result['ai_enabled'] == False
        assert 'ai_analysis' not in result
    
    @patch('ai.deepseek_client.DeepSeekClient.chat')
    def test_ai_analysis_enabled(self, mock_chat, mock_config):
        """测试AI分析启用状态"""
        config = mock_config.copy()
        config['ai']['enabled'] = True
        config['ai']['api_key'] = 'test_key'
        
        # 模拟AI响应
        mock_chat.return_value = {
            'choices': [{
                'message': {
                    'content': '{"confidence": 0.8, "recommendation": "buy"}'
                }
            }]
        }
        
        advisor = AIAdvisor(config)
        result = advisor.analyze_signal({'test': 'signal'})
        
        assert result['ai_enabled'] == True
        assert 'ai_analysis' in result
        assert result['ai_analysis']['confidence'] == 0.8
    
    def test_ai_error_handling(self, mock_config):
        """测试AI错误处理"""
        config = mock_config.copy()
        config['ai']['enabled'] = True
        config['ai']['api_key'] = 'invalid_key'
        
        advisor = AIAdvisor(config)
        
        with patch('ai.deepseek_client.DeepSeekClient.chat', side_effect=Exception("API Error")):
            result = advisor.analyze_signal({'test': 'signal'})
            
            assert 'error' in result
            assert result['ai_enabled'] == True
```

### 通知系统测试 (tests/test_notifications.py)
```python
import pytest
from unittest.mock import Mock, patch
from notifications.email_notifier import EmailNotifier
from notifications.notification_manager import NotificationManager

class TestEmailNotifier:
    """邮件通知测试"""
    
    @patch('smtplib.SMTP')
    def test_email_sending(self, mock_smtp, mock_config):
        """测试邮件发送"""
        config = {
            'email_enabled': True,
            'smtp_server': 'smtp.test.com',
            'smtp_port': 587,
            'email_user': '<EMAIL>',
            'email_password': 'password'
        }
        
        notifier = EmailNotifier(config)
        
        # 模拟SMTP连接
        mock_server = Mock()
        mock_smtp.return_value = mock_server
        
        result = notifier.send_signal_notification({
            'signal_type': 'multi_entry',
            'signal_strength': 3,
            'symbol': 'test_symbol'
        })
        
        assert result == True
        mock_server.send_message.assert_called_once()
    
    def test_email_disabled(self, mock_config):
        """测试邮件禁用状态"""
        config = {'email_enabled': False}
        notifier = EmailNotifier(config)
        
        result = notifier.send_signal_notification({'test': 'signal'})
        assert result == False

class TestNotificationManager:
    """通知管理器测试"""
    
    def test_notification_routing(self, mock_config):
        """测试通知路由"""
        manager = NotificationManager(mock_config)
        
        with patch.object(manager.email_notifier, 'send_signal_notification', return_value=True):
            result = manager.send_notification({
                'signal_type': 'multi_entry',
                'priority': 'high'
            })
            
            assert result == True
    
    def test_notification_filtering(self, mock_config):
        """测试通知过滤"""
        manager = NotificationManager(mock_config)
        
        # 测试低优先级信号过滤
        result = manager.should_send_notification({
            'priority': 'low',
            'signal_strength': 1
        })
        
        assert isinstance(result, bool)
```

### 集成测试 (tests/test_integration.py)
```python
import pytest
import pandas as pd
from datetime import datetime
from unittest.mock import patch, Mock

class TestSystemIntegration:
    """系统集成测试"""
    
    @patch('data.data_fetcher.DataFetcher.get_daily_data')
    @patch('ai.ai_advisor.AIAdvisor.analyze_signal')
    @patch('notifications.notification_manager.NotificationManager.send_notification')
    def test_full_signal_pipeline(self, mock_notification, mock_ai, mock_data, sample_ohlcv_data, mock_config):
        """测试完整信号流水线"""
        # 模拟数据获取
        mock_data.return_value = sample_ohlcv_data
        
        # 模拟AI分析
        mock_ai.return_value = {
            'ai_enabled': True,
            'ai_analysis': {
                'confidence': 0.8,
                'recommendation': 'buy'
            }
        }
        
        # 模拟通知发送
        mock_notification.return_value = True
        
        # 运行完整流程
        from main import GammaShockSystem
        system = GammaShockSystem(mock_config)
        
        result = system.run_analysis_cycle()
        
        assert result is not None
        mock_data.assert_called()
        mock_ai.assert_called()
        mock_notification.assert_called()
    
    def test_error_recovery(self, mock_config):
        """测试错误恢复"""
        from main import GammaShockSystem
        system = GammaShockSystem(mock_config)
        
        # 模拟数据获取失败
        with patch('data.data_fetcher.DataFetcher.get_daily_data', side_effect=Exception("Data Error")):
            result = system.run_analysis_cycle()
            
            # 系统应该能够处理错误并继续运行
            assert result is not None
    
    def test_performance_monitoring(self, mock_config):
        """测试性能监控"""
        from main import GammaShockSystem
        system = GammaShockSystem(mock_config)
        
        start_time = datetime.now()
        system.run_analysis_cycle()
        end_time = datetime.now()
        
        # 检查执行时间是否合理（应该在几秒内完成）
        execution_time = (end_time - start_time).total_seconds()
        assert execution_time < 30  # 30秒内完成
```

## 📦 部署配置设计

### 依赖管理 (requirements.txt)
```txt
# 数据处理
pandas>=2.0.0
numpy>=1.24.0
akshare>=1.12.0

# 技术分析
TA-Lib>=0.4.25
scipy>=1.10.0

# AI集成
requests>=2.31.0
openai>=1.0.0

# 通知系统
smtplib-ssl>=1.0.0
email-validator>=2.0.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# 日志和监控
loguru>=0.7.0
psutil>=5.9.0

# 测试框架
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# 开发工具
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# 部署相关
gunicorn>=21.0.0
supervisor>=4.2.0
```

### Docker配置 (Dockerfile)
```dockerfile
# 多阶段构建
FROM python:3.11-slim as builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 安装TA-Lib
RUN wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz && \
    tar -xzf ta-lib-0.4.0-src.tar.gz && \
    cd ta-lib/ && \
    ./configure --prefix=/usr && \
    make && \
    make install && \
    cd .. && \
    rm -rf ta-lib*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 生产阶段
FROM python:3.11-slim as production

# 创建非root用户
RUN useradd --create-home --shell /bin/bash gamma

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libta-lib0 \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制Python包
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin
COPY --from=builder /usr/lib/libta_lib* /usr/lib/
COPY --from=builder /usr/include/ta-lib /usr/include/ta-lib

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY --chown=gamma:gamma . .

# 创建必要目录
RUN mkdir -p logs cache data && \
    chown -R gamma:gamma /app

# 切换到非root用户
USER gamma

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health', timeout=5)"

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "run.py", "--mode", "production"]
```

### 服务编排 (docker-compose.yml)
```yaml
version: '3.8'

services:
  gamma-shock:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: gamma-shock-app
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - CACHE_TTL=300
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./cache:/app/cache
      - ./data:/app/data
      - ./config:/app/config
    ports:
      - "8000:8000"
    networks:
      - gamma-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: gamma-shock-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - gamma-network
    command: redis-server --appendonly yes

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: gamma-shock-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./deploy/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - gamma-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

volumes:
  redis-data:
  prometheus-data:

networks:
  gamma-network:
    driver: bridge
```

### 部署脚本 (deploy/deploy.sh)
```bash
#!/bin/bash

# Gamma Shock 部署脚本
set -e

echo "🚀 开始部署 Gamma Shock 系统..."

# 检查Docker和docker-compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装，请先安装 docker-compose"
    exit 1
fi

# 创建必要目录
mkdir -p logs cache data config

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件配置必要的环境变量"
    echo "   - DEEPSEEK_API_KEY: DeepSeek API密钥"
    echo "   - EMAIL_PASSWORD: 邮箱密码"
    echo "   - 其他配置项..."
    read -p "配置完成后按回车继续..."
fi

# 构建镜像
echo "🔨 构建Docker镜像..."
docker-compose build

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查健康状态
echo "💓 检查应用健康状态..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 应用启动成功！"
    echo "📊 访问地址: http://localhost:8000"
    echo "📈 监控地址: http://localhost:9090 (如果启用了Prometheus)"
else
    echo "❌ 应用启动失败，请检查日志:"
    echo "   docker-compose logs gamma-shock"
fi

echo "🎉 部署完成！"
```

### 环境变量模板 (.env.example)
```bash
# Gamma Shock 环境变量配置

# ===================
# 基础配置
# ===================
LOG_LEVEL=INFO
DEBUG=false
ENVIRONMENT=production

# ===================
# AI配置
# ===================
DEEPSEEK_API_KEY=your_deepseek_api_key_here
AI_MODEL=deepseek-chat
AI_TIMEOUT=30
AI_MAX_RETRIES=3

# ===================
# 邮件通知配置
# ===================
EMAIL_ENABLED=true
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
EMAIL_TO=<EMAIL>

# ===================
# 微信通知配置（可选）
# ===================
WECHAT_ENABLED=false
WECHAT_WEBHOOK_URL=

# ===================
# 数据配置
# ===================
DATA_SOURCE=akshare
CACHE_TTL=300
DATA_RETRY_TIMES=3
DATA_TIMEOUT=30

# ===================
# 监控配置
# ===================
MONITOR_INTERVAL=300
HEALTH_CHECK_PORT=8000
METRICS_ENABLED=true

# ===================
# Redis配置（可选）
# ===================
REDIS_ENABLED=false
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# ===================
# 安全配置
# ===================
SECRET_KEY=your_secret_key_here
API_TOKEN=your_api_token_here
```

## 🔧 运维脚本设计

### 系统监控脚本 (deploy/monitor.sh)
```bash
#!/bin/bash

# 系统监控脚本
echo "📊 Gamma Shock 系统监控报告"
echo "=============================="
echo "时间: $(date)"
echo ""

# 检查容器状态
echo "🐳 Docker容器状态:"
docker-compose ps
echo ""

# 检查系统资源
echo "💻 系统资源使用:"
echo "CPU使用率: $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free -h | awk '/^Mem:/ {print $3"/"$2}')"
echo "磁盘使用: $(df -h / | awk 'NR==2 {print $3"/"$2" ("$5")')"
echo ""

# 检查应用健康状态
echo "💓 应用健康检查:"
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 应用运行正常"
else
    echo "❌ 应用异常，请检查日志"
fi
echo ""

# 检查日志文件大小
echo "📝 日志文件状态:"
if [ -d "logs" ]; then
    du -sh logs/*
else
    echo "日志目录不存在"
fi
echo ""

# 检查缓存使用
echo "🗄️ 缓存状态:"
if [ -d "cache" ]; then
    echo "缓存文件数量: $(find cache -name '*.cache' | wc -l)"
    echo "缓存目录大小: $(du -sh cache)"
else
    echo "缓存目录不存在"
fi
```

### 备份脚本 (deploy/backup.sh)
```bash
#!/bin/bash

# 数据备份脚本
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 开始备份 Gamma Shock 数据..."
echo "备份目录: $BACKUP_DIR"

# 备份配置文件
echo "📋 备份配置文件..."
cp -r config "$BACKUP_DIR/" 2>/dev/null || echo "配置目录不存在"
cp .env "$BACKUP_DIR/" 2>/dev/null || echo ".env文件不存在"

# 备份日志文件
echo "📝 备份日志文件..."
cp -r logs "$BACKUP_DIR/" 2>/dev/null || echo "日志目录不存在"

# 备份数据文件
echo "💾 备份数据文件..."
cp -r data "$BACKUP_DIR/" 2>/dev/null || echo "数据目录不存在"

# 创建备份信息文件
echo "ℹ️ 创建备份信息..."
cat > "$BACKUP_DIR/backup_info.txt" << EOF
备份时间: $(date)
系统版本: $(git rev-parse HEAD 2>/dev/null || echo "未知")
容器状态: 
$(docker-compose ps)
EOF

# 压缩备份
echo "🗜️ 压缩备份文件..."
tar -czf "${BACKUP_DIR}.tar.gz" -C backups "$(basename $BACKUP_DIR)"
rm -rf "$BACKUP_DIR"

echo "✅ 备份完成: ${BACKUP_DIR}.tar.gz"

# 清理旧备份（保留最近7天）
echo "🧹 清理旧备份..."
find backups -name "*.tar.gz" -mtime +7 -delete

echo "🎉 备份任务完成！"
```

### 自动重启脚本 (deploy/auto_restart.py)
```python
#!/usr/bin/env python3
"""
Gamma Shock 自动重启脚本
监控应用健康状态，异常时自动重启
"""

import time
import requests
import subprocess
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deploy/auto_restart.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class HealthMonitor:
    def __init__(self, health_url="http://localhost:8000/health", check_interval=60, max_failures=3):
        self.health_url = health_url
        self.check_interval = check_interval
        self.max_failures = max_failures
        self.failure_count = 0
        self.last_restart = None
        
    def check_health(self):
        """检查应用健康状态"""
        try:
            response = requests.get(self.health_url, timeout=10)
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"健康检查返回状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    def restart_application(self):
        """重启应用"""
        try:
            logger.info("开始重启应用...")
            
            # 停止服务
            subprocess.run(["docker-compose", "down"], check=True)
            time.sleep(10)
            
            # 启动服务
            subprocess.run(["docker-compose", "up", "-d"], check=True)
            
            # 等待服务启动
            time.sleep(30)
            
            self.last_restart = datetime.now()
            self.failure_count = 0
            
            logger.info("应用重启完成")
            return True
            
        except Exception as e:
            logger.error(f"重启应用失败: {e}")
            return False
    
    def run(self):
        """运行监控循环"""
        logger.info("开始健康监控...")
        
        while True:
            try:
                if self.check_health():
                    if self.failure_count > 0:
                        logger.info("应用恢复正常")
                        self.failure_count = 0
                else:
                    self.failure_count += 1
                    logger.warning(f"健康检查失败 ({self.failure_count}/{self.max_failures})")
                    
                    if self.failure_count >= self.max_failures:
                        # 检查是否刚刚重启过（避免频繁重启）
                        if self.last_restart and (datetime.now() - self.last_restart).seconds < 300:
                            logger.warning("刚刚重启过，跳过本次重启")
                            self.failure_count = 0
                        else:
                            self.restart_application()
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("监控程序被用户中断")
                break
            except Exception as e:
                logger.error(f"监控程序异常: {e}")
                time.sleep(self.check_interval)

if __name__ == "__main__":
    monitor = HealthMonitor()
    monitor.run()
```

## 📈 性能优化建议

### 第十一阶段：性能优化 (1周) 🔄 规划中

#### 11.1 代码优化
- [ ] 数据处理性能优化
- [ ] 指标计算并行化
- [ ] 内存使用优化
- [ ] 异步处理改进

#### 11.2 系统优化
- [ ] 数据库查询优化
- [ ] 缓存策略优化
- [ ] 网络请求优化
- [ ] 资源使用监控

#### 11.3 扩展性设计
- [ ] 微服务架构迁移
- [ ] 负载均衡配置
- [ ] 水平扩展支持
- [ ] 分布式缓存

## 🔮 未来发展规划

### v2.0 规划 (3-6个月)
- [ ] Web界面开发
- [ ] 实时数据流处理
- [ ] 更多技术指标
- [ ] 策略回测功能
- [ ] 多市场支持

### v3.0 规划 (6-12个月)
- [ ] 机器学习模型集成
- [ ] 量化交易接口
- [ ] 移动端应用
- [ ] 社区功能
- [ ] 云服务部署

class SignalGenerator:
    def __init__(self):
        self.min_signal_strength = 2
        self.signal_levels = {
            'level_1': 'multi_short_entry',
            'level_2': 'warning',
            'level_3': 'williams',
            'level_4': 'anomaly',
            'level_5': 'iv_strategy',
            'level_6': 'pcr_strategy'
        }
    
    def generate_signals(self, df: pd.DataFrame) -> dict:
        """生成交易信号"""
        pass
    
    def calculate_signal_strength(self, signals: dict) -> int:
        """计算信号强度"""
        pass
    
    def apply_confirmations(self, signals: dict, df: pd.DataFrame) -> dict:
        """应用确认机制"""
        pass
    
    def generate_iv_signals(self, df: pd.DataFrame, iv_data: dict) -> list:
        """生成基于波动率的策略信号"""
        signals = []
        current_iv = iv_data['current']
        iv_mean = iv_data['mean']
        iv_std = iv_data['std']
        
        # 高波动率策略
        if current_iv > (iv_mean + 1.5 * iv_std):
            # 买入看涨期权条件
            if self._check_high_iv_call_conditions(df):
                signals.append({
                    'level': 5,
                    'type': 'high_iv_call',
                    'strategy': 'buy_call',
                    'reason': 'EMA8上穿EMA21且突破布林带上轨（高波动率）'
                })
            
            # 买入看跌期权条件
            if self._check_high_iv_put_conditions(df):
                signals.append({
                    'level': 5,
                    'type': 'high_iv_put',
                    'strategy': 'buy_put',
                    'reason': 'EMA8下穿EMA21且跌破布林带下轨（高波动率）'
                })
        
        # 低波动率策略
        elif current_iv < (iv_mean - 1.0 * iv_std):
            if self._check_low_iv_call_conditions(df):
                signals.append({
                    'level': 5,
                    'type': 'low_iv_call',
                    'strategy': 'buy_call',
                    'reason': '突破EMA21，成交量增加，MACD柱体转红（低波动率）'
                })
            
            if self._check_low_iv_put_conditions(df):
                signals.append({
                    'level': 5,
                    'type': 'low_iv_put',
                    'strategy': 'buy_put',
                    'reason': '跌破EMA21，成交量放大，MACD柱体翻绿（低波动率）'
                })
        
        return signals
    
    def generate_pcr_signals(self, df: pd.DataFrame, pcr_data: dict) -> list:
        """生成基于沽购比的策略信号"""
        signals = []
        current_pcr = pcr_data['current']
        
        # 高沽购比策略
        if current_pcr > 1.2:
            if self._check_high_pcr_call_conditions(df):
                signals.append({
                    'level': 6,
                    'type': 'high_pcr_call',
                    'strategy': 'buy_call',
                    'reason': 'EMA8上穿EMA21，KDJ的J值从超卖区域反弹（高沽购比）'
                })
        
        # 低沽购比策略
        elif current_pcr < 0.8:
            if self._check_low_pcr_put_conditions(df):
                signals.append({
                    'level': 6,
                    'type': 'low_pcr_put',
                    'strategy': 'buy_put',
                    'reason': 'EMA8下穿EMA21，KDJ的J值从超买区域回落（低沽购比）'
                })
        
        return signals
```

### AI分析模块 (deepseek_client.py)
```python
class DeepSeekClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com"
    
    def analyze_market_data(self, market_data: dict) -> str:
        """分析市场数据"""
        pass
    
    def parse_trading_recommendation(self, response: str) -> dict:
        """解析交易建议"""
        pass
```

## 📈 监控标的配置

### 标的映射关系
```python
SYMBOLS_CONFIG = {
    'zz1000': {
        'name': '中证1000',
        'code': '399852',
        'futures_code': 'IM0',
        'aliases': ['中证1000', 'zz1000', 'sz399852', '399852']
    },
    'zz500_etf': {
        'name': '中证500ETF',
        'code': '510500',
        'aliases': ['中证500', '中证500ETF', '500etf', 'zz500', 'sh510500', '399905']
    },
    'cyb_etf': {
        'name': '创业板ETF',
        'code': '159915',
        'aliases': ['创业板etf', 'cyb', 'sz159915', '159915']
    }
}
```

## ⚙️ 配置管理

### 主配置文件 (config/settings.py)
```python
class Config:
    # 数据源配置
    DATA_SOURCE = 'akshare'
    CACHE_ENABLED = True
    CACHE_DURATION = 300  # 5分钟
    
    # 交易时间配置
    TRADING_HOURS = {
        'morning': ('09:25', '11:35'),
        'afternoon': ('12:55', '15:05')
    }
    
    # 技术指标参数
    EMA_PERIODS = [8, 21, 55, 125]
    KDJ_PERIOD = 14
    WILLIAMS_PERIODS = {'long': 21, 'short': 5}
    
    # 信号配置
    MIN_SIGNAL_STRENGTH = 2
    VOLUME_THRESHOLD = 1.3
    
    # AI配置
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
    AI_ANALYSIS_ENABLED = True
    
    # 通知配置
    EMAIL_ENABLED = True
    WECHAT_ENABLED = False
    
    # 调度配置
    MONITOR_INTERVAL = 300  # 5分钟
```

## 🔄 定时任务设计

### 主监控循环
```python
def main_monitoring_loop():
    """主监控循环"""
    while True:
        try:
            # 检查交易时间
            if not is_trading_hours():
                time.sleep(60)
                continue
            
            # 遍历监控标的
            for symbol in SYMBOLS_CONFIG.keys():
                process_symbol(symbol)
            
            # 等待下一个周期
            time.sleep(CONFIG.MONITOR_INTERVAL)
            
        except Exception as e:
            logger.error(f"监控循环错误: {e}")
            time.sleep(60)

def process_symbol(symbol: str):
    """处理单个标的"""
    # 1. 获取数据
    data = data_fetcher.get_latest_data(symbol)
    
    # 2. 计算指标
    indicators = technical_indicators.calculate_all(data)
    
    # 3. 生成信号
    signals = signal_generator.generate_signals(indicators)
    
    # 4. AI确认
    if signals['strength'] >= 2:
        ai_result = ai_analyzer.analyze(signals, indicators)
        if ai_result['confirmed']:
            # 5. 发送通知
            notification_sender.send_signal(symbol, signals, ai_result)
```

## 📧 通知系统设计

### 邮件模板
```html
<!DOCTYPE html>
<html>
<head>
    <title>期权交易信号</title>
</head>
<body>
    <h2>🚨 {{ signal_type }} - {{ symbol_name }}</h2>
    
    <h3>📊 技术指标</h3>
    <ul>
        <li>当前价格: {{ current_price }}</li>
        <li>信号强度: {{ signal_strength }}</li>
        <li>成交量确认: {{ volume_confirmation }}</li>
        <li>威廉确认: {{ williams_confirmation }}</li>
    </ul>
    
    <h3>🤖 AI分析建议</h3>
    <p><strong>方向:</strong> {{ ai_direction }}</p>
    <p><strong>行权价:</strong> {{ strike_price }}</p>
    <p><strong>到期日:</strong> {{ expiry_date }}</p>
    <p><strong>仓位:</strong> {{ position_size }}</p>
    
    <h3>⚠️ 风险管理</h3>
    <p><strong>止损:</strong> {{ stop_loss }}</p>
    <p><strong>止盈:</strong> {{ take_profit }}</p>
    
    <p><small>发送时间: {{ timestamp }}</small></p>
</body>
</html>
```

## 🧪 测试策略

### 测试覆盖范围
1. **单元测试**: 每个模块的核心功能
2. **集成测试**: 模块间的协作
3. **回测验证**: 历史数据验证策略效果
4. **压力测试**: 高频数据处理能力
5. **异常测试**: 网络中断、数据异常等场景

### 测试数据准备
```python
# 准备测试数据集
test_data = {
    'normal_trend': 'data/test/normal_trend.csv',
    'strong_signal': 'data/test/strong_signal.csv',
    'weak_signal': 'data/test/weak_signal.csv',
    'no_signal': 'data/test/no_signal.csv',
    'extreme_volatility': 'data/test/extreme_volatility.csv'
}
```

## 📋 开发检查清单

### 代码质量
- [x] 代码符合PEP8规范
- [x] 函数和类有完整的文档字符串
- [x] 关键逻辑有详细注释
- [x] 异常处理完善
- [x] 日志记录充分

### 功能完整性
- [x] 所有技术指标计算正确
- [x] 信号生成逻辑准确
- [x] AI集成功能正常
- [x] 通知推送可靠
- [x] 定时任务稳定

### 性能优化
- [x] 数据缓存有效
- [x] 计算效率优化
- [x] 内存使用合理
- [x] 网络请求优化

### 安全性
- [x] API密钥安全存储
- [x] 输入数据验证
- [x] 错误信息不泄露敏感信息
- [x] 日志不包含敏感数据

## 🚀 部署计划

### 环境准备
1. **开发环境**: 本地开发和测试
2. **测试环境**: 模拟生产环境测试
3. **生产环境**: 正式运行环境

### 部署步骤
1. 服务器配置和环境搭建
2. 代码部署和依赖安装
3. 配置文件设置
4. 数据库初始化
5. 服务启动和监控
6. 功能验证和性能测试

### 运维监控
- 系统资源监控
- 应用性能监控
- 错误日志监控
- 业务指标监控
- 告警机制设置

## 📈 后续优化方向

### 功能增强
1. 支持更多技术指标
2. 增加机器学习模型
3. 实现自适应参数优化
4. 添加回测分析功能
5. 支持多品种组合策略

### 性能优化
1. 分布式计算支持
2. 实时数据流处理
3. 缓存策略优化
4. 数据库性能调优

### 用户体验
1. Web界面开发
2. 移动端应用
3. 可视化图表
4. 交互式配置

## 📝 开发日志

### 版本规划
- **v1.0-beta**: 基础功能实现 ✅ (当前版本)
  - 完整的技术指标体系
  - 多级信号生成系统
  - AI增强分析功能
  - 通知推送系统
  - 实时监控功能
- **v1.0**: 正式版本 🔄 (开发中)
  - 完善测试覆盖
  - 生产环境部署
  - 性能优化
- **v1.1**: 功能增强版本 ⏳ (计划中)
  - Web管理界面
  - 更多技术指标
  - 策略回测功能
- **v2.0**: 重大升级版本 ⏳ (规划中)
  - 机器学习模型集成
  - 多品种支持
  - 分布式架构

### 里程碑
- [x] 2024-12-15: 完成基础架构 ✅
- [x] 2024-12-20: 完成技术指标系统 ✅
- [x] 2024-12-25: 完成信号生成系统 ✅
- [x] 2024-12-30: 完成AI集成 ✅
- [x] 2025-01-05: 完成通知系统 ✅
- [x] 2025-01-10: 完成监控系统 ✅
- [x] 2025-01-20: 完成测试系统 ✅
- [x] 2025-01-30: 完成部署和运维 ✅

---

**注意**: 本开发计划将根据实际开发进度和需求变化进行调整和优化。