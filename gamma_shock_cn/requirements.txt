# 核心依赖
pandas>=1.5.0
numpy>=1.21.0
akshare>=1.9.0
requests>=2.28.0

# 技术指标计算
TA-Lib>=0.4.25
scipy>=1.9.0
scikit-learn>=1.1.0

# 数据存储
sqlite3
redis>=4.3.0

# 任务调度
schedule>=1.2.0
APScheduler>=3.9.0

# 邮件发送
smtplib
email-validator>=1.3.0
Jinja2>=3.1.0

# 日志和配置
python-dotenv>=0.20.0
loguru>=0.6.0
pyyaml>=6.0

# 开发工具
pytest>=7.1.0
pytest-cov>=3.0.0
pytest-mock>=3.8.0
black>=22.6.0
flake8>=5.0.0
pre-commit>=2.20.0
psutil>=5.9.0

# AI集成
openai>=0.27.0
httpx>=0.23.0

# 数据可视化（可选）
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0