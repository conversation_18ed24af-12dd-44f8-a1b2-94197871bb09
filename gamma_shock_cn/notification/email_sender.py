"""
邮件发送模块，负责发送邮件通知
"""
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from datetime import datetime
import my_mail_client as mymail

logger = logging.getLogger(__name__)

# 全局邮件参数
SMTP_SERVER = None
FROM_ADDR = None
PASSWORD = None
TO_ADDR = None

def setup_email(smtp_server, from_addr, password, to_addr):
    """设置邮件参数
    
    Args:
        smtp_server: SMTP服务器地址
        from_addr: 发送邮箱
        password: 邮箱授权码
        to_addr: 接收邮箱列表
    """
    global SMTP_SERVER, FROM_ADDR, PASSWORD, TO_ADDR
    SMTP_SERVER = smtp_server
    FROM_ADDR = from_addr
    PASSWORD = password
    TO_ADDR = to_addr
    logger.info("邮件参数设置完成")

def send_email(subject, content):
    """使用配置的邮件参数发送邮件通知
    
    Args:
        subject: 邮件主题
        content: 邮件内容
        
    Returns:
        bool: 是否发送成功
    """
    if not all([SMTP_SERVER, FROM_ADDR, PASSWORD, TO_ADDR]):
        logger.error("邮件参数未设置")
        return False
        
    try:
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = FROM_ADDR
        msg['To'] = ';'.join(TO_ADDR)  # 多个收件人用分号连接
        msg['Subject'] = Header(subject, 'utf-8')
        
        # 添加邮件内容
        msg.attach(MIMEText(content, 'plain', 'utf-8'))
        
        # 连接到SMTP服务器
        server = smtplib.SMTP_SSL(SMTP_SERVER, 465)
        server.login(FROM_ADDR, PASSWORD)
        
        # 发送邮件
        server.sendmail(FROM_ADDR, TO_ADDR, msg.as_string())
        server.quit()
        
        logger.info(f"邮件发送成功: {subject}")
        return True
    except Exception as e:
        logger.error(f"邮件发送失败: {str(e)}")
        return False

def send_qw_wrapper(content):
    """包装mymail.send_qw函数，如果原函数失败则使用新的发送函数
    
    Args:
        content: 邮件内容/标题
        
    Returns:
        bool: 是否发送成功
    """
    try:
        # 首先尝试使用原有的发送函数
        result = mymail.send_qw(content)
        # 只在成功时返回结果，不再同时使用备份发送
        if result:
            return result
        else:
            # 如果原函数返回False，则使用备份发送
            logger.warning("原有邮件发送功能返回失败，使用新的发送函数")
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            subject = f"中证1000策略信号[{current_time}]"
            return send_email(subject, content)
    except Exception as e:
        logger.warning(f"原有邮件发送功能失败: {str(e)}，使用新的发送函数")
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        subject = f"伽马冲击策略信号[{current_time}]"
        return send_email(subject, content)

# 导出原始的mymail.send_qw函数，以便在需要时能够访问
original_send_qw = mymail.send_qw 