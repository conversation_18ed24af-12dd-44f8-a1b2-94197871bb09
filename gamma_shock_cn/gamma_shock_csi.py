# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统
- 应用于中证1000(399852)、中证500ETF(510500)、创业板ETF(159915)等标的
- 基于gamma_shock_us.py策略改编，适配中国市场
- 利用AI导师(DeepSeek)进行交易信号分析
"""
import akshare as ak
import pandas as pd
from functools import lru_cache
from datetime import datetime, timedelta
import os
import sys
import json
import time
import requests
import schedule
import logging
import argparse
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from dotenv import load_dotenv

# --- AI导师模块 ---
AGENT_PROMPT_JSON_PATH = "ai/options_trading_prompt.json"
MASTER_AGENT_PROMPT = None

def load_master_agent_prompt():
    """
    /**
     * @description 在脚本启动时加载AI导师的配置文件
     */
    """
    global MASTER_AGENT_PROMPT
    try:
        with open(AGENT_PROMPT_JSON_PATH, 'r', encoding='utf-8') as f:
            MASTER_AGENT_PROMPT = json.load(f)
        logger.info(f"成功加载AI导师配置文件: {AGENT_PROMPT_JSON_PATH}")
    except Exception as e:
        logger.error(f"加载AI导师配置文件失败: {e}")
        MASTER_AGENT_PROMPT = None

def call_llm_api(system_prompt: str, user_prompt: str) -> str:
    """
    /**
     * @description 调用DeepSeek大语言模型API的函数。
     * @param {string} system_prompt - 系统提示词，定义了AI的角色和规则。
     * @param {string} user_prompt - 用户提示词，包含了实时市场数据和请求。
     * @returns {string} 从LLM返回的分析文本。
     */
    """
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        logger.error("错误: DEEPSEEK_API_KEY 环境变量未设置。")
        return "错误: DeepSeek API Key未配置。"

    url = "https://api.deepseek.com/chat/completions"

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }

    payload = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "temperature": 0.7,
        "stream": False
    }

    try:
        logger.info("正在调用DeepSeek Chat API...")
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        response.raise_for_status()

        data = response.json()
        content = data['choices'][0]['message']['content']
        logger.info("成功从DeepSeek Chat API获取分析。")
        return content

    except requests.exceptions.RequestException as e:
        logger.error(f"调用DeepSeek API时发生网络错误: {e}")
        return f"错误: 调用DeepSeek API时发生网络错误: {e}"
    except KeyError:
        logger.error(f"从DeepSeek API返回的数据格式不正确: {response.text}")
        return "错误: 从DeepSeek API返回的数据格式不正确。"
    except Exception as e:
        logger.error(f"调用DeepSeek API时发生未知错误: {e}")
        return f"错误: 调用DeepSeek API时发生未知错误: {e}"

def get_master_analysis(market_data: dict) -> str:
    """
    /**
     * @description 获取AI导师的综合分析。
     * @param {dict} market_data - 包含市场数据的结构化字典。
     * @returns {string} AI导师的详细分析。
     */
    """
    if not MASTER_AGENT_PROMPT:
        return "错误: AI导师的配置文件未加载。"

    try:
        system_prompt = json.dumps(MASTER_AGENT_PROMPT, ensure_ascii=False)
        user_prompt_text = f"""你好，Alata。我的A股期权双重确认策略脚本检测到一个潜在的交易信号，请按照结论优先的格式进行分析。

请严格按照以下格式回复：

## 期权策略建议
**方向**: [买入看涨期权/买入看跌期权/观察等待]
**行权价**: [具体价格]（[价格相对性描述]）
**到期日**: [建议天数范围]
**仓位**: [百分比]（[基于信号强度的说明]）

## 风险管理
**止损**: [具体条件或百分比]
**止盈**: [分层策略描述]
**Roll Up**: [滚动策略说明]

## 分析依据
[详细的技术分析和市场逻辑]

以下是相关数据：
{json.dumps(market_data, indent=2, ensure_ascii=False)}"""

        analysis = call_llm_api(system_prompt, user_prompt_text)
        return analysis

    except Exception as e:
        logger.error(f"获取AI导师分析时出错: {e}")
        return f"获取AI导师分析时出错: {e}"

# --- 配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), "gamma_shock_csi.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 导入自定义模块 - 使用try/except处理可选依赖
try:
    from data.cache_manager import setup_cache_dirs, save_cache, load_cache
except ImportError:
    logger.warning("cache_manager模块未找到，将使用简化的缓存功能")
    def setup_cache_dirs(data_dir, cache_dir):
        os.makedirs(cache_dir, exist_ok=True)
    def save_cache(data, path):
        pass
    def load_cache(path):
        return None

try:
    from utils.email_sender import send_qw_wrapper
except ImportError:
    logger.warning("email_sender模块未找到，将使用简化的通知功能")
    def send_qw_wrapper(message):
        print(f"通知: {message}")

CONFIG = {
    'data_dir': "/Users/<USER>/VSCodeProjects/data/csi_data",
    'symbols': {
        'zz1000': {'code': '399852', 'name': '中证1000'},
        'zz500_etf': {'code': '510500', 'name': '中证500ETF'},
        'cyb_etf': {'code': '159915', 'name': '创业板ETF'}
    },
    'period': "5"  # 单位：分钟
}

data_dir = str(CONFIG['data_dir']) # 明确转换为字符串以帮助linter
cache_dir = os.path.join(data_dir, "cache")
setup_cache_dirs(data_dir, cache_dir)

# --- 数据获取 ---

def get_csi_symbol_map():
    """
    /**
     * @description 获取A股标的代码映射。
     * @returns {dict} 标的到代码的映射字典。
     */
    """
    symbol_map = {}
    for symbol, info in CONFIG['symbols'].items():
        symbol_map[symbol] = info['code']
    logger.info(f"已创建A股标的映射: {symbol_map}")
    return symbol_map

def calculate_stock_volatility(stock_data: pd.DataFrame, period: int = 20) -> float:
    """
    /**
     * @description 计算个股的历史波动率（年化）
     * @param {pd.DataFrame} stock_data - 股票数据
     * @param {int} period - 计算周期（默认20日）
     * @returns {float} 年化波动率
     */
    """
    if len(stock_data) < period:
        return 0.0

    # 计算收益率
    returns = stock_data['close'].pct_change().dropna()

    if len(returns) < period:
        return 0.0

    # 计算最近period天的波动率并年化
    recent_returns = returns.tail(period)
    volatility = recent_returns.std() * (252 ** 0.5)  # 年化（假设252个交易日）

    return volatility

def get_csi_volatility_data(symbol: str, code: str):
    """
    /**
     * @description 获取A股标的的波动率数据
     * @param {string} symbol - 标的代码 (e.g., "zz1000")
     * @param {string} code - 具体代码 (e.g., "399852")
     * @returns {tuple} (历史波动率, 分时波动率, 波动率百分位)
     */
    """
    hist_volatility = None
    intraday_volatility = None
    vol_percentile = None

    try:
        if symbol == 'zz1000':
            # 中证1000历史波动率
            try:
                hist_vol_df = ak.index_option_1000index_qvix()
                if not hist_vol_df.empty:
                    hist_volatility = hist_vol_df.iloc[-1]['qvix'] / 100  # 转换为小数
                    logger.info(f"获取到{symbol}历史波动率: {hist_volatility:.2%}")
            except Exception as e:
                logger.warning(f"获取{symbol}历史波动率失败: {e}")

            # 中证1000分时波动率
            try:
                intraday_vol_df = ak.index_option_1000index_min_qvix()
                if not intraday_vol_df.empty:
                    intraday_volatility = intraday_vol_df.iloc[-1]['qvix'] / 100
                    logger.info(f"获取到{symbol}分时波动率: {intraday_volatility:.2%}")
            except Exception as e:
                logger.warning(f"获取{symbol}分时波动率失败: {e}")

        elif symbol == 'zz500_etf':
            # 中证500ETF历史波动率
            try:
                hist_vol_df = ak.index_option_500etf_qvix()
                if not hist_vol_df.empty:
                    hist_volatility = hist_vol_df.iloc[-1]['qvix'] / 100
                    logger.info(f"获取到{symbol}历史波动率: {hist_volatility:.2%}")
            except Exception as e:
                logger.warning(f"获取{symbol}历史波动率失败: {e}")

            # 中证500ETF分时波动率
            try:
                intraday_vol_df = ak.index_option_500etf_min_qvix()
                if not intraday_vol_df.empty:
                    intraday_volatility = intraday_vol_df.iloc[-1]['qvix'] / 100
                    logger.info(f"获取到{symbol}分时波动率: {intraday_volatility:.2%}")
            except Exception as e:
                logger.warning(f"获取{symbol}分时波动率失败: {e}")

        elif symbol == 'cyb_etf':
            # 创业板历史波动率
            try:
                hist_vol_df = ak.index_option_cyb_qvix()
                if not hist_vol_df.empty:
                    hist_volatility = hist_vol_df.iloc[-1]['qvix'] / 100
                    logger.info(f"获取到{symbol}历史波动率: {hist_volatility:.2%}")
            except Exception as e:
                logger.warning(f"获取{symbol}历史波动率失败: {e}")

            # 创业板分时波动率
            try:
                intraday_vol_df = ak.index_option_cyb_min_qvix()
                if not intraday_vol_df.empty:
                    intraday_volatility = intraday_vol_df.iloc[-1]['qvix'] / 100
                    logger.info(f"获取到{symbol}分时波动率: {intraday_volatility:.2%}")
            except Exception as e:
                logger.warning(f"获取{symbol}分时波动率失败: {e}")

        # 如果API获取失败，使用默认值
        if hist_volatility is None:
            hist_volatility = 0.2
        if intraday_volatility is None:
            intraday_volatility = hist_volatility

        # 简化的百分位计算
        vol_percentile = 0.5  # 默认中位数

    except Exception as e:
        logger.error(f"获取{symbol}波动率数据时出错: {e}")
        hist_volatility = 0.2
        intraday_volatility = 0.2
        vol_percentile = 0.5

    return hist_volatility, intraday_volatility, vol_percentile

def get_csi_stock_data(symbol: str, code: str, period: str = "5"):
    """
    /**
     * @description 获取A股标的分钟线数据，并进行缓存和处理。
     * @param {string} symbol - 标的代码 (e.g., "zz1000")。
     * @param {string} code - 具体代码 (e.g., "399852")。
     * @param {string} period - K线周期（分钟）。
     * @returns {pd.DataFrame | None} 处理后的分钟线数据。
     */
    """
    cache_file = os.path.join(cache_dir, f"stock_{symbol}_{period}min.csv")

    # 尝试从缓存加载
    if os.path.exists(cache_file):
        file_mod_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        # 对于分钟线数据，设置一个较短的缓存有效期
        if (datetime.now() - file_mod_time).seconds < int(period) * 60:
            try:
                logger.info(f"使用缓存的 {symbol} 数据。")
                df = pd.read_csv(cache_file)
                df['date'] = pd.to_datetime(df['date'])
                return df.set_index('date')
            except Exception as e:
                logger.warning(f"读取 {symbol} 缓存失败: {e}, 将重新获取。")

    logger.info(f"从API获取 {symbol} ({code}) 的分钟线数据...")
    try:
        # 计算时间范围
        end_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        start_date = (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d %H:%M:%S")

        stock_hist_min_df = None

        if symbol == 'zz1000':
            # 中证1000指数分钟线数据
            stock_hist_min_df = ak.index_zh_a_hist_min_em(
                symbol=code,
                period=period,
                start_date=start_date,
                end_date=end_date
            )
        elif symbol in ['zz500_etf', 'cyb_etf']:
            # ETF分钟线数据 - 统一使用index_zh_a_hist_min_em接口
            stock_hist_min_df = ak.index_zh_a_hist_min_em(
                symbol=code,
                period=period,
                start_date=start_date,
                end_date=end_date
            )

        if stock_hist_min_df is None or stock_hist_min_df.empty:
            logger.warning(f"未能获取到 {symbol} 的分钟线数据。")
            return None

        # 统一列名
        if '时间' in stock_hist_min_df.columns:
            stock_hist_min_df.rename(columns={'时间': 'date'}, inplace=True)

        # 统一OHLCV列名
        column_mapping = {
            '开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low',
            '成交量': 'volume', '成交额': 'amount'
        }
        stock_hist_min_df.rename(columns=column_mapping, inplace=True)

        # 处理成交量字段 - 优先使用成交额，如果没有则使用成交量
        if 'amount' in stock_hist_min_df.columns and 'volume' not in stock_hist_min_df.columns:
            stock_hist_min_df['volume'] = stock_hist_min_df['amount']
        elif 'volume' not in stock_hist_min_df.columns:
            stock_hist_min_df['volume'] = 0

        stock_hist_min_df['date'] = pd.to_datetime(stock_hist_min_df['date'])
        stock_hist_min_df.set_index('date', inplace=True)

        # 将数据类型转换为数值型
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in stock_hist_min_df.columns:
                stock_hist_min_df[col] = pd.to_numeric(stock_hist_min_df[col], errors='coerce')

        if stock_hist_min_df.empty:
            logger.warning(f"{symbol} 数据处理后为空。")
            return None

        stock_hist_min_df.to_csv(cache_file)
        logger.info(f"已更新 {symbol} 的数据缓存。")
        return stock_hist_min_df

    except Exception as e:
        logger.error(f"获取 {symbol} 数据时出错: {e}")
        # 如果API失败，尝试返回旧缓存
        if os.path.exists(cache_file):
            try:
                logger.warning(f"API请求失败，使用 {symbol} 的旧缓存数据。")
                df = pd.read_csv(cache_file)
                df['date'] = pd.to_datetime(df['date'])
                return df.set_index('date')
            except Exception as cache_err:
                logger.error(f"读取 {symbol} 的旧缓存也失败了: {cache_err}")
        return None

# --- 策略分析 ---

def analyze_csi_stock_data(symbol: str, code: str):
    """
    /**
     * @description 分析单个A股标的数据并生成交易信号（双重确认升级版策略）。
     * @param {string} symbol - 标的代码 (e.g., "zz1000")。
     * @param {string} code - 具体代码 (e.g., "399852")。
     * @returns {tuple} 包含市场数据的字典和信号检测标志的元组 (market_data, signal_detected)。
     */
     """
    logger.info(f"开始分析 {symbol} 数据...")

    stock_data = get_csi_stock_data(symbol, code, period=str(CONFIG['period']))

    if stock_data is None or stock_data.empty:
        logger.error(f"无法获取 {symbol} 数据，跳过分析。")
        return None, False

    # --- 计算技术指标（双重确认升级版） ---
    # 多条均线系统
    stock_data['EMA8'] = stock_data['close'].ewm(span=8, adjust=False).mean()
    stock_data['EMA21'] = stock_data['close'].ewm(span=21, adjust=False).mean()
    stock_data['EMA55'] = stock_data['close'].ewm(span=55, adjust=False).mean()
    stock_data['EMA125'] = stock_data['close'].ewm(span=125, adjust=False).mean()

    # 改进的KDJ指标（使用14周期）
    low_list = stock_data['low'].rolling(window=14, min_periods=1).min()
    high_list = stock_data['high'].rolling(window=14, min_periods=1).max()
    rsv = (stock_data['close'] - low_list) / (high_list - low_list) * 100
    stock_data['K'] = rsv.ewm(com=2, adjust=False).mean()  # SMA(RSV,3,1) 近似
    stock_data['D'] = stock_data['K'].ewm(com=2, adjust=False).mean()  # SMA(K,3,1) 近似
    stock_data['J'] = 3 * stock_data['K'] - 2 * stock_data['D']

    # MACD指标
    stock_data['EMA12'] = stock_data['close'].ewm(span=12, adjust=False).mean()
    stock_data['EMA26'] = stock_data['close'].ewm(span=26, adjust=False).mean()
    stock_data['DIF'] = stock_data['EMA12'] - stock_data['EMA26']
    stock_data['DEA'] = stock_data['DIF'].ewm(span=9, adjust=False).mean()
    stock_data['MACD'] = (stock_data['DIF'] - stock_data['DEA']) * 2

    # 成交量移动平均线
    stock_data['VOL_MA10'] = stock_data['volume'].rolling(window=10, min_periods=1).mean()

    # --- 威廉指标变种（新增） ---
    N = 19
    stock_data['VAR1'] = stock_data['high'].rolling(window=N, min_periods=1).max()  # HHV(HIGH,19)
    stock_data['VAR2'] = stock_data['low'].rolling(window=N, min_periods=1).min()   # LLV(LOW,19)

    # 计算威廉指标基础值
    williams_base = (stock_data['close'] - stock_data['VAR2']) / (stock_data['VAR1'] - stock_data['VAR2'])
    williams_base = williams_base.fillna(0.5)  # 处理除零情况

    # ZLS: 21日EMA平滑后减0.5 (长期线)
    stock_data['ZLS'] = williams_base.ewm(span=21, adjust=False).mean() - 0.5

    # CZX: 5日EMA平滑后减0.5 (短期线)
    stock_data['CZX'] = williams_base.ewm(span=5, adjust=False).mean() - 0.5

    # HLB: 两线差值
    stock_data['HLB'] = stock_data['CZX'] - stock_data['ZLS']

    # --- 波动率分析 ---
    stock_volatility = None
    intraday_volatility = None
    vol_percentile = None

    try:
        # 获取API波动率数据
        hist_vol, intraday_vol, vol_pct = get_csi_volatility_data(symbol, code)
        stock_volatility = hist_vol
        intraday_volatility = intraday_vol
        vol_percentile = vol_pct

        # 如果API失败，使用K线数据计算
        if stock_volatility is None or stock_volatility == 0:
            stock_volatility = calculate_stock_volatility(stock_data, period=20)
            if stock_volatility > 0:
                # 计算波动率的历史百分位
                volatilities = []
                for i in range(20, len(stock_data)):
                    hist_vol = calculate_stock_volatility(stock_data.iloc[:i+1], period=20)
                    if hist_vol > 0:
                        volatilities.append(hist_vol)

                if volatilities:
                    vol_percentile = sum(1 for v in volatilities if v <= stock_volatility) / len(volatilities)
                    logger.info(f"{symbol}: 个股波动率={stock_volatility:.2%}, 百分位={vol_percentile:.2%}")
    except Exception as e:
        logger.warning(f"{symbol}: 计算波动率时出错: {e}")
        stock_volatility = 0.2  # 默认值
        intraday_volatility = 0.2
        vol_percentile = 0.5

    # --- 准备返回数据 ---
    if len(stock_data) < 3:
        logger.warning(f"{symbol} 数据点不足，无法生成信号。")
        return None, False

    # 获取当前和历史数据点
    current_row = stock_data.iloc[-1]
    prev_row = stock_data.iloc[-2] if len(stock_data) >= 2 else current_row
    prev2_row = stock_data.iloc[-3] if len(stock_data) >= 3 else prev_row

    # --- 威廉指标信号检测 ---
    williams_c_signal = False  # 超卖反弹信号
    williams_p_signal = False  # 超买回调信号

    # C信号: CROSS(CZX, ZLS) AND ZLS < 0.1
    if (current_row['CZX'] > current_row['ZLS'] and
        prev_row['CZX'] <= prev_row['ZLS'] and
        current_row['ZLS'] < 0.1):
        williams_c_signal = True
        logger.info(f"{symbol}: 威廉指标C信号 - 超卖反弹 (ZLS={current_row['ZLS']:.3f})")

    # P信号: CROSS(ZLS, CZX) AND ZLS > 0.25
    if (current_row['ZLS'] > current_row['CZX'] and
        prev_row['ZLS'] <= prev_row['CZX'] and
        current_row['ZLS'] > 0.25):
        williams_p_signal = True
        logger.info(f"{symbol}: 威廉指标P信号 - 超买回调 (ZLS={current_row['ZLS']:.3f})")

    # --- 双重确认升级版信号逻辑 ---
    signal_type = "无信号"
    signal_strength = 0
    volume_confirmation = False
    williams_confirmation = ""

    # 成交量确认条件 - 使用1.3倍阈值（根据项目设计文档）
    if current_row['volume'] > current_row['VOL_MA10'] * 1.3:
        volume_confirmation = True
        logger.info(f"{symbol}: 检测到成交量异常放大 (当前: {current_row['volume']:.0f}, 10日均量: {current_row['VOL_MA10']:.0f})")

    # 1. 多头进场信号
    # CROSS(EMA8, EMA21) AND REF(EMA8,1)<REF(EMA21,1) AND REF(CLOSE,1)>REF(EMA8,1)
    if (current_row['EMA8'] > current_row['EMA21'] and
        prev_row['EMA8'] < prev_row['EMA21'] and
        prev_row['close'] > prev_row['EMA8']):
        signal_type = "多头进场信号"
        signal_strength = 3
        if volume_confirmation:
            signal_strength += 1
        if williams_c_signal:  # 威廉指标确认
            signal_strength += 1
            williams_confirmation = "威廉C信号确认"

    # 2. 空头进场信号
    # CROSS(EMA21, EMA8) AND REF(EMA8,1)>REF(EMA21,1) AND REF(CLOSE,1)<REF(EMA8,1)
    elif (current_row['EMA21'] > current_row['EMA8'] and
          prev_row['EMA8'] > prev_row['EMA21'] and
          prev_row['close'] < prev_row['EMA8']):
        signal_type = "空头进场信号"
        signal_strength = 3
        if volume_confirmation:
            signal_strength += 1
        if williams_p_signal:  # 威廉指标确认
            signal_strength += 1
            williams_confirmation = "威廉P信号确认"

    # 3. 多头预警
    # REF(CLOSE,1)>REF(EMA8,1) AND REF(CLOSE,1)<REF(EMA21,1) AND CROSS(J,K)
    elif (prev_row['close'] > prev_row['EMA8'] and
          prev_row['close'] < prev_row['EMA21'] and
          current_row['J'] > current_row['K'] and
          prev_row['J'] < prev_row['K']):
        signal_type = "多头预警信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        if williams_c_signal:  # 威廉指标确认可升级为进场信号
            signal_type = "多头进场信号(威廉确认)"
            signal_strength += 1
            williams_confirmation = "威廉C信号升级"

    # 4. 空头预警
    # REF(CLOSE,1)<REF(EMA8,1) AND REF(CLOSE,1)>REF(EMA21,1) AND CROSS(K,J)
    elif (prev_row['close'] < prev_row['EMA8'] and
          prev_row['close'] > prev_row['EMA21'] and
          current_row['K'] > current_row['J'] and
          prev_row['K'] < prev_row['J']):
        signal_type = "空头预警信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        if williams_p_signal:  # 威廉指标确认可升级为进场信号
            signal_type = "空头进场信号(威廉确认)"
            signal_strength += 1
            williams_confirmation = "威廉P信号升级"

    # 5. 纯威廉指标信号（当主策略无明确信号时）
    elif williams_c_signal:
        signal_type = "威廉超卖反弹信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        williams_confirmation = "纯威廉C信号"
    elif williams_p_signal:
        signal_type = "威廉超买回调信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        williams_confirmation = "纯威廉P信号"

    # 6. 成交量异常但无明确技术信号
    elif volume_confirmation:
        signal_type = "成交量异常信号"
        signal_strength = 1

    # 判断是否有信号
    signal_detected = signal_strength >= 2

    market_data = {
        'symbol': symbol,
        'symbol_name': CONFIG['symbols'][symbol]['name'],
        'timestamp': current_row.name.strftime('%Y-%m-%d %H:%M:%S'),
        'price': current_row['close'],
        'signal_type': signal_type,
        'signal_strength': signal_strength,
        'volume_confirmation': volume_confirmation,
        'williams_confirmation': williams_confirmation,
        # 技术指标数据
        'EMA8': current_row['EMA8'],
        'EMA21': current_row['EMA21'],
        'EMA55': current_row['EMA55'],
        'EMA125': current_row['EMA125'],
        'K_value': current_row['K'],
        'D_value': current_row['D'],
        'J_value': current_row['J'],
        'DIF': current_row['DIF'],
        'DEA': current_row['DEA'],
        'MACD': current_row['MACD'],
        'volume': current_row['volume'],
        'vol_ma10': current_row['VOL_MA10'],
        'vol_ratio': current_row['volume'] / current_row['VOL_MA10'] if current_row['VOL_MA10'] > 0 else 0,
        # 威廉指标数据
        'ZLS': current_row['ZLS'],
        'CZX': current_row['CZX'],
        'HLB': current_row['HLB'],
        'williams_c_signal': williams_c_signal,
        'williams_p_signal': williams_p_signal,
        # 波动率数据
        'stock_volatility': stock_volatility,
        'intraday_volatility': intraday_volatility,
        'vol_percentile': vol_percentile,
        'volatility_source': "csi_api",
        # 趋势分析
        'trend_short': "上涨" if current_row['EMA8'] > current_row['EMA21'] else "下跌",
        'trend_medium': "上涨" if current_row['EMA21'] > current_row['EMA55'] else "下跌",
        'trend_long': "上涨" if current_row['EMA55'] > current_row['EMA125'] else "下跌",
        'signal': signal_type
    }

    if signal_detected:
        confirmation_info = f" ({williams_confirmation})" if williams_confirmation else ""
        logger.info(f"{symbol} 检测到信号: {signal_type} (强度: {signal_strength}){confirmation_info}")

    return market_data, signal_detected

# --- 任务调度 ---

def is_csi_trading_hours(test_mode=False):
    """
    /**
     * @description 判断当前是否在A股交易时段（北京时间 9:30-11:30 & 13:00-15:00）。
     * @param {boolean} test_mode - 测试模式，如果为True则忽略交易时间限制
     * @returns {boolean} 如果是交易时间则返回True。
     */
    """
    if test_mode:
        logger.info("测试模式：忽略交易时间限制")
        return True

    now = datetime.now()

    # 只在工作日运行
    if now.weekday() >= 5:
        logger.info("当前为周末，非交易日")
        return False

    # 交易时段: 9:25-11:35, 12:55-15:05 (留出5分钟缓冲)
    morning_start = now.replace(hour=9, minute=25, second=0, microsecond=0).time()
    morning_end = now.replace(hour=11, minute=35, second=0, microsecond=0).time()
    afternoon_start = now.replace(hour=12, minute=55, second=0, microsecond=0).time()
    afternoon_end = now.replace(hour=15, minute=5, second=0, microsecond=0).time()

    current_time = now.time()
    is_trading = (morning_start <= current_time <= morning_end) or \
                 (afternoon_start <= current_time <= afternoon_end)

    if not is_trading:
        logger.info(f"当前时间 {current_time} 不在交易时段")

    return is_trading

def job(test_mode=False, force_run=False):
    """
    /**
     * @description 定时执行的任务，分析所有配置的A股标的。
     * @param {boolean} test_mode - 测试模式，忽略交易时间限制
     * @param {boolean} force_run - 强制运行一次
     */
    """
    if not test_mode and not force_run and not is_csi_trading_hours():
        logger.info("当前非A股交易时段，跳过运行。")
        return

    start_time = time.time()
    logger.info("开始A股策略定时任务...")

    symbol_map = get_csi_symbol_map()
    if not symbol_map:
        logger.error("无法获取标的映射，任务中止。")
        return

    for symbol, code in symbol_map.items():
        try:
            market_data, signal_detected = analyze_csi_stock_data(symbol, code)
            if signal_detected and market_data:
                logger.info(f"为 {symbol} 检测到信号，正在获取AI导师分析...")
                master_analysis = get_master_analysis(market_data)
                logger.info(f"AI导师分析结果 for {symbol}:\n{master_analysis}")
                # 发送企业微信通知
                send_qw_wrapper(f"--- AI导师A股期权分析: {market_data['symbol_name']} ({symbol}) ---\n{master_analysis}")
            elif market_data:
                logger.info(f"{symbol} 未检测到明确交易信号。")
        except Exception as e:
            logger.error(f"分析 {symbol} 数据时出错: {e}", exc_info=True)

    elapsed_time = time.time() - start_time
    logger.info(f"任务完成，耗时: {elapsed_time:.2f}秒")

def run_scheduler(test_mode=False):
    """
    /**
     * @description 启动定时任务调度器。
     * @param {boolean} test_mode - 测试模式
     */
    """
    logger.info("启动A股策略定时任务调度器...")

    schedule.every(int(CONFIG['period'])).minutes.do(job, test_mode=test_mode)

    logger.info("立即执行一次任务...")
    job(test_mode=test_mode, force_run=True)

    logger.info(f"定时任务已设置，每{CONFIG['period']}分钟运行一次。")

    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到中断信号，退出程序。")
            break
        except Exception as e:
            logger.error(f"调度器出错: {str(e)}")
            send_qw_wrapper(f"A股策略调度器出错: {str(e)}")
            time.sleep(60)

def init():
    """
    /**
     * @description 初始化程序，加载配置和检查API密钥。
     */
    """
    # 加载 .env 文件中的环境变量
    if not load_dotenv():
        logger.warning(".env 文件未找到。请确保在项目根目录下创建了 .env 文件，并已设置 DEEPSEEK_API_KEY。")
        logger.warning("例如: DEEPSEEK_API_KEY=\"your_key_here\"")

    load_master_agent_prompt()

    if not os.getenv("DEEPSEEK_API_KEY"):
        logger.error("关键错误: DEEPSEEK_API_KEY 环境变量未设置。程序无法继续。")
        logger.info("请在终端使用 'export DEEPSEEK_API_KEY=\"your_key_here\"' 命令设置。")
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='A股期权策略跟踪系统')
    parser.add_argument('--test-mode', action='store_true', help='测试模式：忽略交易时间限制')
    parser.add_argument('--force-run', action='store_true', help='强制运行一次分析')
    parser.add_argument('--run-once', action='store_true', help='运行一次性分析（兼容旧参数）')

    args = parser.parse_args()

    try:
        init()

        if not MASTER_AGENT_PROMPT:
            logger.error("AI导师配置未能加载，程序退出。")
            sys.exit(1)

        if args.run_once or args.force_run:
            logger.info("运行一次性分析...")
            job(test_mode=args.test_mode, force_run=True)
            logger.info("一次性分析完成。")
        else:
            run_scheduler(test_mode=args.test_mode)

    except Exception as e:
        error_msg = f"程序主流程运行出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        try:
            send_qw_wrapper(f"A股策略程序运行出错: {str(e)}")
        except Exception as notify_e:
            logger.error(f"发送错误通知也失败了: {notify_e}")