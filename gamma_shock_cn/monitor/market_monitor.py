#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场监控模块

主要功能：
- 定时监控市场数据
- 生成交易信号
- AI信号确认
- 风险控制
- 通知推送

作者: AI Assistant
创建时间: 2024-12-11
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
import json
import os
from pathlib import Path

# 导入项目模块
from data import DataFetcher, DataValidator
from data.data_storage import DataStorageManager
from indicators import (
    calculate_all_indicators,
    calculate_williams_variant,
    calculate_volatility_indicators,
    analyze_option_sentiment
)
from strategy.signal_generator import generate_trading_signals, TradingSignal, SignalLevel, SignalType
from ai import create_ai_client, AIAnalysisRequest
from config.settings import settings

logger = logging.getLogger(__name__)

@dataclass
class MonitoringResult:
    """监控结果数据结构"""
    symbol: str
    timestamp: datetime
    market_data: Dict[str, Any]
    technical_indicators: Dict[str, Any]
    volatility_analysis: Dict[str, Any]
    option_sentiment: Optional[Dict[str, Any]]
    signals: List[TradingSignal]
    ai_confirmation: Optional[Dict[str, Any]]
    risk_assessment: Dict[str, Any]
    notifications: List[str]
    processing_time: float
    success: bool
    error_message: Optional[str] = None

class MarketMonitor:
    """
    市场监控器
    
    负责定时监控市场数据，生成交易信号，并进行AI确认
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化市场监控器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or settings.system.SCHEDULE_CONFIG
        
        # 初始化组件
        self.data_fetcher = DataFetcher()
        self.data_storage = DataStorageManager()
        self.data_validator = DataValidator()
        
        # 初始化AI客户端
        ai_config = settings.ai.AI_ANALYSIS_CONFIG.copy()
        api_key = settings.ai.DEEPSEEK_API_KEY
        self.ai_client = create_ai_client(api_key, ai_config)
        
        # 监控配置
        symbols_list = []
        for target in settings.monitoring.PRIMARY_TARGETS.values():
            symbols_list.append(target)
        self.symbols = symbols_list
        self.monitoring_interval = self.config.get('monitoring_interval', 300)  # 5分钟
        self.ai_confirmation_threshold = self.config.get('ai_confirmation_threshold', 3)  # 信号等级3以上需要AI确认
        self.test_mode = self.config.get('test_mode', False)  # 测试模式，忽略交易时间限制
        
        # 运行状态
        self.is_running = False
        self.last_monitoring_time = {}
        self.monitoring_stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'signals_generated': 0,
            'ai_confirmations': 0
        }
        
        # 结果存储
        self.latest_results = {}
        self.signal_history = []
        
        logger.info("市场监控器初始化完成")
    
    async def start_monitoring(self):
        """
        开始监控
        """
        if self.is_running:
            logger.warning("监控已在运行中")
            return
        
        self.is_running = True
        logger.info("开始市场监控")
        
        try:
            while self.is_running:
                start_time = time.time()
                
                # 检查是否在交易时间
                if not self._is_trading_time():
                    logger.debug("非交易时间，跳过监控")
                    await asyncio.sleep(60)  # 1分钟后再检查
                    continue
                
                # 执行监控任务
                await self._run_monitoring_cycle()
                
                # 更新统计信息
                self.monitoring_stats['total_runs'] += 1
                
                # 计算下次监控时间
                elapsed_time = time.time() - start_time
                sleep_time = max(0, self.monitoring_interval - elapsed_time)
                
                logger.info(f"监控周期完成，耗时: {elapsed_time:.2f}秒，下次监控: {sleep_time:.0f}秒后")
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")
            self.monitoring_stats['failed_runs'] += 1
        finally:
            self.is_running = False
            logger.info("市场监控已停止")
    
    def stop_monitoring(self):
        """
        停止监控
        """
        self.is_running = False
        logger.info("正在停止市场监控...")
    
    async def _run_monitoring_cycle(self):
        """
        运行一个监控周期
        """
        try:
            # 并发监控所有标的
            tasks = []
            for symbol_info in self.symbols:
                task = asyncio.create_task(
                    self._monitor_single_symbol(symbol_info)
                )
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            successful_count = 0
            for i, result in enumerate(results):
                symbol_info = self.symbols[i]
                symbol = symbol_info['symbol']
                
                if isinstance(result, Exception):
                    logger.error(f"监控 {symbol} 时出错: {result}")
                    self.monitoring_stats['failed_runs'] += 1
                elif result and result.success:
                    successful_count += 1
                    self.latest_results[symbol] = result
                    
                    # 统计信号数量
                    if result.signals:
                        self.monitoring_stats['signals_generated'] += len(result.signals)
                        self.signal_history.extend(result.signals)
                    
                    # 统计AI确认次数
                    if result.ai_confirmation:
                        self.monitoring_stats['ai_confirmations'] += 1
            
            if successful_count > 0:
                self.monitoring_stats['successful_runs'] += 1
            
            logger.info(f"监控周期完成: {successful_count}/{len(self.symbols)} 个标的成功")
            
        except Exception as e:
            logger.error(f"监控周期执行失败: {e}")
            raise
    
    async def _monitor_single_symbol(self, symbol_info: Dict[str, Any]) -> Optional[MonitoringResult]:
        """
        监控单个标的
        
        Args:
            symbol_info: 标的信息字典
            
        Returns:
            监控结果
        """
        symbol = symbol_info['symbol']
        start_time = time.time()
        
        try:
            logger.debug(f"开始监控 {symbol}")
            
            # 1. 获取市场数据
            market_data = await self._fetch_market_data(symbol_info)
            if not market_data:
                raise ValueError("获取市场数据失败")
            
            # 2. 计算技术指标
            technical_indicators = self._calculate_technical_indicators(
                market_data['price_data']
            )
            
            # 3. 计算波动率分析
            volatility_analysis = self._calculate_volatility_analysis(
                market_data['price_data'],
                market_data.get('vix_data')
            )
            
            # 4. 分析期权情绪（如果有期权数据）
            option_sentiment = None
            if market_data.get('option_data'):
                option_sentiment = self._analyze_option_sentiment(
                    market_data['option_data']
                )
            
            # 5. 生成交易信号
            signals = self._generate_trading_signals(
                symbol,
                market_data['price_data'],
                market_data.get('option_data'),
                market_data.get('vix_data')
            )
            
            # 6. AI信号确认（如果需要）
            ai_confirmation = None
            if signals and self._need_ai_confirmation(signals):
                ai_confirmation = await self._get_ai_confirmation(
                    symbol, signals, market_data, technical_indicators,
                    volatility_analysis, option_sentiment
                )
            
            # 7. 风险评估
            risk_assessment = self._assess_risk(
                market_data, technical_indicators, volatility_analysis, signals
            )
            
            # 8. 生成通知
            notifications = self._generate_notifications(
                symbol, signals, ai_confirmation, risk_assessment
            )
            
            # 9. 存储数据
            await self._store_monitoring_data(
                symbol, market_data, technical_indicators, 
                volatility_analysis, option_sentiment, signals
            )
            
            # 创建监控结果
            result = MonitoringResult(
                symbol=symbol,
                timestamp=datetime.now(),
                market_data=market_data,
                technical_indicators=technical_indicators,
                volatility_analysis=volatility_analysis,
                option_sentiment=option_sentiment,
                signals=signals,
                ai_confirmation=ai_confirmation,
                risk_assessment=risk_assessment,
                notifications=notifications,
                processing_time=time.time() - start_time,
                success=True
            )
            
            logger.info(f"监控 {symbol} 完成: {len(signals)} 个信号, 耗时: {result.processing_time:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"监控 {symbol} 失败: {e}")
            return MonitoringResult(
                symbol=symbol,
                timestamp=datetime.now(),
                market_data={},
                technical_indicators={},
                volatility_analysis={},
                option_sentiment=None,
                signals=[],
                ai_confirmation=None,
                risk_assessment={'level': 'unknown'},
                notifications=[],
                processing_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )
    
    async def _fetch_market_data(self, symbol_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        获取市场数据
        
        Args:
            symbol_info: 标的信息
            
        Returns:
            市场数据字典
        """
        try:
            symbol = symbol_info['symbol']
            data_types = symbol_info.get('data_types', ['daily', 'minute'])
            
            market_data = {}
            
            # 获取实时数据（DataFetcher只有get_realtime_data方法）
            realtime_data = self.data_fetcher.get_realtime_data(symbol)
            if realtime_data is not None:
                # 将MarketData转换为DataFrame格式用于技术分析
                import pandas as pd
                price_df = pd.DataFrame([{
                    'open': realtime_data.open_price,
                    'high': realtime_data.high_price,
                    'low': realtime_data.low_price,
                    'close': realtime_data.close_price,
                    'volume': realtime_data.volume
                }], index=[realtime_data.timestamp])
                market_data['price_data'] = price_df
                market_data['realtime_data'] = realtime_data
            
            # 模拟其他数据（实际项目中需要实现相应的数据获取方法）
            if symbol_info.get('include_vix', False):
                # 模拟VIX数据
                market_data['vix_data'] = {'current_vix': 20.0, 'vix_regime': 'medium'}
            
            if symbol_info.get('include_options', False):
                # 模拟期权数据
                market_data['option_data'] = {'pcr_ratio': 1.0, 'sentiment': 'neutral'}
            
            return market_data if market_data else None
            
        except Exception as e:
            logger.error(f"获取市场数据失败 ({symbol_info['symbol']}): {e}")
            return None
    
    def _calculate_technical_indicators(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算技术指标
        
        Args:
            price_data: 价格数据
            
        Returns:
            技术指标字典
        """
        try:
            # 计算所有技术指标
            indicators_df = calculate_all_indicators(price_data)
            
            # 计算威廉指标变种
            williams_df = calculate_williams_variant(price_data)
            
            # 提取最新值
            latest_indicators = {}
            if not indicators_df.empty:
                latest = indicators_df.iloc[-1]
                for col in indicators_df.columns:
                    latest_indicators[col] = latest[col]
            
            # 添加威廉指标
            if not williams_df.empty:
                latest_williams = williams_df.iloc[-1]
                for col in williams_df.columns:
                    latest_indicators[f'WILLIAMS_{col}'] = latest_williams[col]
            
            return latest_indicators
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}
    
    def _calculate_volatility_analysis(self, price_data: pd.DataFrame, 
                                     vix_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        计算波动率分析
        
        Args:
            price_data: 价格数据
            vix_data: VIX数据
            
        Returns:
            波动率分析字典
        """
        try:
            vol_data, vol_stats = calculate_volatility_indicators(price_data, vix_data)
            
            # 提取最新波动率数据
            latest_vol = {}
            if not vol_data.empty:
                latest = vol_data.iloc[-1]
                for col in vol_data.columns:
                    latest_vol[col] = latest[col]
            
            # 合并统计信息
            result = {
                'latest_volatility': latest_vol,
                'volatility_stats': vol_stats
            }
            
            return result
            
        except Exception as e:
            logger.error(f"计算波动率分析失败: {e}")
            return {}
    
    def _analyze_option_sentiment(self, option_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        分析期权情绪
        
        Args:
            option_data: 期权数据
            
        Returns:
            期权情绪分析结果
        """
        try:
            sentiment_analysis = analyze_option_sentiment(
                put_volume=option_data.get('put_volume', 0),
                call_volume=option_data.get('call_volume', 0),
                put_oi=option_data.get('put_oi'),
                call_oi=option_data.get('call_oi'),
                option_data=option_data.get('option_df'),
                iv_data=option_data.get('iv_data'),
                pcr_history=option_data.get('pcr_history')
            )
            
            return sentiment_analysis
            
        except Exception as e:
            logger.error(f"分析期权情绪失败: {e}")
            return None
    
    def _generate_trading_signals(self, symbol: str, price_data: pd.DataFrame,
                                option_data: Optional[Dict[str, Any]] = None,
                                vix_data: Optional[pd.DataFrame] = None) -> List[TradingSignal]:
        """
        生成交易信号
        
        Args:
            symbol: 标的代码
            price_data: 价格数据
            option_data: 期权数据
            vix_data: VIX数据
            
        Returns:
            交易信号列表
        """
        try:
            signals = generate_trading_signals(
                symbol=symbol,
                price_data=price_data,
                option_data=option_data,
                vix_data=vix_data
            )
            
            return signals
            
        except Exception as e:
            logger.error(f"生成交易信号失败 ({symbol}): {e}")
            return []
    
    def _need_ai_confirmation(self, signals: List[TradingSignal]) -> bool:
        """
        判断是否需要AI确认
        
        Args:
            signals: 交易信号列表
            
        Returns:
            是否需要AI确认
        """
        for signal in signals:
            if signal.level.value >= self.ai_confirmation_threshold:
                return True
        return False
    
    async def _get_ai_confirmation(self, symbol: str, signals: List[TradingSignal],
                                 market_data: Dict[str, Any], 
                                 technical_indicators: Dict[str, Any],
                                 volatility_analysis: Dict[str, Any],
                                 option_sentiment: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        获取AI确认
        
        Args:
            symbol: 标的代码
            signals: 交易信号列表
            market_data: 市场数据
            technical_indicators: 技术指标
            volatility_analysis: 波动率分析
            option_sentiment: 期权情绪
            
        Returns:
            AI确认结果
        """
        try:
            # 构建AI分析请求
            request = AIAnalysisRequest(
                symbol=symbol,
                analysis_type='signal_confirmation',
                market_data={
                    'price_info': self._extract_price_info(market_data.get('price_data')),
                    'trend_info': self._extract_trend_info(technical_indicators)
                },
                technical_indicators={
                    'ema': self._extract_ema_info(technical_indicators),
                    'kdj': self._extract_kdj_info(technical_indicators),
                    'macd': self._extract_macd_info(technical_indicators)
                },
                volatility_analysis=volatility_analysis.get('volatility_stats', {}),
                option_sentiment=option_sentiment,
                current_signals=[asdict(signal) for signal in signals],
                context=f"请确认 {len(signals)} 个交易信号"
            )
            
            # 执行AI分析
            response = self.ai_client.analyze_market_data(request)
            
            if response.success:
                return {
                    'confirmed': response.confidence_score > 70,
                    'confidence': response.confidence_score,
                    'ai_assessment': response.analysis_result.get('market_assessment', 'neutral'),
                    'ai_recommendation': response.analysis_result.get('signal_confirmation', 'hold'),
                    'risk_level': response.risk_assessment.get('level', 'medium'),
                    'key_factors': response.key_factors,
                    'recommendations': response.recommendations,
                    'timestamp': response.timestamp
                }
            else:
                return {
                    'confirmed': False,
                    'confidence': 0.0,
                    'error': response.error_message,
                    'timestamp': datetime.now()
                }
                
        except Exception as e:
            logger.error(f"AI确认失败 ({symbol}): {e}")
            return {
                'confirmed': False,
                'confidence': 0.0,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def _extract_price_info(self, price_data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """
        提取价格信息
        
        Args:
            price_data: 价格数据
            
        Returns:
            价格信息字典
        """
        if price_data is None or price_data.empty:
            return {}
        
        latest = price_data.iloc[-1]
        prev = price_data.iloc[-2] if len(price_data) > 1 else latest
        
        current_price = latest.get('close', 0)
        prev_price = prev.get('close', current_price)
        change_pct = ((current_price - prev_price) / prev_price * 100) if prev_price != 0 else 0
        
        return {
            'current_price': current_price,
            'change_pct': change_pct,
            'volume': latest.get('volume', 0),
            'high': latest.get('high', current_price),
            'low': latest.get('low', current_price)
        }
    
    def _extract_trend_info(self, technical_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取趋势信息
        
        Args:
            technical_indicators: 技术指标
            
        Returns:
            趋势信息字典
        """
        ema8 = technical_indicators.get('EMA8', 0)
        ema21 = technical_indicators.get('EMA21', 0)
        ema55 = technical_indicators.get('EMA55', 0)
        
        # 简单趋势判断
        if ema8 > ema21 > ema55:
            short_term = '上涨'
            medium_term = '上涨'
        elif ema8 < ema21 < ema55:
            short_term = '下跌'
            medium_term = '下跌'
        elif ema8 > ema21:
            short_term = '上涨'
            medium_term = '震荡'
        elif ema8 < ema21:
            short_term = '下跌'
            medium_term = '震荡'
        else:
            short_term = '震荡'
            medium_term = '震荡'
        
        return {
            'short_term': short_term,
            'medium_term': medium_term,
            'long_term': '震荡'  # 需要更复杂的逻辑
        }
    
    def _extract_ema_info(self, technical_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取EMA信息
        
        Args:
            technical_indicators: 技术指标
            
        Returns:
            EMA信息字典
        """
        return {
            'ema8': technical_indicators.get('EMA8', 0),
            'ema21': technical_indicators.get('EMA21', 0),
            'ema55': technical_indicators.get('EMA55', 0)
        }
    
    def _extract_kdj_info(self, technical_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取KDJ信息
        
        Args:
            technical_indicators: 技术指标
            
        Returns:
            KDJ信息字典
        """
        return {
            'k': technical_indicators.get('KDJ_K', 50),
            'd': technical_indicators.get('KDJ_D', 50),
            'j': technical_indicators.get('KDJ_J', 50)
        }
    
    def _extract_macd_info(self, technical_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取MACD信息
        
        Args:
            technical_indicators: 技术指标
            
        Returns:
            MACD信息字典
        """
        return {
            'macd': technical_indicators.get('MACD', 0),
            'signal': technical_indicators.get('MACD_SIGNAL', 0),
            'histogram': technical_indicators.get('MACD_HIST', 0)
        }
    
    def _assess_risk(self, market_data: Dict[str, Any], 
                    technical_indicators: Dict[str, Any],
                    volatility_analysis: Dict[str, Any],
                    signals: List[TradingSignal]) -> Dict[str, Any]:
        """
        评估风险
        
        Args:
            market_data: 市场数据
            technical_indicators: 技术指标
            volatility_analysis: 波动率分析
            signals: 交易信号
            
        Returns:
            风险评估字典
        """
        try:
            risk_factors = []
            risk_score = 50  # 基础风险分数
            
            # 波动率风险
            vol_stats = volatility_analysis.get('volatility_stats', {})
            vix_stats = vol_stats.get('vix_stats', {})
            vix_regime = vix_stats.get('vix_regime', 'medium')
            
            if vix_regime == 'high':
                risk_score += 20
                risk_factors.append('高波动率环境')
            elif vix_regime == 'low':
                risk_score -= 10
                risk_factors.append('低波动率环境')
            
            # 技术面风险
            kdj_k = technical_indicators.get('KDJ_K', 50)
            if kdj_k > 80:
                risk_score += 10
                risk_factors.append('技术指标超买')
            elif kdj_k < 20:
                risk_score += 10
                risk_factors.append('技术指标超卖')
            
            # 信号风险
            high_level_signals = [s for s in signals if s.level.value >= 4]
            if len(high_level_signals) > 2:
                risk_score += 15
                risk_factors.append('多个高等级信号')
            
            # 确定风险等级
            if risk_score >= 75:
                risk_level = 'high'
            elif risk_score >= 55:
                risk_level = 'medium'
            else:
                risk_level = 'low'
            
            return {
                'level': risk_level,
                'score': risk_score,
                'factors': risk_factors,
                'assessment_time': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            return {
                'level': 'unknown',
                'score': 50,
                'factors': ['评估失败'],
                'assessment_time': datetime.now()
            }
    
    def _generate_notifications(self, symbol: str, signals: List[TradingSignal],
                              ai_confirmation: Optional[Dict[str, Any]],
                              risk_assessment: Dict[str, Any]) -> List[str]:
        """
        生成通知
        
        Args:
            symbol: 标的代码
            signals: 交易信号
            ai_confirmation: AI确认结果
            risk_assessment: 风险评估
            
        Returns:
            通知列表
        """
        notifications = []
        
        try:
            # 信号通知
            for signal in signals:
                if signal.level.value >= 3:  # 中等以上信号
                    notification = f"{symbol}: {signal.signal_type.value} 信号 (等级{signal.level.value}, 置信度{signal.confidence:.1f}%)"
                    notifications.append(notification)
            
            # AI确认通知
            if ai_confirmation and ai_confirmation.get('confirmed'):
                notification = f"{symbol}: AI确认信号 (置信度{ai_confirmation.get('confidence', 0):.1f}%)"
                notifications.append(notification)
            
            # 风险通知
            risk_level = risk_assessment.get('level', 'unknown')
            if risk_level == 'high':
                notification = f"{symbol}: 高风险警告 - {', '.join(risk_assessment.get('factors', []))}"
                notifications.append(notification)
            
        except Exception as e:
            logger.error(f"生成通知失败: {e}")
            notifications.append(f"{symbol}: 通知生成失败")
        
        return notifications
    
    async def _store_monitoring_data(self, symbol: str, market_data: Dict[str, Any],
                                   technical_indicators: Dict[str, Any],
                                   volatility_analysis: Dict[str, Any],
                                   option_sentiment: Optional[Dict[str, Any]],
                                   signals: List[TradingSignal]):
        """
        存储监控数据
        
        Args:
            symbol: 标的代码
            market_data: 市场数据
            technical_indicators: 技术指标
            volatility_analysis: 波动率分析
            option_sentiment: 期权情绪
            signals: 交易信号
        """
        try:
            # 存储实时数据（使用DataStorageManager的正确方法）
            if 'realtime_data' in market_data:
                # 直接存储MarketData对象，无需额外导入检查
                self.data_storage.store_market_data(market_data['realtime_data'])
            
            # 存储历史数据
            if 'price_data' in market_data:
                self.data_storage.store_historical_data(
                    symbol, market_data['price_data'], period='1min'
                )
            
            # 技术指标和信号存储（需要实现相应的存储方法或使用数据库直接存储）
            if technical_indicators:
                # 暂时记录到日志，实际项目中需要实现indicators存储
                logger.info(f"技术指标 {symbol}: {list(technical_indicators.keys())}")
            
            # 信号存储
            for signal in signals:
                # 暂时记录到日志，实际项目中需要实现signal存储
                logger.info(f"交易信号 {symbol}: {signal.signal_type.value} (等级{signal.level.value})")
            
        except Exception as e:
            logger.error(f"存储监控数据失败 ({symbol}): {e}")
    
    def _is_trading_time(self) -> bool:
        """
        检查是否在交易时间
        
        Returns:
            是否在交易时间
        """
        # 测试模式：忽略交易时间限制
        if self.test_mode:
            logger.info("测试模式：忽略交易时间限制，强制执行监控")
            return True
            
        now = datetime.now()
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        # 检查交易时间（简化版本）
        current_time = now.time()
        
        # 上午交易时间: 9:30-11:30
        morning_start = datetime.strptime('09:30', '%H:%M').time()
        morning_end = datetime.strptime('11:30', '%H:%M').time()
        
        # 下午交易时间: 13:00-15:00
        afternoon_start = datetime.strptime('13:00', '%H:%M').time()
        afternoon_end = datetime.strptime('15:00', '%H:%M').time()
        
        return (morning_start <= current_time <= morning_end or 
                afternoon_start <= current_time <= afternoon_end)
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        获取监控统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'monitoring_stats': self.monitoring_stats.copy(),
            'latest_results': {k: v.symbol for k, v in self.latest_results.items()},
            'signal_count': len(self.signal_history),
            'is_running': self.is_running,
            'symbols_count': len(self.symbols)
        }
    
    def get_latest_signals(self, symbol: Optional[str] = None, 
                          limit: int = 10) -> List[TradingSignal]:
        """
        获取最新信号
        
        Args:
            symbol: 标的代码（可选）
            limit: 返回数量限制
            
        Returns:
            信号列表
        """
        signals = self.signal_history.copy()
        
        if symbol:
            signals = [s for s in signals if s.symbol == symbol]
        
        # 按时间排序
        signals.sort(key=lambda x: x.timestamp, reverse=True)
        
        return signals[:limit]


# 便捷函数
def create_market_monitor(config: Optional[Dict[str, Any]] = None) -> MarketMonitor:
    """
    创建市场监控器
    
    Args:
        config: 配置参数
        
    Returns:
        市场监控器实例
    """
    return MarketMonitor(config)


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_monitor():
        print("测试市场监控器...")
        
        # 创建监控器
        monitor = create_market_monitor()
        
        # 获取统计信息
        stats = monitor.get_monitoring_stats()
        print(f"监控器状态: {stats}")
        
        # 模拟运行一个监控周期（测试用）
        print("\n运行测试监控周期...")
        try:
            await monitor._run_monitoring_cycle()
            print("测试监控周期完成")
        except Exception as e:
            print(f"测试监控周期失败: {e}")
        
        # 获取最新信号
        latest_signals = monitor.get_latest_signals(limit=5)
        print(f"\n最新信号数量: {len(latest_signals)}")
        
        for signal in latest_signals:
            print(f"- {signal.symbol}: {signal.signal_type.value} (等级{signal.level.value})")
    
    # 运行测试
    asyncio.run(test_monitor())