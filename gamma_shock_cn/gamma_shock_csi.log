2025-07-17 10:26:09,935 - WARNING - cache_manager模块未找到，将使用简化的缓存功能
2025-07-17 10:26:09,938 - WARNING - email_sender模块未找到，将使用简化的通知功能
2025-07-17 10:26:09,940 - ERROR - 加载AI导师配置文件失败: [Errno 2] No such file or directory: 'options_trading_prompt.json'
2025-07-17 10:26:09,940 - ERROR - AI导师配置未能加载，程序退出。
2025-07-17 10:27:06,067 - INFO - 数据库初始化完成
2025-07-17 10:27:06,069 - INFO - 缓存管理器初始化完成 - 后端: memory
2025-07-17 10:27:06,093 - INFO - 缓存管理器初始化完成 - 后端: memory
2025-07-17 10:27:06,093 - WARNING - cache_manager模块未找到，将使用简化的缓存功能
2025-07-17 10:27:06,094 - WARNING - email_sender模块未找到，将使用简化的通知功能
2025-07-17 10:27:06,096 - INFO - 成功加载AI导师配置文件: ai/options_trading_prompt.json
2025-07-17 10:27:06,096 - INFO - 运行一次性分析...
2025-07-17 10:27:06,096 - INFO - 开始A股策略定时任务...
2025-07-17 10:27:06,096 - INFO - 已创建A股标的映射: {'zz1000': '399852', 'zz500_etf': '510500', 'cyb_etf': '159915'}
2025-07-17 10:27:06,096 - INFO - 开始分析 zz1000 数据...
2025-07-17 10:27:06,096 - INFO - 从API获取 zz1000 (399852) 的分钟线数据...
2025-07-17 10:27:15,769 - INFO - 已更新 zz1000 的数据缓存。
2025-07-17 10:27:21,489 - WARNING - 获取zz1000历史波动率失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 10:27:23,310 - INFO - 获取到zz1000分时波动率: nan%
2025-07-17 10:27:23,312 - INFO - zz1000 未检测到明确交易信号。
2025-07-17 10:27:23,312 - INFO - 开始分析 zz500_etf 数据...
2025-07-17 10:27:23,312 - INFO - 从API获取 zz500_etf (510500) 的分钟线数据...
2025-07-17 10:27:25,568 - ERROR - 获取 zz500_etf 数据时出错: '510500'
2025-07-17 10:27:25,569 - ERROR - 无法获取 zz500_etf 数据，跳过分析。
2025-07-17 10:27:25,569 - INFO - 开始分析 cyb_etf 数据...
2025-07-17 10:27:25,569 - INFO - 从API获取 cyb_etf (159915) 的分钟线数据...
2025-07-17 10:27:28,963 - INFO - 已更新 cyb_etf 的数据缓存。
2025-07-17 10:27:33,959 - WARNING - 获取cyb_etf历史波动率失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 10:27:35,553 - INFO - 获取到cyb_etf分时波动率: nan%
2025-07-17 10:27:35,553 - INFO - cyb_etf 未检测到明确交易信号。
2025-07-17 10:27:35,554 - INFO - 任务完成，耗时: 29.46秒
2025-07-17 10:27:35,554 - INFO - 一次性分析完成。
