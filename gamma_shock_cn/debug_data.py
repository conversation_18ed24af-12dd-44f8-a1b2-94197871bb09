#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取调试脚本

用于诊断数据获取问题的根本原因
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_akshare_interfaces():
    """测试AKShare接口"""
    print("=== AKShare接口测试 ===")
    
    # 测试标的列表
    test_symbols = {
        '399852': {'name': '中证1000', 'type': 'index', 'ak_symbol': 'sz399852'},
        '510500': {'name': '中证500ETF', 'type': 'etf', 'ak_symbol': 'sh510500'},
        '159915': {'name': '创业板ETF', 'type': 'etf', 'ak_symbol': 'sz159915'}
    }
    
    for symbol, info in test_symbols.items():
        print(f"\n--- 测试 {info['name']} ({symbol}) ---")
        
        try:
            # 测试实时数据接口
            if info['type'] == 'index':
                # 指数数据
                print("尝试获取指数实时数据...")
                df = ak.index_zh_a_hist(
                    symbol=info['ak_symbol'], 
                    period='daily', 
                    start_date='20241219', 
                    end_date='20241219'
                )
                print(f"指数数据获取成功，形状: {df.shape}")
                if not df.empty:
                    print(f"列名: {list(df.columns)}")
                    print(f"最新数据: {df.iloc[-1].to_dict()}")
                    
            else:
                # ETF数据
                print("尝试获取ETF实时数据...")
                df = ak.fund_etf_hist_em(
                    symbol=info['ak_symbol'],
                    period='daily',
                    start_date='20241219',
                    end_date='20241219',
                    adjust=''
                )
                print(f"ETF数据获取成功，形状: {df.shape}")
                if not df.empty:
                    print(f"列名: {list(df.columns)}")
                    print(f"最新数据: {df.iloc[-1].to_dict()}")
                    
        except Exception as e:
            print(f"获取 {info['name']} 数据失败: {e}")
            
            # 尝试备用接口
            try:
                print("尝试备用接口...")
                if info['type'] == 'index':
                    df = ak.stock_zh_index_daily_em(symbol=info['ak_symbol'])
                else:
                    df = ak.fund_etf_hist_em(symbol=info['ak_symbol'], period='daily')
                    
                print(f"备用接口成功，形状: {df.shape}")
                if not df.empty:
                    print(f"列名: {list(df.columns)}")
                    print(f"最新数据: {df.iloc[-1].to_dict()}")
                    
            except Exception as e2:
                print(f"备用接口也失败: {e2}")

def test_historical_data():
    """测试历史数据获取"""
    print("\n=== 历史数据测试 ===")
    
    try:
        # 测试中证1000历史数据
        print("获取中证1000历史数据...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        df = ak.index_zh_a_hist(
            symbol='sz399852',
            period='daily',
            start_date=start_date.strftime('%Y%m%d'),
            end_date=end_date.strftime('%Y%m%d')
        )
        
        print(f"历史数据获取成功，形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print(f"数据范围: {df.index[0]} 到 {df.index[-1]}")
        print(f"最新5条数据:")
        print(df.tail())
        
        return df
        
    except Exception as e:
        print(f"历史数据获取失败: {e}")
        return None

def test_data_processing(df):
    """测试数据处理"""
    if df is None or df.empty:
        print("\n=== 数据处理测试 - 跳过（无数据） ===")
        return
        
    print("\n=== 数据处理测试 ===")
    
    try:
        # 检查必要的列
        required_cols = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"缺少必要列: {missing_cols}")
            print(f"可用列: {list(df.columns)}")
        else:
            print("所有必要列都存在")
            
        # 测试技术指标计算
        print("\n测试技术指标计算...")
        
        # 威廉指标
        high_col = '最高' if '最高' in df.columns else 'high'
        low_col = '最低' if '最低' in df.columns else 'low' 
        close_col = '收盘' if '收盘' in df.columns else 'close'
        
        if all(col in df.columns for col in [high_col, low_col, close_col]):
            # 计算威廉指标基础值
            period = 19
            df['VAR1'] = df[high_col].rolling(window=period, min_periods=1).max()
            df['VAR2'] = df[low_col].rolling(window=period, min_periods=1).min()
            
            denominator = df['VAR1'] - df['VAR2']
            df['WILLIAMS_BASE'] = (df[close_col] - df['VAR2']) / denominator
            
            print(f"威廉指标计算成功，最新值: {df['WILLIAMS_BASE'].iloc[-1]:.4f}")
            
            # 计算ZLS和CZX
            zls_ema = df['WILLIAMS_BASE'].ewm(span=21, adjust=False).mean()
            df['ZLS'] = zls_ema - 0.5
            
            czx_ema = df['WILLIAMS_BASE'].ewm(span=5, adjust=False).mean()
            df['CZX'] = czx_ema - 0.5
            
            print(f"ZLS最新值: {df['ZLS'].iloc[-1]:.4f}")
            print(f"CZX最新值: {df['CZX'].iloc[-1]:.4f}")
            
        else:
            print(f"缺少价格列，无法计算技术指标")
            
    except Exception as e:
        print(f"数据处理失败: {e}")

def main():
    """主函数"""
    print("开始数据获取调试...")
    
    # 测试AKShare接口
    test_akshare_interfaces()
    
    # 测试历史数据
    df = test_historical_data()
    
    # 测试数据处理
    test_data_processing(df)
    
    print("\n调试完成！")

if __name__ == "__main__":
    main()