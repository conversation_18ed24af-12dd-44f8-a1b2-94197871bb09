"""
期权情绪分析模块 - 整合专业期权分析师策略
作者：gamma_shock_cn 策略团队
日期：2025年1月
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
from typing import Dict, Tuple, List, Optional
import os
from .akshare_interface_patch import akshare_patch

logger = logging.getLogger(__name__)

class OptionSentimentAnalyzer:
    """期权情绪分析器 - 基于专业分析师的五大策略特色"""
    
    def __init__(self):
        self.sentiment_data = {}
        self.market_focus_config = self._load_focus_config()
        
    def _load_focus_config(self) -> dict:
        """加载市场焦点配置"""
        try:
            # 修正路径以确保从项目根目录正确加载
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'option_focus_list.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"加载焦点配置失败，使用默认配置: {e}")
            return {
                "focus_etfs": ["中证500ETF", "上证50ETF", "沪深300ETF", "科创50ETF"],
                "volume_thresholds": {
                    "极度活跃": 500000,
                    "活跃": 300000, 
                    "一般": 100000
                }
            }
    
    def fetch_option_data(self) -> Dict:
        """特色1: 多维度市场扫描 - 获取全面的期权数据"""
        sentiment_data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'risk_analysis': None,
            'daily_stats_sse': None,
            'daily_stats_szse': None,
            'zz1000_options': None,
            'data_quality': 0.0
        }
        
        data_sources = [
            ("risk_analysis", self._fetch_risk_analysis),
            ("daily_stats_sse", self._fetch_sse_stats),
            ("daily_stats_szse", self._fetch_szse_stats),
            ("zz1000_options", self._fetch_zz1000_options)
        ]
        
        successful_sources = 0
        for name, fetch_func in data_sources:
            try:
                logger.info(f"获取 {name} 数据...")
                data = fetch_func()

                # --- 修复：对不同返回类型进行不同判断 ---
                is_valid = False
                if name == 'zz1000_options':
                    # 对于返回字典的函数，只检查是否为None
                    if data is not None:
                        is_valid = True
                else:
                    # 对于返回DataFrame的函数，检查是否为None且不为空
                    if data is not None and isinstance(data, pd.DataFrame) and not data.empty:
                        is_valid = True

                if is_valid:
                    if name == 'zz1000_options':
                        sentiment_data[name] = data  # type: ignore
                    else:
                        sentiment_data[name] = data  # type: ignore
                    successful_sources += 1
                    # 对DataFrame显示长度，对dict显示键
                    if isinstance(data, pd.DataFrame):
                        log_msg = f"共{len(data)}条记录"
                    elif isinstance(data, dict):
                        log_msg = f"包含键: {list(data.keys())}"
                    else:
                        log_msg = "数据已获取"
                    logger.info(f"成功获取 {name} 数据，{log_msg}")
                else:
                    logger.warning(f"{name} 数据为空或获取失败")
            except Exception as e:
                logger.error(f"获取 {name} 数据失败: {e}")
        
        sentiment_data['data_quality'] = float(successful_sources) / len(data_sources)
        self.sentiment_data = sentiment_data
        return sentiment_data
    
    def _fetch_risk_analysis(self) -> Optional[pd.DataFrame]:
        """获取期权风险分析数据"""
        return ak.option_risk_analysis_em()
    
    def _fetch_sse_stats(self) -> Optional[pd.DataFrame]:
        """获取上交所期权日统计 - 使用修复补丁"""
        try:
            # 使用修复补丁获取数据
            current_date = datetime.now().strftime("%Y%m%d")
            data = akshare_patch.get_option_daily_stats_sse_fixed(current_date)
            
            if data is not None and not data.empty:
                logger.info(f"成功获取上交所期权日统计数据 (使用修复补丁)，共{len(data)}条记录")
                logger.info(f"上交所期权数据列名: {data.columns.tolist()}")
                
                # 验证数据质量
                if self._validate_option_data(data, 'sse'):
                    return data
                else:
                    logger.warning(f"上交所期权数据质量验证失败 (修复补丁)")
            else:
                logger.warning(f"上交所期权数据为空 (修复补丁)")
            
            logger.error("无法获取任何有效的上交所期权日统计数据")
            return None
            
        except Exception as e:
            logger.error(f"获取上交所期权日统计失败: {e}")
            return None
    
    def _fetch_szse_stats(self) -> Optional[pd.DataFrame]:
        """获取深交所期权日统计 - 使用修复补丁"""
        try:
            # 使用修复补丁获取数据
            current_date = datetime.now().strftime("%Y%m%d")
            data = akshare_patch.get_option_daily_stats_szse_fixed(current_date)
            
            if data is not None and not data.empty:
                logger.info(f"成功获取深交所期权日统计数据 (使用修复补丁)，共{len(data)}条记录")
                logger.info(f"深交所期权数据列名: {data.columns.tolist()}")
                
                # 验证数据质量
                if self._validate_option_data(data, 'szse'):
                    return data
                else:
                    logger.warning(f"深交所期权数据质量验证失败 (修复补丁)")
            else:
                logger.warning(f"深交所期权数据为空 (修复补丁)")
            
            logger.error("无法获取任何有效的深交所期权日统计数据")
            return None
            
        except Exception as e:
            logger.error(f"获取深交所期权日统计失败: {e}")
            return None
    
    def _validate_option_data(self, data: pd.DataFrame, exchange: str) -> bool:
        """验证期权数据质量"""
        if data is None or data.empty:
            return False
            
        # 检查必要的列是否存在
        required_columns = ['合约标的名称', '认购成交量', '认沽成交量']
        
        for col in required_columns:
            if col not in data.columns:
                logger.warning(f"{exchange} 期权数据缺少必要列: {col}")
                return False
        
        # 检查数据是否有效 - 添加类型检查
        try:
            call_volume = pd.to_numeric(data['认购成交量'], errors='coerce').sum()
            put_volume = pd.to_numeric(data['认沽成交量'], errors='coerce').sum()
            
            if call_volume == 0 and put_volume == 0:
                logger.warning(f"{exchange} 期权数据成交量为0")
                return False
        except Exception as e:
            logger.warning(f"{exchange} 期权数据成交量验证失败: {e}")
            return False
            
        return True
    
    def _fetch_zz1000_options(self) -> Optional[Dict]:
        """获取中证1000期权现货数据 - 优化版本"""
        try:
            # 获取中证1000期权合约列表
            contract_dict = ak.option_cffex_zz1000_list_sina()
            if not (contract_dict and isinstance(contract_dict, dict)):
                logger.warning("获取中证1000合约列表返回为空或格式不正确")
                return None

            contract_list = next(iter(contract_dict.values()))
            if not contract_list:
                logger.warning("中证1000合约列表为空")
                return None
            
            main_contract_code = contract_list[0]
            logger.info(f"成功提取中证1000主力合约: {main_contract_code}")

            # 获取期权现货数据
            spot_data = ak.option_cffex_zz1000_spot_sina(symbol=main_contract_code)
            
            # 检查数据类型和有效性
            if spot_data is not None:
                if isinstance(spot_data, pd.DataFrame):
                    if not spot_data.empty:
                        logger.info(f"成功获取中证1000期权现货数据，共{len(spot_data)}条记录")
                        logger.info(f"中证1000期权数据列名: {spot_data.columns.tolist()}")
                        
                        # 计算沽购比
                        pcr_data = self._calculate_zz1000_pcr(spot_data)
                        
                        return {
                            'main_contract': main_contract_code,
                            'spot_data': spot_data,
                            'pcr_data': pcr_data,
                            'data_timestamp': datetime.now()
                        }
                    else:
                        logger.warning(f"中证1000主力合约 {main_contract_code} 的现货数据为空DataFrame")
                        return None
                elif isinstance(spot_data, dict):
                    logger.warning(f"中证1000期权数据返回字典格式，无法处理: {spot_data}")
                    return None
                else:
                    logger.warning(f"中证1000期权数据返回未知格式: {type(spot_data)}")
                    return None
            else:
                logger.warning(f"中证1000主力合约 {main_contract_code} 的现货数据为None")
                return None

        except Exception as e:
            logger.error(f"获取中证1000期权数据时发生错误: {e}", exc_info=True)
        return None
    
    def _calculate_zz1000_pcr(self, spot_data: pd.DataFrame) -> Dict:
        """计算中证1000期权的沽购比"""
        try:
            if '看涨合约-持仓量' in spot_data.columns and '看跌合约-持仓量' in spot_data.columns:
                # 基于持仓量计算沽购比 - 添加数值转换
                total_call_oi = pd.to_numeric(spot_data['看涨合约-持仓量'], errors='coerce').sum()
                total_put_oi = pd.to_numeric(spot_data['看跌合约-持仓量'], errors='coerce').sum()
                
                if total_call_oi > 0:
                    pcr_ratio = float(total_put_oi / total_call_oi)
                    logger.info(f"中证1000期权沽购比: {pcr_ratio:.4f} (看跌持仓量: {total_put_oi}, 看涨持仓量: {total_call_oi})")
                    
                    return {
                        'pcr_ratio': pcr_ratio,
                        'call_open_interest': float(total_call_oi),
                        'put_open_interest': float(total_put_oi),
                        'calculation_method': 'open_interest'
                    }
                else:
                    logger.warning("看涨合约持仓量为0，无法计算沽购比")
                    return {'pcr_ratio': 1.0, 'calculation_method': 'default'}
            else:
                logger.warning("中证1000期权数据缺少持仓量信息")
                return {'pcr_ratio': 1.0, 'calculation_method': 'default'}
                
        except Exception as e:
            logger.error(f"计算中证1000期权沽购比时出错: {e}")
            return {'pcr_ratio': 1.0, 'calculation_method': 'error'}
    
    def get_comprehensive_pcr_analysis(self) -> Dict:
        """获取综合沽购比分析"""
        pcr_analysis = {
            'zz500_pcr': None,
            'cyb_pcr': None,
            'zz1000_pcr': None,
            'market_wide_pcr': None,
            'pcr_sentiment': '中性',
            'pcr_score': 0.0
        }
        
        try:
            # 1. 分析中证500ETF沽购比（使用持仓量）
            if 'daily_stats_sse' in self.sentiment_data and self.sentiment_data['daily_stats_sse'] is not None:
                sse_data = self.sentiment_data['daily_stats_sse']
                zz500_data = sse_data[sse_data['合约标的名称'].str.contains('500', na=False)]
                
                if not zz500_data.empty:
                    # 使用持仓量计算PCR比率（主要指标）
                    call_oi = pd.to_numeric(zz500_data['未平仓认购合约数'], errors='coerce').sum()
                    put_oi = pd.to_numeric(zz500_data['未平仓认沽合约数'], errors='coerce').sum()
                    
                    # 同时计算成交量比率（短期情绪指标）
                    call_vol = pd.to_numeric(zz500_data['认购成交量'], errors='coerce').sum()
                    put_vol = pd.to_numeric(zz500_data['认沽成交量'], errors='coerce').sum()
                    
                    if call_oi > 0:
                        pcr_analysis['zz500_pcr'] = float(put_oi / call_oi)  # 使用持仓量
                        volume_pcr = float(put_vol / call_vol) if call_vol > 0 else 0
                        logger.info(f"中证500ETF持仓沽购比: {pcr_analysis['zz500_pcr']:.4f}, 成交量沽购比: {volume_pcr:.4f}")
            
            # 2. 分析创业板ETF沽购比（使用持仓比）
            if 'daily_stats_szse' in self.sentiment_data and self.sentiment_data['daily_stats_szse'] is not None:
                szse_data = self.sentiment_data['daily_stats_szse']
                cyb_data = szse_data[szse_data['合约标的名称'].str.contains('创业板', na=False)]
                
                if not cyb_data.empty:
                    # 使用持仓比计算PCR比率（主要指标）
                    position_pcr_percent = pd.to_numeric(cyb_data['认沽/认购持仓比'], errors='coerce').fillna(0).sum()
                    
                    # 同时计算成交量比率（短期情绪指标）
                    call_vol = pd.to_numeric(cyb_data['认购成交量'], errors='coerce').sum()
                    put_vol = pd.to_numeric(cyb_data['认沽成交量'], errors='coerce').sum()
                    
                    if position_pcr_percent > 0:
                        pcr_analysis['cyb_pcr'] = float(position_pcr_percent / 100)  # 转换为比率
                        volume_pcr = float(put_vol / call_vol) if call_vol > 0 else 0
                        logger.info(f"创业板ETF持仓沽购比: {pcr_analysis['cyb_pcr']:.4f}, 成交量沽购比: {volume_pcr:.4f}")
            
            # 3. 分析中证1000沽购比
            if 'zz1000_options' in self.sentiment_data and self.sentiment_data['zz1000_options'] is not None:
                zz1000_data = self.sentiment_data['zz1000_options']
                if 'pcr_data' in zz1000_data:
                    pcr_ratio = zz1000_data['pcr_data'].get('pcr_ratio')
                    if pcr_ratio is not None:
                        pcr_analysis['zz1000_pcr'] = float(pcr_ratio)
                        logger.info(f"中证1000沽购比: {pcr_analysis['zz1000_pcr']:.4f}")
            
            # 4. 计算市场整体沽购比
            pcr_values = [pcr for pcr in [pcr_analysis['zz500_pcr'], pcr_analysis['cyb_pcr'], pcr_analysis['zz1000_pcr']] if pcr is not None and isinstance(pcr, (int, float))]
            
            if pcr_values:
                pcr_analysis['market_wide_pcr'] = sum(pcr_values) / len(pcr_values)
                
                # 5. 评估沽购比情绪
                avg_pcr = pcr_analysis['market_wide_pcr']
                if avg_pcr is not None and isinstance(avg_pcr, (int, float)):
                    if avg_pcr < 0.6:
                        pcr_analysis['pcr_sentiment'] = '极度乐观'
                        pcr_analysis['pcr_score'] = 4.0
                    elif avg_pcr < 0.8:
                        pcr_analysis['pcr_sentiment'] = '乐观'
                        pcr_analysis['pcr_score'] = 2.0
                    elif avg_pcr < 1.2:
                        pcr_analysis['pcr_sentiment'] = '中性'
                        pcr_analysis['pcr_score'] = 0.0
                    elif avg_pcr < 1.5:
                        pcr_analysis['pcr_sentiment'] = '谨慎'
                        pcr_analysis['pcr_score'] = -2.0
                    else:
                        pcr_analysis['pcr_sentiment'] = '恐慌'
                        pcr_analysis['pcr_score'] = -4.0
                    
                    logger.info(f"市场整体沽购比: {avg_pcr:.4f}, 情绪: {pcr_analysis['pcr_sentiment']}, 评分: {pcr_analysis['pcr_score']}")
                else:
                    logger.warning("未获取到任何有效的沽购比数据")

        except Exception as e:
            logger.error(f"综合沽购比分析失败: {e}")
        
        return pcr_analysis
    
    def market_focus_scan(self) -> Dict:
        """特色1: 多维度市场扫描与焦点选择"""
        if 'daily_stats_sse' not in self.sentiment_data or self.sentiment_data['daily_stats_sse'] is None:
            return {'focus_target': '数据不足', 'focus_score': 0}
        
        sse_data = self.sentiment_data['daily_stats_sse']
        focus_analysis = {
            'focus_target': '未知',
            'focus_score': 0,
            'volume_ranking': [],
            'volume_percentile': 0
        }
        
        try:
            if '总成交量' in sse_data.columns and '合约标的名称' in sse_data.columns:
                volume_ranking = sse_data.nlargest(5, '总成交量')[['合约标的名称', '总成交量']]
                focus_analysis['volume_ranking'] = volume_ranking.to_dict('records')
                
                max_volume_idx = sse_data['总成交量'].idxmax()
                focus_target = sse_data.loc[max_volume_idx, '合约标的名称']
                max_volume = sse_data.loc[max_volume_idx, '总成交量']
                
                focus_analysis['focus_target'] = focus_target
                
                total_volume = sse_data['总成交量'].sum()
                focus_volume_ratio = max_volume / total_volume if total_volume > 0 else 0
                
                if focus_volume_ratio > 0.4:
                    focus_analysis['focus_score'] = 10
                elif focus_volume_ratio > 0.3:
                    focus_analysis['focus_score'] = 8
                elif focus_volume_ratio > 0.2:
                    focus_analysis['focus_score'] = 6
                else:
                    focus_analysis['focus_score'] = 3
                
                focus_analysis['volume_percentile'] = focus_volume_ratio * 100
                
                logger.info(f"市场焦点: {focus_target}, 成交量占比: {focus_volume_ratio:.2%}, 评分: {focus_analysis['focus_score']}")
                
        except Exception as e:
            logger.error(f"市场焦点扫描失败: {e}")
        
        return focus_analysis
    
    def emotion_price_verification(self) -> Dict:
        """特色2: '情绪'与'价格'的双重验证"""
        verification = {
            'pcr_ratio': 0.0,
            'emotion_level': '中性',
            'price_momentum': '无明显趋势',
            'verification_score': 0.0
        }
        
        try:
            if 'daily_stats_sse' in self.sentiment_data and self.sentiment_data['daily_stats_sse'] is not None:
                sse_data = self.sentiment_data['daily_stats_sse']
                if '认购成交量' in sse_data.columns and '认沽成交量' in sse_data.columns:
                    total_call = pd.to_numeric(sse_data['认购成交量'], errors='coerce').sum()
                    total_put = pd.to_numeric(sse_data['认沽成交量'], errors='coerce').sum()
                    
                    if total_call > 0:
                        pcr = float(total_put / total_call)
                        verification['pcr_ratio'] = pcr
                        
                        # 重置评分
                        verification['verification_score'] = 0.0 
                        
                        # PCR情绪分级
                        if pcr < 0.6:
                            verification['emotion_level'] = '极度乐观'
                            verification['verification_score'] = 4.0
                        elif pcr < 0.8:
                            verification['emotion_level'] = '乐观'
                            verification['verification_score'] = 3.0
                        elif pcr < 1.2:
                            verification['emotion_level'] = '中性'
                            verification['verification_score'] = 1.0
                        elif pcr < 1.5:
                            verification['emotion_level'] = '谨慎'
                            verification['verification_score'] = -1.0
                        else:
                            verification['emotion_level'] = '恐慌'
                            verification['verification_score'] = -2.0
            
            verification['price_momentum'] = '待主策略确认'
            
            logger.info(f"情绪验证: PCR={verification['pcr_ratio']:.3f}, 情绪={verification['emotion_level']}, 评分={verification['verification_score']}")
            
        except Exception as e:
            logger.error(f"情绪价格验证失败: {e}")
        
        return verification
    
    def volatility_analysis(self) -> Dict:
        """特色3: 精细化的期权波动率分析"""
        volatility = {
            'iv_level': '中等',
            'iv_percentile': 50.0,
            'iv_skew': 0.0,
            'term_structure': '正常',
            'volatility_score': 0.0
        }
        
        try:
            if 'risk_analysis' in self.sentiment_data and self.sentiment_data['risk_analysis'] is not None:
                risk_data = self.sentiment_data['risk_analysis']
                
                if 'Vega' in risk_data.columns:
                    # 使用均值和标准差来评估波动率
                    avg_vega = pd.to_numeric(risk_data['Vega'], errors='coerce').mean()
                    
                    volatility['volatility_score'] = 0.0
                    if avg_vega > 0.5:
                        volatility['iv_level'] = '高波动率'
                        volatility['volatility_score'] = 3.0
                    elif avg_vega > 0.2:
                        volatility['iv_level'] = '中等波动率'
                        volatility['volatility_score'] = 1.0
                    else:
                        volatility['iv_level'] = '低波动率'
                        volatility['volatility_score'] = -1.0
                    
                    # 近似计算波动率分位数
                    volatility['iv_percentile'] = min(100.0, max(0.0, (avg_vega / 1.0) * 100))
                
                # 分析波动率偏斜
                if 'Delta' in risk_data.columns:
                    # 确保类型正确
                    risk_data['Delta'] = pd.to_numeric(risk_data['Delta'], errors='coerce')
                    risk_data['Vega'] = pd.to_numeric(risk_data['Vega'], errors='coerce')
                    
                    call_options = risk_data[risk_data['Delta'] > 0]
                    put_options = risk_data[risk_data['Delta'] < 0]
                    
                    if not call_options.empty and not put_options.empty:
                        avg_call_vega = call_options['Vega'].mean()
                        avg_put_vega = put_options['Vega'].mean()
                        if pd.notna(avg_put_vega) and pd.notna(avg_call_vega):
                                volatility['iv_skew'] = float(avg_put_vega) - float(avg_call_vega)
            
            logger.info(f"波动率分析: IV水平={volatility['iv_level']}, 分位数={volatility['iv_percentile']:.1f}%, 评分={volatility['volatility_score']}")
            
        except Exception as e:
            logger.error(f"波动率分析失败: {e}")
        
        return volatility
    
    def time_cycle_analysis(self) -> Dict:
        """特色4: 动态的时间周期与关键位分析"""
        return {
            'cycle_status': '待价格数据确认',
            'key_levels': [],
            'cycle_score': 0
        }
    
    def scenario_strategy(self, market_data: Optional[Dict] = None) -> Dict:
        """特色5: 基于情景的应对策略"""
        focus_analysis = self.market_focus_scan()
        emotion_verification = self.emotion_price_verification()
        volatility_analysis = self.volatility_analysis()
        
        total_score = (
            float(focus_analysis.get('focus_score', 0)) * 0.3 +
            float(emotion_verification.get('verification_score', 0)) * 0.4 +
            float(volatility_analysis.get('volatility_score', 0)) * 0.3
        )
        
        strategy = {
            'total_score': total_score,
            'signal_strength': '无信号',
            'recommended_action': '观察等待',
            'confidence_level': 0,
            'risk_level': '中等'
        }
        
        if total_score >= 8:
            strategy['signal_strength'] = '强信号'
            strategy['recommended_action'] = '积极开仓'
            strategy['confidence_level'] = 85
        elif total_score >= 5:
            strategy['signal_strength'] = '中等信号'
            strategy['recommended_action'] = '小仓位试探'
            strategy['confidence_level'] = 65
        elif total_score >= 2:
            strategy['signal_strength'] = '弱信号'
            strategy['recommended_action'] = '密切观察'
            strategy['confidence_level'] = 40
        
        if emotion_verification.get('emotion_level') in ['极度乐观', '恐慌'] or volatility_analysis.get('iv_level') == '高波动率':
            strategy['risk_level'] = '高风险'
        
        logger.info(f"策略建议: 总分={total_score:.1f}, 信号强度={strategy['signal_strength']}, 建议={strategy['recommended_action']}")
        
        return {
            'focus_analysis': focus_analysis,
            'emotion_verification': emotion_verification,
            'volatility_analysis': volatility_analysis,
            'strategy_recommendation': strategy
        }
    
    def get_cyb_focus_boost(self) -> int:
        """检查创业板是否为当前市场焦点，返回加成分数"""
        focus_analysis = self.market_focus_scan()
        focus_target = focus_analysis.get('focus_target', '')
        cyb_keywords = ['创业板', 'ETF', '399006', '159915']
        
        if any(keyword in focus_target for keyword in cyb_keywords):
            logger.info(f"创业板成为市场焦点: {focus_target}")
            # 从字典中安全地获取评分
            score = focus_analysis.get('focus_score', 0)
            return min(3, int(score) // 3)
        return 0
    
    def get_sentiment_boost(self) -> int:
        """获取整体市场情绪加成分数"""
        emotion_verification = self.emotion_price_verification()
        emotion_level = emotion_verification.get('emotion_level', '中性')
        verification_score = float(emotion_verification.get('verification_score', 0))
        
        if emotion_level in ['极度乐观', '乐观'] and verification_score > 2.0:
            return 2
        elif emotion_level == '乐观' or verification_score > 0.0:
            return 1
        return 0
    
    def fetch_all_data(self) -> Dict:
        """获取所有期权数据 - 兼容性方法"""
        logger.info("开始获取所有期权数据...")
        return self.fetch_option_data()
    
    def get_comprehensive_sentiment_analysis(self) -> Dict:
        """获取综合情绪分析 - 兼容性方法"""
        logger.info("开始综合情绪分析...")
        
        # 首先获取数据
        if not self.sentiment_data:
            self.fetch_option_data()
        
        # 执行各项分析
        pcr_analysis = self.get_comprehensive_pcr_analysis()
        focus_analysis = self.market_focus_scan()
        emotion_verification = self.emotion_price_verification()
        volatility_analysis = self.volatility_analysis()
        scenario_strategy = self.scenario_strategy()
        
        # 综合分析结果
        comprehensive_analysis = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_quality': self.sentiment_data.get('data_quality', 0.0),
            'pcr_analysis': pcr_analysis,
            'focus_analysis': focus_analysis,
            'emotion_verification': emotion_verification,
            'volatility_analysis': volatility_analysis,
            'scenario_strategy': scenario_strategy,
            'overall_sentiment': self._calculate_overall_sentiment(pcr_analysis, emotion_verification, volatility_analysis),
            'market_summary': self._generate_market_summary(pcr_analysis, focus_analysis, emotion_verification)
        }
        
        logger.info(f"综合情绪分析完成，整体情绪: {comprehensive_analysis['overall_sentiment']}")
        return comprehensive_analysis
    
    def _calculate_overall_sentiment(self, pcr_analysis: Dict, emotion_verification: Dict, volatility_analysis: Dict) -> str:
        """计算整体市场情绪"""
        try:
            # 综合各项指标评分
            pcr_score = pcr_analysis.get('pcr_score', 0.0)
            emotion_score = emotion_verification.get('verification_score', 0.0)
            volatility_score = volatility_analysis.get('volatility_score', 0.0)
            
            # 加权平均
            overall_score = (pcr_score * 0.4 + emotion_score * 0.4 + volatility_score * 0.2)
            
            if overall_score >= 3.0:
                return '极度乐观'
            elif overall_score >= 1.5:
                return '乐观'
            elif overall_score >= -1.5:
                return '中性'
            elif overall_score >= -3.0:
                return '谨慎'
            else:
                return '恐慌'
        except Exception as e:
            logger.error(f"计算整体情绪失败: {e}")
            return '中性'
    
    def _generate_market_summary(self, pcr_analysis: Dict, focus_analysis: Dict, emotion_verification: Dict) -> str:
        """生成市场摘要"""
        try:
            focus_target = focus_analysis.get('focus_target', '未知')
            pcr_sentiment = pcr_analysis.get('pcr_sentiment', '中性')
            emotion_level = emotion_verification.get('emotion_level', '中性')
            
            summary = f"市场焦点: {focus_target}，沽购比情绪: {pcr_sentiment}，整体情绪: {emotion_level}"
            return summary
        except Exception as e:
            logger.error(f"生成市场摘要失败: {e}")
            return "市场数据分析中..."