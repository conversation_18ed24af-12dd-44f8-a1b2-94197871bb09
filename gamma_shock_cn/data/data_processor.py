#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 数据处理模块

本模块负责数据的标准化、验证、清洗和转换，包括：
- 数据格式标准化
- 数据质量验证
- 数据重采样和聚合
- 技术指标预处理
- 异常数据检测和处理

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime as dt, timedelta
import logging
from dataclasses import dataclass
import warnings

# 可选依赖处理
try:
    from scipy import stats
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    stats = None

try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.cluster import DBSCAN
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    StandardScaler = None
    MinMaxScaler = None
    DBSCAN = None

# 导入配置
from config.settings import settings


@dataclass
class DataQualityReport:
    """数据质量报告"""
    total_rows: int
    valid_rows: int
    invalid_rows: int
    missing_data_pct: float
    outlier_count: int
    quality_score: float
    issues: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'total_rows': self.total_rows,
            'valid_rows': self.valid_rows,
            'invalid_rows': self.invalid_rows,
            'missing_data_pct': self.missing_data_pct,
            'outlier_count': self.outlier_count,
            'quality_score': self.quality_score,
            'issues': self.issues
        }


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 数据质量阈值
        self.quality_thresholds = {
            'max_missing_pct': 10.0,  # 最大缺失数据百分比
            'max_outlier_pct': 5.0,   # 最大异常值百分比
            'min_quality_score': 80.0  # 最小质量分数
        }
        
        # 价格异常检测参数
        self.price_anomaly_params = {
            'max_daily_change_pct': 20.0,  # 最大日涨跌幅
            'max_gap_pct': 15.0,           # 最大跳空幅度
            'min_price': 0.01,             # 最小价格
            'max_price_ratio': 10.0        # 最大价格比率（高/低）
        }
    
    def standardize_data(self, df: pd.DataFrame, symbol: str = None) -> pd.DataFrame:
        """
        标准化数据格式
        
        Args:
            df: 原始数据DataFrame
            symbol: 标的代码
            
        Returns:
            标准化后的DataFrame
        """
        try:
            if df is None or df.empty:
                self.logger.warning("输入数据为空")
                return pd.DataFrame()
            
            # 复制数据避免修改原始数据
            df = df.copy()
            
            # 标准化列名
            df = self._standardize_columns(df)
            
            # 确保必要的列存在
            df = self._ensure_required_columns(df)
            
            # 数据类型转换
            df = self._convert_data_types(df)
            
            # 处理时间索引
            df = self._process_datetime_index(df)
            
            # 添加标的代码
            if symbol:
                df['symbol'] = symbol
            
            # 排序数据
            if isinstance(df.index, pd.DatetimeIndex):
                df = df.sort_index()
            
            self.logger.debug(f"数据标准化完成，处理 {len(df)} 行数据")
            return df
            
        except Exception as e:
            self.logger.error(f"数据标准化失败: {e}")
            raise
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        # 列名映射表（支持中英文）
        column_mapping = {
            # 中文列名
            '时间': 'datetime',
            '日期': 'datetime',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount',
            '涨跌额': 'change',
            '涨跌幅': 'change_pct',
            # 英文列名
            'Date': 'datetime',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'Amount': 'amount',
            'Change': 'change',
            'Change_pct': 'change_pct',
            # 其他可能的列名
            'price': 'close',
            'vol': 'volume',
            'amt': 'amount'
        }
        
        # 重命名列
        df = df.rename(columns=column_mapping)
        
        # 转换为小写（如果还有其他列名）
        df.columns = df.columns.str.lower()
        
        return df
    
    def _ensure_required_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """确保必要的列存在"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        for col in required_columns:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = 0
                    self.logger.warning(f"缺少成交量列，已设置为0")
                elif col in ['open', 'high', 'low'] and 'close' in df.columns:
                    # 如果只有收盘价，用收盘价填充其他价格
                    df[col] = df['close']
                    self.logger.warning(f"缺少{col}列，已用收盘价填充")
                else:
                    raise ValueError(f"缺少必要列: {col}")
        
        # 确保可选列存在
        optional_columns = {'amount': 0.0, 'change': 0.0, 'change_pct': 0.0}
        for col, default_value in optional_columns.items():
            if col not in df.columns:
                df[col] = default_value
        
        return df
    
    def _convert_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """转换数据类型"""
        # 数值列
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'change', 'change_pct']
        
        for col in numeric_columns:
            if col in df.columns:
                # 处理特殊值
                df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                # 转换为数值类型
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 整数列
        integer_columns = ['volume']
        for col in integer_columns:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype('int64')
        
        return df
    
    def _process_datetime_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理时间索引"""
        # 如果有datetime列，设置为索引
        if 'datetime' in df.columns:
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.set_index('datetime')
        elif not isinstance(df.index, pd.DatetimeIndex):
            # 尝试将索引转换为时间
            try:
                df.index = pd.to_datetime(df.index)
            except Exception:
                self.logger.warning("无法将索引转换为时间格式")
        
        # 确保索引名称
        if isinstance(df.index, pd.DatetimeIndex):
            df.index.name = 'datetime'
        
        return df
    
    def validate_data(self, df: pd.DataFrame, symbol: str = None) -> DataQualityReport:
        """
        验证数据质量
        
        Args:
            df: 数据DataFrame
            symbol: 标的代码
            
        Returns:
            数据质量报告
        """
        try:
            if df is None or df.empty:
                return DataQualityReport(
                    total_rows=0, valid_rows=0, invalid_rows=0,
                    missing_data_pct=100.0, outlier_count=0,
                    quality_score=0.0, issues=['数据为空']
                )
            
            total_rows = len(df)
            issues = []
            
            # 检查缺失数据
            missing_data = df.isnull().sum()
            missing_pct = (missing_data.sum() / (len(df) * len(df.columns))) * 100
            
            if missing_pct > self.quality_thresholds['max_missing_pct']:
                issues.append(f"缺失数据过多: {missing_pct:.1f}%")
            
            # 检查价格逻辑
            price_issues = self._validate_price_logic(df)
            issues.extend(price_issues)
            
            # 检查异常值
            outliers = self._detect_outliers(df)
            outlier_count = len(outliers)
            outlier_pct = (outlier_count / total_rows) * 100
            
            if outlier_pct > self.quality_thresholds['max_outlier_pct']:
                issues.append(f"异常值过多: {outlier_count} 个 ({outlier_pct:.1f}%)")
            
            # 检查数据连续性
            continuity_issues = self._check_data_continuity(df)
            issues.extend(continuity_issues)
            
            # 计算有效行数
            valid_mask = self._get_valid_data_mask(df)
            valid_rows = valid_mask.sum()
            invalid_rows = total_rows - valid_rows
            
            # 计算质量分数
            quality_score = self._calculate_quality_score(
                missing_pct, outlier_pct, len(issues)
            )
            
            report = DataQualityReport(
                total_rows=total_rows,
                valid_rows=valid_rows,
                invalid_rows=invalid_rows,
                missing_data_pct=missing_pct,
                outlier_count=outlier_count,
                quality_score=quality_score,
                issues=issues
            )
            
            self.logger.info(f"数据质量验证完成 - 质量分数: {quality_score:.1f}")
            return report
            
        except Exception as e:
            self.logger.error(f"数据质量验证失败: {e}")
            return DataQualityReport(
                total_rows=0, valid_rows=0, invalid_rows=0,
                missing_data_pct=100.0, outlier_count=0,
                quality_score=0.0, issues=[f'验证失败: {str(e)}']
            )
    
    def _validate_price_logic(self, df: pd.DataFrame) -> List[str]:
        """验证价格逻辑"""
        issues = []
        
        if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            return issues
        
        # 检查价格关系
        invalid_high_low = (df['high'] < df['low']).sum()
        if invalid_high_low > 0:
            issues.append(f"最高价小于最低价: {invalid_high_low} 行")
        
        invalid_high_open = (df['high'] < df['open']).sum()
        if invalid_high_open > 0:
            issues.append(f"最高价小于开盘价: {invalid_high_open} 行")
        
        invalid_high_close = (df['high'] < df['close']).sum()
        if invalid_high_close > 0:
            issues.append(f"最高价小于收盘价: {invalid_high_close} 行")
        
        invalid_low_open = (df['low'] > df['open']).sum()
        if invalid_low_open > 0:
            issues.append(f"最低价大于开盘价: {invalid_low_open} 行")
        
        invalid_low_close = (df['low'] > df['close']).sum()
        if invalid_low_close > 0:
            issues.append(f"最低价大于收盘价: {invalid_low_close} 行")
        
        # 检查价格范围
        zero_prices = (df[['open', 'high', 'low', 'close']] <= 0).any(axis=1).sum()
        if zero_prices > 0:
            issues.append(f"价格为零或负数: {zero_prices} 行")
        
        # 检查异常价格比率
        price_ratio = df['high'] / df['low']
        extreme_ratios = (price_ratio > self.price_anomaly_params['max_price_ratio']).sum()
        if extreme_ratios > 0:
            issues.append(f"价格比率异常: {extreme_ratios} 行")
        
        return issues
    
    def _detect_outliers(self, df: pd.DataFrame) -> pd.Index:
        """检测异常值"""
        outliers = pd.Index([])
        
        if 'change_pct' in df.columns:
            # 使用涨跌幅检测异常值
            extreme_changes = abs(df['change_pct']) > self.price_anomaly_params['max_daily_change_pct']
            outliers = outliers.union(df[extreme_changes].index)
        
        # 使用统计方法检测异常值（IQR方法）
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns and len(df[col].dropna()) > 10:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 3 * IQR
                upper_bound = Q3 + 3 * IQR
                
                col_outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)].index
                outliers = outliers.union(col_outliers)
        
        return outliers
    
    def _check_data_continuity(self, df: pd.DataFrame) -> List[str]:
        """检查数据连续性"""
        issues = []
        
        if not isinstance(df.index, pd.DatetimeIndex) or len(df) < 2:
            return issues
        
        # 检查时间间隔
        time_diffs = df.index.to_series().diff().dropna()
        
        if len(time_diffs) > 0:
            # 检测异常的时间间隔
            median_diff = time_diffs.median()
            large_gaps = time_diffs > median_diff * 5
            
            if large_gaps.sum() > 0:
                issues.append(f"数据存在较大时间间隔: {large_gaps.sum()} 处")
        
        return issues
    
    def _get_valid_data_mask(self, df: pd.DataFrame) -> pd.Series:
        """获取有效数据掩码"""
        # 基本有效性检查
        valid_mask = pd.Series(True, index=df.index)
        
        # 检查必要列的有效性
        required_columns = ['open', 'high', 'low', 'close']
        for col in required_columns:
            if col in df.columns:
                valid_mask &= df[col].notna()
                valid_mask &= df[col] > 0
        
        # 检查价格逻辑
        if all(col in df.columns for col in required_columns):
            valid_mask &= df['high'] >= df['low']
            valid_mask &= df['high'] >= df['open']
            valid_mask &= df['high'] >= df['close']
            valid_mask &= df['low'] <= df['open']
            valid_mask &= df['low'] <= df['close']
        
        return valid_mask
    
    def _calculate_quality_score(self, missing_pct: float, outlier_pct: float, issue_count: int) -> float:
        """计算质量分数"""
        base_score = 100.0
        
        # 扣分项
        score = base_score
        score -= missing_pct * 2  # 缺失数据扣分
        score -= outlier_pct * 1.5  # 异常值扣分
        score -= issue_count * 5  # 问题数量扣分
        
        return max(0.0, min(100.0, score))
    
    def clean_data(self, df: pd.DataFrame, method: str = 'conservative') -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            df: 原始数据
            method: 清洗方法 ('conservative', 'aggressive', 'interpolate')
            
        Returns:
            清洗后的数据
        """
        try:
            if df is None or df.empty:
                return df
            
            df = df.copy()
            original_length = len(df)
            
            if method == 'conservative':
                # 保守清洗：只删除明显错误的数据
                df = self._conservative_clean(df)
            elif method == 'aggressive':
                # 激进清洗：删除所有可疑数据
                df = self._aggressive_clean(df)
            elif method == 'interpolate':
                # 插值清洗：尽量保留数据，用插值填补
                df = self._interpolate_clean(df)
            else:
                raise ValueError(f"不支持的清洗方法: {method}")
            
            cleaned_length = len(df)
            removed_pct = ((original_length - cleaned_length) / original_length) * 100
            
            self.logger.info(f"数据清洗完成 - 方法: {method}, 删除: {removed_pct:.1f}% ({original_length - cleaned_length}/{original_length})")
            
            return df
            
        except Exception as e:
            self.logger.error(f"数据清洗失败: {e}")
            return df
    
    def _conservative_clean(self, df: pd.DataFrame) -> pd.DataFrame:
        """保守清洗"""
        # 删除明显错误的数据
        valid_mask = self._get_valid_data_mask(df)
        
        # 删除极端异常值（超过20%涨跌幅）
        if 'change_pct' in df.columns:
            valid_mask &= abs(df['change_pct']) <= self.price_anomaly_params['max_daily_change_pct']
        
        return df[valid_mask]
    
    def _aggressive_clean(self, df: pd.DataFrame) -> pd.DataFrame:
        """激进清洗"""
        # 删除所有异常值
        outliers = self._detect_outliers(df)
        df = df.drop(outliers)
        
        # 删除无效数据
        valid_mask = self._get_valid_data_mask(df)
        df = df[valid_mask]
        
        return df
    
    def _interpolate_clean(self, df: pd.DataFrame) -> pd.DataFrame:
        """插值清洗"""
        # 对于缺失值，使用插值填补
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        
        for col in numeric_columns:
            if col in df.columns:
                # 线性插值
                df[col] = df[col].interpolate(method='linear', limit_direction='both')
        
        # 删除仍然无效的数据
        valid_mask = self._get_valid_data_mask(df)
        df = df[valid_mask]
        
        return df
    
    def resample_data(self, df: pd.DataFrame, freq: str, method: str = 'ohlc') -> pd.DataFrame:
        """
        重采样数据
        
        Args:
            df: 原始数据
            freq: 重采样频率 ('5min', '15min', '1H', '1D'等)
            method: 重采样方法 ('ohlc', 'mean', 'last')
            
        Returns:
            重采样后的数据
        """
        try:
            if df is None or df.empty:
                return df
            
            if not isinstance(df.index, pd.DatetimeIndex):
                raise ValueError("数据索引必须是时间格式")
            
            if method == 'ohlc':
                # OHLC重采样
                resampled = df.resample(freq).agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum',
                    'amount': 'sum'
                })
                
                # 计算涨跌幅
                resampled['change'] = resampled['close'] - resampled['close'].shift(1)
                resampled['change_pct'] = (resampled['change'] / resampled['close'].shift(1) * 100).round(2)
                
            elif method == 'mean':
                # 均值重采样
                resampled = df.resample(freq).mean()
                
            elif method == 'last':
                # 最后值重采样
                resampled = df.resample(freq).last()
                
            else:
                raise ValueError(f"不支持的重采样方法: {method}")
            
            # 删除空值行
            resampled = resampled.dropna()
            
            # 保留其他列
            if 'symbol' in df.columns:
                resampled['symbol'] = df['symbol'].iloc[0]
            
            self.logger.debug(f"数据重采样完成 - 频率: {freq}, 方法: {method}, 结果: {len(resampled)} 行")
            
            return resampled
            
        except Exception as e:
            self.logger.error(f"数据重采样失败: {e}")
            return df
    
    def calculate_returns(self, df: pd.DataFrame, periods: List[int] = [1, 5, 10, 20]) -> pd.DataFrame:
        """
        计算收益率
        
        Args:
            df: 价格数据
            periods: 计算周期列表
            
        Returns:
            包含收益率的数据
        """
        try:
            if df is None or df.empty or 'close' not in df.columns:
                return df
            
            df = df.copy()
            
            for period in periods:
                # 简单收益率
                df[f'return_{period}'] = df['close'].pct_change(period) * 100
                
                # 对数收益率
                df[f'log_return_{period}'] = np.log(df['close'] / df['close'].shift(period)) * 100
            
            self.logger.debug(f"收益率计算完成 - 周期: {periods}")
            return df
            
        except Exception as e:
            self.logger.error(f"收益率计算失败: {e}")
            return df
    
    def detect_anomalies(self, df: pd.DataFrame, method: str = 'zscore', threshold: float = 3.0) -> pd.DataFrame:
        """
        检测异常值
        
        Args:
            df: 数据
            method: 检测方法 ('zscore', 'iqr', 'isolation')
            threshold: 阈值
            
        Returns:
            包含异常标记的数据
        """
        try:
            if df is None or df.empty:
                return df
            
            df = df.copy()
            
            if method == 'zscore':
                # Z-score方法
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in df.columns:
                        if SCIPY_AVAILABLE and stats is not None:
                            z_scores = np.abs(stats.zscore(df[col].dropna()))
                            df[f'{col}_anomaly'] = z_scores > threshold
                        else:
                            # 使用简单的标准差方法
                            mean_val = df[col].mean()
                            std_val = df[col].std()
                            anomaly_mask = np.abs(df[col] - mean_val) > (threshold * std_val)
                            df[f'{col}_anomaly'] = anomaly_mask
            
            elif method == 'iqr':
                # IQR方法
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in df.columns:
                        Q1 = df[col].quantile(0.25)
                        Q3 = df[col].quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - threshold * IQR
                        upper_bound = Q3 + threshold * IQR
                        df[f'{col}_anomaly'] = (df[col] < lower_bound) | (df[col] > upper_bound)
            
            # 综合异常标记
            anomaly_cols = [col for col in df.columns if col.endswith('_anomaly')]
            if anomaly_cols:
                df['is_anomaly'] = df[anomaly_cols].any(axis=1)
            
            self.logger.debug(f"异常检测完成 - 方法: {method}, 阈值: {threshold}")
            return df
            
        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
            return df


# 全局数据处理器实例
data_processor = DataProcessor()


if __name__ == '__main__':
    # 测试数据处理器
    import pandas as pd
    from datetime import datetime, timedelta
    
    print("=== 数据处理器测试 ===")
    
    # 创建测试数据
    dates = pd.date_range(start='2024-01-01', end='2024-01-10', freq='1H')
    test_data = pd.DataFrame({
        'datetime': dates,
        'open': np.random.uniform(100, 110, len(dates)),
        'high': np.random.uniform(105, 115, len(dates)),
        'low': np.random.uniform(95, 105, len(dates)),
        'close': np.random.uniform(100, 110, len(dates)),
        'volume': np.random.randint(1000, 10000, len(dates))
    })
    
    # 添加一些异常数据
    test_data.loc[10, 'high'] = test_data.loc[10, 'low'] - 1  # 最高价小于最低价
    test_data.loc[20, 'close'] = 0  # 零价格
    test_data.loc[30:35, 'volume'] = np.nan  # 缺失数据
    
    print(f"\n原始数据: {len(test_data)} 行")
    
    # 测试数据标准化
    print("\n1. 测试数据标准化:")
    standardized_data = data_processor.standardize_data(test_data, symbol='TEST')
    print(f"标准化后: {len(standardized_data)} 行")
    print(f"列名: {list(standardized_data.columns)}")
    
    # 测试数据验证
    print("\n2. 测试数据验证:")
    quality_report = data_processor.validate_data(standardized_data)
    print(f"质量分数: {quality_report.quality_score:.1f}")
    print(f"问题数量: {len(quality_report.issues)}")
    for issue in quality_report.issues:
        print(f"  - {issue}")
    
    # 测试数据清洗
    print("\n3. 测试数据清洗:")
    cleaned_data = data_processor.clean_data(standardized_data, method='conservative')
    print(f"清洗后: {len(cleaned_data)} 行")
    
    # 测试数据重采样
    print("\n4. 测试数据重采样:")
    resampled_data = data_processor.resample_data(cleaned_data, freq='1D')
    print(f"重采样后: {len(resampled_data)} 行")
    
    # 测试收益率计算
    print("\n5. 测试收益率计算:")
    returns_data = data_processor.calculate_returns(resampled_data, periods=[1, 3])
    print(f"收益率列: {[col for col in returns_data.columns if 'return' in col]}")
    
    # 测试异常检测
    print("\n6. 测试异常检测:")
    anomaly_data = data_processor.detect_anomalies(cleaned_data, method='zscore')
    anomaly_count = anomaly_data['is_anomaly'].sum() if 'is_anomaly' in anomaly_data.columns else 0
    print(f"检测到异常: {anomaly_count} 个")
    
    print("\n=== 测试完成 ===")