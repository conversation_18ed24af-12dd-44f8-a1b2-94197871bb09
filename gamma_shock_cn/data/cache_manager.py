#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 缓存管理模块

本模块负责数据缓存的管理，包括：
- 内存缓存管理
- 文件缓存管理
- Redis缓存支持
- 缓存策略和过期管理
- 缓存统计和监控

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import pickle
import json
import os
import time
import hashlib
import threading
from typing import Any, Optional, Dict, List, Union
from pathlib import Path
from datetime import datetime as dt, timedelta
import logging
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
import gzip
import sqlite3

# 导入配置
from config.settings import settings


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_keys: int = 0
    hit_count: int = 0
    miss_count: int = 0
    expired_count: int = 0
    evicted_count: int = 0
    total_size: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hit_count + self.miss_count
        return (self.hit_count / total * 100) if total > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: float
    expires_at: float
    access_count: int = 0
    last_accessed: float = 0.0
    size: int = 0
    
    def __post_init__(self):
        if self.last_accessed == 0.0:
            self.last_accessed = self.created_at
        if self.size == 0:
            self.size = self._calculate_size()
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return time.time() > self.expires_at
    
    def touch(self):
        """更新访问时间"""
        self.last_accessed = time.time()
        self.access_count += 1
    
    def _calculate_size(self) -> int:
        """计算条目大小"""
        try:
            return len(pickle.dumps(self.value))
        except Exception:
            return 0


class CacheBackend(ABC):
    """缓存后端抽象基类"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = 0) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """清空缓存"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass
    
    @abstractmethod
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        pass


class MemoryCache(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300, cleanup_interval: int = 60):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cleanup_interval = cleanup_interval
        
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        self._stats = CacheStats()
        self._last_cleanup = time.time()
        
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self._stats.miss_count += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self._stats.expired_count += 1
                self._stats.miss_count += 1
                return None
            
            # 更新访问信息
            entry.touch()
            self._stats.hit_count += 1
            
            # 按需清理
            self._cleanup_if_needed()
            
            return entry.value
    
    def set(self, key: str, value: Any, ttl: int = 0) -> bool:
        """设置缓存值"""
        try:
            with self._lock:
                ttl = ttl or self.default_ttl
                current_time = time.time()
                
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_at=current_time,
                    expires_at=current_time + ttl
                )
                
                # 检查是否需要淘汰
                if len(self._cache) >= self.max_size and key not in self._cache:
                    self._evict_lru()
                
                self._cache[key] = entry
                self._update_stats()
                
                return True
        
        except Exception as e:
            self.logger.error(f"设置缓存失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._update_stats()
                return True
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats = CacheStats()
            return True
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        with self._lock:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            if entry.is_expired():
                del self._cache[key]
                self._stats.expired_count += 1
                return False
            
            return True
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        with self._lock:
            self._update_stats()
            return self._stats
    
    def _cleanup_if_needed(self):
        """按需清理过期缓存"""
        current_time = time.time()
        if current_time - self._last_cleanup > self.cleanup_interval:
            self._cleanup_expired()
            self._last_cleanup = current_time
    
    def _cleanup_expired(self):
        """清理过期缓存"""
        expired_keys = []
        current_time = time.time()
        
        for key, entry in self._cache.items():
            if entry.expires_at < current_time:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            self._stats.expired_count += 1
    
    def _evict_lru(self):
        """淘汰最近最少使用的缓存"""
        if not self._cache:
            return
        
        # 找到最近最少使用的条目
        lru_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_accessed)
        del self._cache[lru_key]
        self._stats.evicted_count += 1
    
    def _update_stats(self):
        """更新统计信息"""
        self._stats.total_keys = len(self._cache)
        self._stats.total_size = sum(entry.size for entry in self._cache.values())


class FileCache(CacheBackend):
    """文件缓存后端"""
    
    def __init__(self, cache_dir: str = "./cache", default_ttl: int = 300, compress: bool = True):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.default_ttl = default_ttl
        self.compress = compress
        
        self._stats = CacheStats()
        self._lock = threading.RLock()
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化统计信息
        self._update_stats()
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名过长和特殊字符
        hash_key = hashlib.md5(key.encode()).hexdigest()
        extension = '.gz' if self.compress else '.cache'
        return self.cache_dir / f"{hash_key}{extension}"
    
    def _get_meta_path(self, key: str) -> Path:
        """获取元数据文件路径"""
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{hash_key}.meta"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            with self._lock:
                cache_path = self._get_cache_path(key)
                meta_path = self._get_meta_path(key)
                
                if not cache_path.exists() or not meta_path.exists():
                    self._stats.miss_count += 1
                    return None
                
                # 读取元数据
                with open(meta_path, 'r') as f:
                    meta = json.load(f)
                
                # 检查是否过期
                if time.time() > meta['expires_at']:
                    cache_path.unlink(missing_ok=True)
                    meta_path.unlink(missing_ok=True)
                    self._stats.expired_count += 1
                    self._stats.miss_count += 1
                    return None
                
                # 读取缓存数据
                if self.compress:
                    with gzip.open(cache_path, 'rb') as f:
                        data = pickle.load(f)
                else:
                    with open(cache_path, 'rb') as f:
                        data = pickle.load(f)
                
                # 更新访问信息
                meta['access_count'] = meta.get('access_count', 0) + 1
                meta['last_accessed'] = time.time()
                
                with open(meta_path, 'w') as f:
                    json.dump(meta, f)
                
                self._stats.hit_count += 1
                return data
        
        except Exception as e:
            self.logger.error(f"获取文件缓存失败: {e}")
            self._stats.miss_count += 1
            return None
    
    def set(self, key: str, value: Any, ttl: int = 0) -> bool:
        """设置缓存值"""
        try:
            with self._lock:
                ttl = ttl or self.default_ttl
                current_time = time.time()
                
                cache_path = self._get_cache_path(key)
                meta_path = self._get_meta_path(key)
                
                # 写入缓存数据
                if self.compress:
                    with gzip.open(cache_path, 'wb') as f:
                        pickle.dump(value, f)
                else:
                    with open(cache_path, 'wb') as f:
                        pickle.dump(value, f)
                
                # 写入元数据
                meta = {
                    'key': key,
                    'created_at': current_time,
                    'expires_at': current_time + ttl,
                    'access_count': 0,
                    'last_accessed': current_time,
                    'size': cache_path.stat().st_size
                }
                
                with open(meta_path, 'w') as f:
                    json.dump(meta, f)
                
                self._update_stats()
                return True
        
        except Exception as e:
            self.logger.error(f"设置文件缓存失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            with self._lock:
                cache_path = self._get_cache_path(key)
                meta_path = self._get_meta_path(key)
                
                cache_path.unlink(missing_ok=True)
                meta_path.unlink(missing_ok=True)
                
                self._update_stats()
                return True
        
        except Exception as e:
            self.logger.error(f"删除文件缓存失败: {e}")
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        try:
            with self._lock:
                for cache_file in self.cache_dir.glob("*"):
                    if cache_file.is_file():
                        cache_file.unlink()
                
                self._stats = CacheStats()
                return True
        
        except Exception as e:
            self.logger.error(f"清空文件缓存失败: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            cache_path = self._get_cache_path(key)
            meta_path = self._get_meta_path(key)
            
            if not cache_path.exists() or not meta_path.exists():
                return False
            
            # 检查是否过期
            with open(meta_path, 'r') as f:
                meta = json.load(f)
            
            if time.time() > meta['expires_at']:
                cache_path.unlink(missing_ok=True)
                meta_path.unlink(missing_ok=True)
                return False
            
            return True
        
        except Exception as e:
            self.logger.error(f"检查文件缓存存在失败: {e}")
            return False
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        with self._lock:
            self._update_stats()
            return self._stats
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            cache_files = list(self.cache_dir.glob("*.cache")) + list(self.cache_dir.glob("*.gz"))
            meta_files = list(self.cache_dir.glob("*.meta"))
            
            self._stats.total_keys = len(meta_files)
            
            total_size = 0
            for cache_file in cache_files:
                if cache_file.exists():
                    total_size += cache_file.stat().st_size
            
            self._stats.total_size = total_size
        
        except Exception as e:
            self.logger.error(f"更新文件缓存统计失败: {e}")
    
    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        cleaned_count = 0
        try:
            with self._lock:
                current_time = time.time()
                
                for meta_file in self.cache_dir.glob("*.meta"):
                    try:
                        with open(meta_file, 'r') as f:
                            meta = json.load(f)
                        
                        if current_time > meta['expires_at']:
                            # 删除过期文件
                            hash_key = meta_file.stem
                            cache_file = self.cache_dir / f"{hash_key}.cache"
                            gz_file = self.cache_dir / f"{hash_key}.gz"
                            
                            meta_file.unlink(missing_ok=True)
                            cache_file.unlink(missing_ok=True)
                            gz_file.unlink(missing_ok=True)
                            
                            cleaned_count += 1
                    
                    except Exception:
                        # 如果元数据文件损坏，也删除
                        meta_file.unlink(missing_ok=True)
                        cleaned_count += 1
                
                self._stats.expired_count += cleaned_count
                self._update_stats()
                
                return cleaned_count
        
        except Exception as e:
            self.logger.error(f"清理过期文件缓存失败: {e}")
            return cleaned_count


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, backend: str = 'memory', **kwargs):
        self.logger = logging.getLogger(__name__)
        
        # 创建缓存后端
        if backend == 'memory':
            self.backend = MemoryCache(**kwargs)
        elif backend == 'file':
            self.backend = FileCache(**kwargs)
        else:
            raise ValueError(f"不支持的缓存后端: {backend}")
        
        self.backend_type = backend
        
        # 缓存配置
        self.enable_cache = getattr(settings.data.DATA_STORAGE, 'enable_cache', True)
        self.default_ttl = getattr(settings.data.DATA_FETCH_CONFIG, 'cache_duration', 300)
        
        self.logger.info(f"缓存管理器初始化完成 - 后端: {backend}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存"""
        if not self.enable_cache:
            return default
        
        try:
            value = self.backend.get(key)
            return value if value is not None else default
        except Exception as e:
            self.logger.error(f"获取缓存失败 {key}: {e}")
            return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存"""
        if not self.enable_cache:
            return False
        
        try:
            ttl = ttl or self.default_ttl
            return self.backend.set(key, value, ttl)
        except Exception as e:
            self.logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            return self.backend.delete(key)
        except Exception as e:
            self.logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        try:
            result = self.backend.clear()
            self.logger.info("缓存已清空")
            return result
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self.enable_cache:
            return False
        
        try:
            return self.backend.exists(key)
        except Exception as e:
            self.logger.error(f"检查缓存存在失败 {key}: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        try:
            stats = self.backend.get_stats()
            return {
                'backend_type': self.backend_type,
                'enabled': self.enable_cache,
                'default_ttl': self.default_ttl,
                **stats.to_dict()
            }
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def cleanup(self) -> int:
        """清理过期缓存"""
        try:
            if hasattr(self.backend, 'cleanup_expired'):
                cleaned_count = self.backend.cleanup_expired()
                self.logger.info(f"清理过期缓存完成，删除 {cleaned_count} 个条目")
                return cleaned_count
            return 0
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            return 0
    
    def get_or_set(self, key: str, func, ttl: Optional[int] = None, *args, **kwargs) -> Any:
        """
        获取缓存，如果不存在则执行函数并缓存结果
        
        Args:
            key: 缓存键
            func: 获取数据的函数
            ttl: 缓存时间
            *args, **kwargs: 传递给函数的参数
            
        Returns:
            缓存的值或函数执行结果
        """
        # 先尝试从缓存获取
        cached_value = self.get(key)
        if cached_value is not None:
            return cached_value
        
        # 缓存不存在，执行函数
        try:
            value = func(*args, **kwargs)
            if value is not None:
                self.set(key, value, ttl)
            return value
        except Exception as e:
            self.logger.error(f"执行缓存函数失败 {key}: {e}")
            raise
    
    def invalidate_pattern(self, pattern: str) -> int:
        """根据模式删除缓存"""
        # 这个功能需要后端支持
        # 目前简单实现：如果是内存缓存，可以遍历删除
        if isinstance(self.backend, MemoryCache):
            deleted_count = 0
            with self.backend._lock:
                keys_to_delete = []
                for key in self.backend._cache.keys():
                    if pattern in key:
                        keys_to_delete.append(key)
                
                for key in keys_to_delete:
                    if self.delete(key):
                        deleted_count += 1
            
            return deleted_count
        
        return 0


# 创建全局缓存管理器实例
def create_cache_manager(backend: str = 'memory', **kwargs) -> CacheManager:
    """创建缓存管理器"""
    try:
        # 从配置获取缓存设置
        cache_config = getattr(settings.data, 'CACHE_CONFIG', {})
        
        # 合并配置
        final_kwargs = {**cache_config, **kwargs}
        
        return CacheManager(backend=backend, **final_kwargs)
    except Exception as e:
        logging.getLogger(__name__).error(f"创建缓存管理器失败: {e}")
        # 返回默认的内存缓存
        return CacheManager(backend='memory')


# 全局缓存管理器实例
cache_manager = create_cache_manager()


if __name__ == '__main__':
    # 测试缓存管理器
    import time
    
    print("=== 缓存管理器测试 ===")
    
    # 测试内存缓存
    print("\n1. 测试内存缓存:")
    memory_cache = CacheManager(backend='memory', max_size=100, default_ttl=5)
    
    # 设置缓存
    memory_cache.set('test_key', {'data': 'test_value', 'number': 123})
    print(f"设置缓存: test_key")
    
    # 获取缓存
    cached_data = memory_cache.get('test_key')
    print(f"获取缓存: {cached_data}")
    
    # 检查存在
    exists = memory_cache.exists('test_key')
    print(f"缓存存在: {exists}")
    
    # 获取统计
    stats = memory_cache.get_stats()
    print(f"缓存统计: 命中率 {stats['hit_rate']:.1f}%, 总键数 {stats['total_keys']}")
    
    # 测试文件缓存
    print("\n2. 测试文件缓存:")
    file_cache = CacheManager(backend='file', cache_dir='./test_cache', default_ttl=10)
    
    # 设置缓存
    file_cache.set('file_test', {'file_data': 'test', 'timestamp': time.time()})
    print(f"设置文件缓存: file_test")
    
    # 获取缓存
    file_data = file_cache.get('file_test')
    print(f"获取文件缓存: {file_data}")
    
    # 测试get_or_set
    print("\n3. 测试get_or_set:")
    
    def expensive_function(x, y):
        print(f"执行昂贵的计算: {x} + {y}")
        time.sleep(0.1)  # 模拟耗时操作
        return x + y
    
    # 第一次调用，会执行函数
    result1 = memory_cache.get_or_set('calc_1_2', expensive_function, None, 1, 2)
    print(f"第一次结果: {result1}")
    
    # 第二次调用，从缓存获取
    result2 = memory_cache.get_or_set('calc_1_2', expensive_function, None, 1, 2)
    print(f"第二次结果: {result2}")
    
    # 测试过期
    print("\n4. 测试缓存过期:")
    memory_cache.set('expire_test', 'will_expire', ttl=2)
    print(f"设置2秒过期缓存")
    
    print(f"立即获取: {memory_cache.get('expire_test')}")
    time.sleep(3)
    print(f"3秒后获取: {memory_cache.get('expire_test')}")
    
    # 清理测试文件
    file_cache.clear()
    
    print("\n=== 测试完成 ===")