#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 数据存储模块

本模块负责数据的持久化存储，包括：
- SQLite数据库管理
- 数据表结构定义
- 数据CRUD操作
- 数据备份和恢复
- 数据清理和维护

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import sqlite3
import pandas as pd
import numpy as np
import datetime
import logging
import json
import os
import shutil
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
from contextlib import contextmanager
from dataclasses import asdict

# 导入配置和数据结构
from config.settings import settings


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            db_path = settings.data.DATA_STORAGE['database_path']
        
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建实时数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS realtime_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        name TEXT NOT NULL,
                        timestamp DATETIME NOT NULL,
                        open_price REAL NOT NULL,
                        high_price REAL NOT NULL,
                        low_price REAL NOT NULL,
                        close_price REAL NOT NULL,
                        volume INTEGER NOT NULL,
                        amount REAL NOT NULL,
                        change_value REAL NOT NULL,
                        change_pct REAL NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(symbol, timestamp)
                    )
                ''')
                
                # 创建历史数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS historical_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        datetime DATETIME NOT NULL,
                        open_price REAL NOT NULL,
                        high_price REAL NOT NULL,
                        low_price REAL NOT NULL,
                        close_price REAL NOT NULL,
                        volume INTEGER NOT NULL,
                        amount REAL,
                        change_value REAL,
                        change_pct REAL,
                        period TEXT NOT NULL DEFAULT '1min',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(symbol, datetime, period)
                    )
                ''')
                
                # 创建信号记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS signal_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        signal_type TEXT NOT NULL,
                        signal_level TEXT NOT NULL,
                        signal_value REAL NOT NULL,
                        threshold_value REAL NOT NULL,
                        description TEXT,
                        metadata TEXT,
                        timestamp DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建系统日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        level TEXT NOT NULL,
                        module TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        timestamp DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建配置历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS config_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        config_type TEXT NOT NULL,
                        config_data TEXT NOT NULL,
                        version TEXT NOT NULL,
                        description TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建索引
                self._create_indexes(cursor)
                
                conn.commit()
                self.logger.info("数据库初始化完成")
        
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_realtime_symbol_time ON realtime_data(symbol, timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_historical_symbol_time ON historical_data(symbol, datetime)",
            "CREATE INDEX IF NOT EXISTS idx_historical_period ON historical_data(period)",
            "CREATE INDEX IF NOT EXISTS idx_signals_symbol_time ON signal_records(symbol, timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_signals_type ON signal_records(signal_type)",
            "CREATE INDEX IF NOT EXISTS idx_logs_level_time ON system_logs(level, timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_logs_module ON system_logs(module)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建索引失败: {e}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,
                check_same_thread=False
            )
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库连接错误: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def save_realtime_data(self, market_data: Dict[str, Any]) -> bool:
        """保存实时数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 确保timestamp是datetime类型
                timestamp_val = market_data.get('timestamp')
                if isinstance(timestamp_val, pd.Timestamp):
                    timestamp = timestamp_val.to_pydatetime()
                elif isinstance(timestamp_val, datetime.datetime):
                    timestamp = timestamp_val
                else:
                    timestamp = pd.to_datetime(timestamp_val).to_pydatetime()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO realtime_data 
                    (symbol, name, timestamp, open_price, high_price, low_price, 
                     close_price, volume, amount, change_value, change_pct)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    market_data.get('symbol'),
                    market_data.get('name'),
                    timestamp,
                    market_data.get('open_price'),
                    market_data.get('high_price'),
                    market_data.get('low_price'),
                    market_data.get('close_price'),
                    market_data.get('volume'),
                    market_data.get('amount'),
                    market_data.get('change'),
                    market_data.get('change_pct')
                ))
                
                conn.commit()
                return True
        
        except Exception as e:
            self.logger.error(f"保存实时数据失败: {e}")
            return False
    
    def save_historical_data(self, symbol: str, df: pd.DataFrame, period: str = '1min') -> bool:
        """保存历史数据"""
        try:
            if df.empty:
                return True
            
            with self.get_connection() as conn:
                # 准备数据
                data_to_insert = []
                for index, row in df.iterrows():
                    # 确保时间戳是datetime类型
                    if isinstance(index, pd.Timestamp):
                        dt_index = index.to_pydatetime()
                    elif isinstance(index, datetime.datetime):
                        dt_index = index
                    else:
                        dt_index = pd.to_datetime(index).to_pydatetime()
                    
                    data_to_insert.append((
                        symbol,
                        dt_index,
                        float(row.get('open', 0)),
                        float(row.get('high', 0)),
                        float(row.get('low', 0)),
                        float(row.get('close', 0)),
                        int(row.get('volume', 0)),
                        float(row.get('amount', 0)) if 'amount' in row else None,
                        float(row.get('change', 0)) if 'change' in row else None,
                        float(row.get('change_pct', 0)) if 'change_pct' in row else None,
                        period
                    ))
                
                cursor = conn.cursor()
                cursor.executemany('''
                    INSERT OR REPLACE INTO historical_data 
                    (symbol, datetime, open_price, high_price, low_price, 
                     close_price, volume, amount, change_value, change_pct, period)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', data_to_insert)
                
                conn.commit()
                self.logger.info(f"保存历史数据成功: {symbol}, {len(data_to_insert)} 条记录")
                return True
        
        except Exception as e:
            self.logger.error(f"保存历史数据失败: {e}")
            return False
    
    def get_latest_realtime_data(self, symbol: str = None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """获取最新实时数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if symbol:
                    cursor.execute('''
                        SELECT * FROM realtime_data 
                        WHERE symbol = ? 
                        ORDER BY timestamp DESC 
                        LIMIT 1
                    ''', (symbol,))
                    row = cursor.fetchone()
                    return dict(row) if row else None
                else:
                    cursor.execute('''
                        SELECT * FROM realtime_data r1
                        WHERE r1.timestamp = (
                            SELECT MAX(r2.timestamp) 
                            FROM realtime_data r2 
                            WHERE r2.symbol = r1.symbol
                        )
                        ORDER BY r1.symbol
                    ''')
                    rows = cursor.fetchall()
                    return [dict(row) for row in rows]
        
        except Exception as e:
            self.logger.error(f"获取实时数据失败: {e}")
            return None if symbol else []
    
    def get_historical_data(self, symbol: str, period: str = '1min', 
                          start_time: datetime.datetime = None,
                          end_time: datetime.datetime = None,
                          limit: int = None) -> pd.DataFrame:
        """获取历史数据"""
        try:
            with self.get_connection() as conn:
                # 构建查询条件
                where_conditions = ["symbol = ?", "period = ?"]
                params = [symbol, period]
                
                if start_time:
                    where_conditions.append("datetime >= ?")
                    params.append(start_time)
                
                if end_time:
                    where_conditions.append("datetime <= ?")
                    params.append(end_time)
                
                where_clause = " AND ".join(where_conditions)
                
                # 构建完整查询
                query = f'''
                    SELECT datetime, open_price as open, high_price as high, 
                           low_price as low, close_price as close, volume,
                           amount, change_value as change, change_pct
                    FROM historical_data 
                    WHERE {where_clause}
                    ORDER BY datetime DESC
                '''
                
                if limit:
                    query += f" LIMIT {limit}"
                
                df = pd.read_sql_query(query, conn, params=params, parse_dates=['datetime'])
                
                if not df.empty:
                    df = df.set_index('datetime').sort_index()
                
                return df
        
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def save_signal_record(self, symbol: str, signal_type: str, signal_level: str,
                          signal_value: float, threshold_value: float,
                          description: str = None, metadata: Dict = None,
                          timestamp: datetime.datetime = None) -> bool:
        """保存信号记录"""
        try:
            if timestamp is None:
                timestamp = datetime.datetime.now()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO signal_records 
                    (symbol, signal_type, signal_level, signal_value, threshold_value,
                     description, metadata, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol,
                    signal_type,
                    signal_level,
                    signal_value,
                    threshold_value,
                    description,
                    json.dumps(metadata) if metadata else None,
                    timestamp
                ))
                
                conn.commit()
                return True
        
        except Exception as e:
            self.logger.error(f"保存信号记录失败: {e}")
            return False
    
    def get_signal_records(self, symbol: str = None, signal_type: str = None,
                          start_time: datetime.datetime = None,
                          end_time: datetime.datetime = None,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """获取信号记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if symbol:
                    where_conditions.append("symbol = ?")
                    params.append(symbol)
                
                if signal_type:
                    where_conditions.append("signal_type = ?")
                    params.append(signal_type)
                
                if start_time:
                    where_conditions.append("timestamp >= ?")
                    params.append(start_time)
                
                if end_time:
                    where_conditions.append("timestamp <= ?")
                    params.append(end_time)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                query = f'''
                    SELECT * FROM signal_records 
                    WHERE {where_clause}
                    ORDER BY timestamp DESC
                    LIMIT ?
                '''
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                results = []
                for row in rows:
                    record = dict(row)
                    if record['metadata']:
                        try:
                            record['metadata'] = json.loads(record['metadata'])
                        except:
                            record['metadata'] = {}
                    results.append(record)
                
                return results
        
        except Exception as e:
            self.logger.error(f"获取信号记录失败: {e}")
            return []
    
    def save_system_log(self, level: str, module: str, message: str,
                       details: Dict = None, timestamp: datetime.datetime = None) -> bool:
        """保存系统日志"""
        try:
            if timestamp is None:
                timestamp = datetime.datetime.now()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO system_logs 
                    (level, module, message, details, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    level,
                    module,
                    message,
                    json.dumps(details) if details else None,
                    timestamp
                ))
                
                conn.commit()
                return True
        
        except Exception as e:
            self.logger.error(f"保存系统日志失败: {e}")
            return False
    
    def cleanup_old_data(self, days_to_keep: int = 30) -> bool:
        """清理旧数据"""
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 清理旧的实时数据
                cursor.execute('''
                    DELETE FROM realtime_data 
                    WHERE timestamp < ?
                ''', (cutoff_date,))
                realtime_deleted = cursor.rowcount
                
                # 清理旧的系统日志
                cursor.execute('''
                    DELETE FROM system_logs 
                    WHERE timestamp < ?
                ''', (cutoff_date,))
                logs_deleted = cursor.rowcount
                
                # 清理旧的信号记录（保留更长时间）
                signal_cutoff = datetime.datetime.now() - datetime.timedelta(days=days_to_keep * 3)
                cursor.execute('''
                    DELETE FROM signal_records 
                    WHERE timestamp < ?
                ''', (signal_cutoff,))
                signals_deleted = cursor.rowcount
                
                conn.commit()
                
                self.logger.info(f"数据清理完成: 实时数据 {realtime_deleted} 条, "
                               f"系统日志 {logs_deleted} 条, 信号记录 {signals_deleted} 条")
                return True
        
        except Exception as e:
            self.logger.error(f"数据清理失败: {e}")
            return False
    
    def backup_database(self, backup_path: str = None) -> bool:
        """备份数据库"""
        try:
            if backup_path is None:
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"{self.db_path.parent}/backup_{timestamp}.db"
            
            shutil.copy2(self.db_path, backup_path)
            self.logger.info(f"数据库备份完成: {backup_path}")
            return True
        
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # 表记录数统计
                tables = ['realtime_data', 'historical_data', 'signal_records', 'system_logs']
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[f"{table}_count"] = cursor.fetchone()[0]
                
                # 数据库文件大小
                stats['database_size_mb'] = round(self.db_path.stat().st_size / 1024 / 1024, 2)
                
                # 最新数据时间
                cursor.execute("SELECT MAX(timestamp) FROM realtime_data")
                latest_realtime = cursor.fetchone()[0]
                stats['latest_realtime'] = latest_realtime
                
                cursor.execute("SELECT MAX(datetime) FROM historical_data")
                latest_historical = cursor.fetchone()[0]
                stats['latest_historical'] = latest_historical
                
                # 监控标的统计
                cursor.execute("SELECT symbol, COUNT(*) FROM realtime_data GROUP BY symbol")
                symbol_counts = cursor.fetchall()
                stats['symbol_counts'] = {row[0]: row[1] for row in symbol_counts}
                
                return stats
        
        except Exception as e:
            self.logger.error(f"获取数据库统计失败: {e}")
            return {}
    
    def vacuum_database(self) -> bool:
        """优化数据库"""
        try:
            with self.get_connection() as conn:
                conn.execute("VACUUM")
                self.logger.info("数据库优化完成")
                return True
        
        except Exception as e:
            self.logger.error(f"数据库优化失败: {e}")
            return False


class DataStorageManager:
    """数据存储管理器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.logger = logging.getLogger(__name__)
        
        # 存储配置
        self.auto_cleanup = settings.data.DATA_STORAGE['auto_cleanup']
        self.cleanup_days = settings.data.DATA_STORAGE['cleanup_days']
        self.auto_backup = settings.data.DATA_STORAGE['auto_backup']
        
        # 最后清理时间
        self._last_cleanup = None
        self._last_backup = None
    
    def store_market_data(self, market_data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> bool:
        """存储市场数据"""
        try:
            if isinstance(market_data, dict):
                return self.db_manager.save_realtime_data(market_data)
            elif isinstance(market_data, list):
                success_count = 0
                for data in market_data:
                    if self.db_manager.save_realtime_data(data):
                        success_count += 1
                
                self.logger.info(f"批量存储市场数据: {success_count}/{len(market_data)} 成功")
                return success_count == len(market_data)
            else:
                self.logger.error("不支持的数据类型")
                return False
        
        except Exception as e:
            self.logger.error(f"存储市场数据失败: {e}")
            return False
    
    def store_historical_data(self, symbol: str, df: pd.DataFrame, period: str = '1min') -> bool:
        """存储历史数据"""
        return self.db_manager.save_historical_data(symbol, df, period)
    
    def get_stored_data(self, symbol: str, data_type: str = 'realtime', **kwargs) -> Any:
        """获取存储的数据"""
        try:
            if data_type == 'realtime':
                return self.db_manager.get_latest_realtime_data(symbol)
            elif data_type == 'historical':
                return self.db_manager.get_historical_data(symbol, **kwargs)
            elif data_type == 'signals':
                return self.db_manager.get_signal_records(symbol=symbol, **kwargs)
            else:
                self.logger.error(f"不支持的数据类型: {data_type}")
                return None
        
        except Exception as e:
            self.logger.error(f"获取存储数据失败: {e}")
            return None
    
    def perform_maintenance(self, force: bool = False) -> Dict[str, bool]:
        """执行维护任务"""
        results = {
            'cleanup': False,
            'backup': False,
            'vacuum': False
        }
        
        try:
            current_time = datetime.datetime.now()
            
            # 自动清理
            if (self.auto_cleanup and 
                (force or self._last_cleanup is None or 
                 current_time - self._last_cleanup > datetime.timedelta(days=1))):
                
                results['cleanup'] = self.db_manager.cleanup_old_data(self.cleanup_days)
                if results['cleanup']:
                    self._last_cleanup = current_time
            
            # 自动备份
            if (self.auto_backup and 
                (force or self._last_backup is None or 
                 current_time - self._last_backup > datetime.timedelta(days=7))):
                
                results['backup'] = self.db_manager.backup_database()
                if results['backup']:
                    self._last_backup = current_time
            
            # 数据库优化
            if force:
                results['vacuum'] = self.db_manager.vacuum_database()
            
            return results
        
        except Exception as e:
            self.logger.error(f"维护任务执行失败: {e}")
            return results
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        return self.db_manager.get_database_stats()


# 全局数据存储管理器实例
data_storage = DataStorageManager()


if __name__ == '__main__':
    # 测试数据存储模块
    print("=== 数据存储模块测试 ===")
    
    # 创建测试数据
    test_data = {
        'symbol': '510500',
        'name': '中证500ETF',
        'timestamp': datetime.datetime.now(),
        'open_price': 100.0,
        'high_price': 102.0,
        'low_price': 99.0,
        'close_price': 101.5,
        'volume': 1000000,
        'amount': 101500000.0,
        'change': 1.5,
        'change_pct': 1.5
    }
    
    # 测试存储
    print("\n1. 测试数据存储:")
    success = data_storage.store_market_data(test_data)
    print(f"存储结果: {'成功' if success else '失败'}")
    
    # 测试获取
    print("\n2. 测试数据获取:")
    stored_data = data_storage.get_stored_data('510500', 'realtime')
    if stored_data:
        print(f"获取到数据: {stored_data['name']} - {stored_data['close_price']}")
    else:
        print("未获取到数据")
    
    # 测试统计信息
    print("\n3. 测试统计信息:")
    stats = data_storage.get_storage_stats()
    print(f"数据库大小: {stats.get('database_size_mb', 0)} MB")
    print(f"实时数据条数: {stats.get('realtime_data_count', 0)}")
    
    # 测试维护
    print("\n4. 测试维护任务:")
    maintenance_results = data_storage.perform_maintenance(force=True)
    for task, result in maintenance_results.items():
        print(f"{task}: {'成功' if result else '失败'}")