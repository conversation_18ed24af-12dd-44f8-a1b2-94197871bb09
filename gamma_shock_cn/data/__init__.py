# -*- coding: utf-8 -*-
"""
数据模块

包含数据获取、数据处理、缓存管理等功能
"""

# 导入数据获取相关类
from .data_fetcher import DataFetcher

# 导入数据处理相关类
from .data_processor import DataProcessor, DataQualityReport

# 导入数据存储相关类
from .data_storage import DatabaseManager, DataStorageManager

# 为了保持向后兼容性，创建别名
DataStorage = DataStorageManager

# 导入数据验证相关类
from .data_validator import DataValidator, ValidationResult, ValidationLevel, ValidationRule

# 导入缓存管理相关类
from .cache_manager import CacheManager, CacheStats
from .data_cache import DataCache, CacheEntry, CacheBackend, MemoryCache

# 创建全局实例
data_fetcher = DataFetcher()
data_processor = DataProcessor()
data_validator = DataValidator()
cache_manager = CacheManager()

# 定义模块公共接口
__all__ = [
    # 数据获取
    'DataFetcher',
    'data_fetcher',
    
    # 数据处理
    'DataProcessor',
    'DataQualityReport',
    'data_processor',
    
    # 数据存储
    'DatabaseManager',
    
    # 数据验证
    'DataValidator',
    'ValidationResult',
    'ValidationLevel',
    'ValidationRule',
    'data_validator',
    
    # 缓存管理
    'CacheManager',
    'CacheStats',
    'DataCache',
    'CacheEntry',
    'CacheBackend',
    'MemoryCache',
    'cache_manager'
]

__version__ = "1.0.0"
__author__ = "Gamma Shock Team"