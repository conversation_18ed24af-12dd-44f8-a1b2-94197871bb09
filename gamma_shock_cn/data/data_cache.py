#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 数据缓存模块

本模块负责数据缓存管理，包括：
- 内存缓存管理
- Redis缓存支持
- 缓存策略配置
- 缓存失效机制
- 缓存性能监控

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import json
import time
import hashlib
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import OrderedDict
from abc import ABC, abstractmethod

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

# 导入配置和数据结构
from config.settings import settings


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    timestamp: datetime
    ttl: int  # 生存时间（秒）
    access_count: int = 0
    last_access: datetime = None
    
    def __post_init__(self):
        if self.last_access is None:
            self.last_access = self.timestamp
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl <= 0:  # 永不过期
            return False
        return datetime.now() > self.timestamp + timedelta(seconds=self.ttl)
    
    def update_access(self):
        """更新访问信息"""
        self.access_count += 1
        self.last_access = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'key': self.key,
            'value': self.value,
            'timestamp': self.timestamp.isoformat(),
            'ttl': self.ttl,
            'access_count': self.access_count,
            'last_access': self.last_access.isoformat()
        }


@dataclass
class CacheStats:
    """缓存统计"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    total_entries: int = 0
    memory_usage: int = 0  # 字节
    expired_entries: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        if self.total_requests == 0:
            return 0.0
        return self.cache_hits / self.total_requests
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'total_requests': self.total_requests,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': round(self.hit_rate, 4),
            'miss_rate': round(self.miss_rate, 4),
            'total_entries': self.total_entries,
            'memory_usage': self.memory_usage,
            'expired_entries': self.expired_entries
        }


class CacheBackend(ABC):
    """缓存后端抽象基类"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = 0) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """清空缓存"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass
    
    @abstractmethod
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        pass


class MemoryCache(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self, max_size: int = 1000, cleanup_interval: int = 300):
        self.max_size = max_size
        self.cleanup_interval = cleanup_interval
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._stats = CacheStats()
        self._last_cleanup = time.time()
        
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            self._stats.total_requests += 1
            
            # 定期清理过期缓存
            self._cleanup_if_needed()
            
            if key not in self._cache:
                self._stats.cache_misses += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self._stats.cache_misses += 1
                self._stats.expired_entries += 1
                return None
            
            # 更新访问信息
            entry.update_access()
            
            # 移到末尾（LRU策略）
            self._cache.move_to_end(key)
            
            self._stats.cache_hits += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: int = 0) -> bool:
        """设置缓存值"""
        with self._lock:
            try:
                # 检查是否需要清理空间
                if len(self._cache) >= self.max_size and key not in self._cache:
                    self._evict_lru()
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    timestamp=datetime.now(),
                    ttl=ttl
                )
                
                self._cache[key] = entry
                self._cache.move_to_end(key)
                
                self._update_stats()
                return True
            
            except Exception as e:
                self.logger.error(f"设置缓存失败: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._update_stats()
                return True
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats = CacheStats()
            return True
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        with self._lock:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            if entry.is_expired():
                del self._cache[key]
                self._stats.expired_entries += 1
                return False
            
            return True
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        with self._lock:
            self._update_stats()
            return self._stats
    
    def _evict_lru(self):
        """淘汰最少使用的缓存"""
        if self._cache:
            # 移除最旧的条目
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
    
    def _cleanup_if_needed(self):
        """按需清理过期缓存"""
        current_time = time.time()
        if current_time - self._last_cleanup > self.cleanup_interval:
            self._cleanup_expired()
            self._last_cleanup = current_time
    
    def _cleanup_expired(self):
        """清理过期缓存"""
        expired_keys = []
        for key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            self._stats.expired_entries += 1
    
    def _update_stats(self):
        """更新统计信息"""
        self._stats.total_entries = len(self._cache)
        # 估算内存使用量
        self._stats.memory_usage = sum(
            len(str(entry.key)) + len(str(entry.value)) + 200  # 估算开销
            for entry in self._cache.values()
        )


class RedisCache(CacheBackend):
    """Redis缓存后端"""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, 
                 db: int = 0, password: str = None, prefix: str = 'gamma_shock:'):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis not available. Install redis-py: pip install redis")
        
        self.prefix = prefix
        self._stats = CacheStats()
        self.logger = logging.getLogger(__name__)
        
        try:
            self.redis_client = redis.Redis(
                host=host,
                port=port,
                db=db,
                password=password,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            # 测试连接
            self.redis_client.ping()
            self.logger.info("Redis连接成功")
        
        except Exception as e:
            self.logger.error(f"Redis连接失败: {e}")
            raise
    
    def _make_key(self, key: str) -> str:
        """生成带前缀的键"""
        return f"{self.prefix}{key}"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            self._stats.total_requests += 1
            
            redis_key = self._make_key(key)
            value = self.redis_client.get(redis_key)
            
            if value is None:
                self._stats.cache_misses += 1
                return None
            
            # 反序列化
            try:
                result = json.loads(value)
                self._stats.cache_hits += 1
                return result
            except json.JSONDecodeError:
                # 如果不是JSON，直接返回字符串
                self._stats.cache_hits += 1
                return value
        
        except Exception as e:
            self.logger.error(f"Redis获取失败: {e}")
            self._stats.cache_misses += 1
            return None
    
    def set(self, key: str, value: Any, ttl: int = 0) -> bool:
        """设置缓存值"""
        try:
            redis_key = self._make_key(key)
            
            # 序列化值
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            else:
                serialized_value = str(value)
            
            # 设置缓存
            if ttl > 0:
                result = self.redis_client.setex(redis_key, ttl, serialized_value)
            else:
                result = self.redis_client.set(redis_key, serialized_value)
            
            return bool(result)
        
        except Exception as e:
            self.logger.error(f"Redis设置失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            redis_key = self._make_key(key)
            result = self.redis_client.delete(redis_key)
            return result > 0
        
        except Exception as e:
            self.logger.error(f"Redis删除失败: {e}")
            return False
    
    def clear(self) -> bool:
        """清空缓存"""
        try:
            # 删除所有带前缀的键
            pattern = f"{self.prefix}*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
            
            self._stats = CacheStats()
            return True
        
        except Exception as e:
            self.logger.error(f"Redis清空失败: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            redis_key = self._make_key(key)
            return bool(self.redis_client.exists(redis_key))
        
        except Exception as e:
            self.logger.error(f"Redis检查存在失败: {e}")
            return False
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        try:
            # 获取Redis信息
            info = self.redis_client.info()
            pattern = f"{self.prefix}*"
            keys = self.redis_client.keys(pattern)
            
            self._stats.total_entries = len(keys)
            self._stats.memory_usage = info.get('used_memory', 0)
            
            return self._stats
        
        except Exception as e:
            self.logger.error(f"获取Redis统计失败: {e}")
            return self._stats


class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, backend: CacheBackend = None, 
                 default_ttl: int = 300, enable_compression: bool = False):
        self.backend = backend or MemoryCache()
        self.default_ttl = default_ttl
        self.enable_compression = enable_compression
        self.logger = logging.getLogger(__name__)
        
        # 缓存键前缀
        self.KEY_PREFIXES = {
            'market_data': 'md:',
            'historical_data': 'hd:',
            'indicator': 'ind:',
            'signal': 'sig:',
            'analysis': 'ana:'
        }
    
    def _make_cache_key(self, prefix: str, *args) -> str:
        """生成缓存键"""
        # 将参数转换为字符串并生成哈希
        key_parts = [str(arg) for arg in args]
        key_string = ':'.join(key_parts)
        
        # 如果键太长，使用哈希
        if len(key_string) > 200:
            key_hash = hashlib.md5(key_string.encode()).hexdigest()
            return f"{prefix}{key_hash}"
        
        return f"{prefix}{key_string}"
    
    def get_market_data(self, symbol: str, timestamp: datetime = None) -> Optional[Dict[str, Any]]:
        """获取市场数据缓存"""
        if timestamp is None:
            timestamp = datetime.now().replace(second=0, microsecond=0)
        
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['market_data'],
            symbol,
            timestamp.isoformat()
        )
        
        cached_data = self.backend.get(cache_key)
        if cached_data:
            try:
                # 返回字典格式的数据
                if isinstance(cached_data, dict):
                    if 'timestamp' in cached_data and isinstance(cached_data['timestamp'], str):
                        cached_data['timestamp'] = datetime.fromisoformat(cached_data['timestamp'])
                    return cached_data
                return cached_data
            except Exception as e:
                self.logger.error(f"反序列化市场数据失败: {e}")
                self.backend.delete(cache_key)
        
        return None
    
    def set_market_data(self, market_data: Dict[str, Any], ttl: int = None) -> bool:
        """设置市场数据缓存"""
        if ttl is None:
            ttl = self.default_ttl
        
        symbol = market_data.get('symbol')
        timestamp = market_data.get('timestamp')
        
        if not symbol or not timestamp:
            self.logger.error("市场数据缺少必要字段: symbol或timestamp")
            return False
        
        # 确保timestamp是datetime对象
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp)
        
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['market_data'],
            symbol,
            timestamp.isoformat()
        )
        
        # 序列化数据
        try:
            data_dict = market_data.copy()
            if isinstance(data_dict.get('timestamp'), datetime):
                data_dict['timestamp'] = data_dict['timestamp'].isoformat()
            
            return self.backend.set(cache_key, data_dict, ttl)
        
        except Exception as e:
            self.logger.error(f"序列化市场数据失败: {e}")
            return False
    
    def get_historical_data(self, symbol: str, period: str, 
                          start_date: datetime, end_date: datetime) -> Optional[Any]:
        """获取历史数据缓存"""
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['historical_data'],
            symbol,
            period,
            start_date.date().isoformat(),
            end_date.date().isoformat()
        )
        
        return self.backend.get(cache_key)
    
    def set_historical_data(self, symbol: str, period: str, 
                          start_date: datetime, end_date: datetime, 
                          data: Any, ttl: int = None) -> bool:
        """设置历史数据缓存"""
        if ttl is None:
            ttl = self.default_ttl * 10  # 历史数据缓存更久
        
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['historical_data'],
            symbol,
            period,
            start_date.date().isoformat(),
            end_date.date().isoformat()
        )
        
        return self.backend.set(cache_key, data, ttl)
    
    def get_indicator(self, symbol: str, indicator_name: str, 
                     params: Dict[str, Any] = None, timestamp: datetime = None) -> Optional[Any]:
        """获取技术指标缓存"""
        if timestamp is None:
            timestamp = datetime.now().replace(second=0, microsecond=0)
        
        params_str = json.dumps(params or {}, sort_keys=True)
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['indicator'],
            symbol,
            indicator_name,
            params_str,
            timestamp.isoformat()
        )
        
        return self.backend.get(cache_key)
    
    def set_indicator(self, symbol: str, indicator_name: str, 
                     params: Dict[str, Any] = None, value: Any = None, 
                     timestamp: datetime = None, ttl: int = None) -> bool:
        """设置技术指标缓存"""
        if timestamp is None:
            timestamp = datetime.now().replace(second=0, microsecond=0)
        
        if ttl is None:
            ttl = self.default_ttl
        
        params_str = json.dumps(params or {}, sort_keys=True)
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['indicator'],
            symbol,
            indicator_name,
            params_str,
            timestamp.isoformat()
        )
        
        return self.backend.set(cache_key, value, ttl)
    
    def get_signal(self, symbol: str, signal_type: str, timestamp: datetime = None) -> Optional[Any]:
        """获取信号缓存"""
        if timestamp is None:
            timestamp = datetime.now().replace(second=0, microsecond=0)
        
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['signal'],
            symbol,
            signal_type,
            timestamp.isoformat()
        )
        
        return self.backend.get(cache_key)
    
    def set_signal(self, symbol: str, signal_type: str, signal_data: Any, 
                  timestamp: datetime = None, ttl: int = None) -> bool:
        """设置信号缓存"""
        if timestamp is None:
            timestamp = datetime.now().replace(second=0, microsecond=0)
        
        if ttl is None:
            ttl = self.default_ttl
        
        cache_key = self._make_cache_key(
            self.KEY_PREFIXES['signal'],
            symbol,
            signal_type,
            timestamp.isoformat()
        )
        
        return self.backend.set(cache_key, signal_data, ttl)
    
    def invalidate_symbol(self, symbol: str) -> int:
        """使某个标的的所有缓存失效"""
        count = 0
        for prefix in self.KEY_PREFIXES.values():
            # 这里需要根据具体后端实现
            # 简化实现：只能逐个删除已知的键
            pass
        return count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self.backend.get_stats()
        return {
            'backend_type': type(self.backend).__name__,
            'stats': stats.to_dict(),
            'default_ttl': self.default_ttl,
            'compression_enabled': self.enable_compression
        }
    
    def clear_all(self) -> bool:
        """清空所有缓存"""
        return self.backend.clear()
    
    def health_check(self) -> Dict[str, Any]:
        """缓存健康检查"""
        try:
            # 测试基本操作
            test_key = "health_check_test"
            test_value = {"timestamp": datetime.now().isoformat(), "test": True}
            
            # 测试设置
            set_success = self.backend.set(test_key, test_value, 60)
            
            # 测试获取
            get_result = self.backend.get(test_key)
            get_success = get_result is not None
            
            # 测试删除
            delete_success = self.backend.delete(test_key)
            
            stats = self.backend.get_stats()
            
            return {
                'status': 'healthy' if all([set_success, get_success, delete_success]) else 'unhealthy',
                'operations': {
                    'set': set_success,
                    'get': get_success,
                    'delete': delete_success
                },
                'stats': stats.to_dict(),
                'timestamp': datetime.now().isoformat()
            }
        
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# 创建全局缓存实例
def create_cache_backend() -> CacheBackend:
    """创建缓存后端"""
    cache_config = getattr(settings, 'CACHE', {})
    backend_type = cache_config.get('backend', 'memory')
    
    if backend_type == 'redis' and REDIS_AVAILABLE:
        try:
            redis_config = cache_config.get('redis', {})
            return RedisCache(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                prefix=redis_config.get('prefix', 'gamma_shock:')
            )
        except Exception as e:
            logging.getLogger(__name__).warning(f"Redis缓存初始化失败，使用内存缓存: {e}")
    
    # 默认使用内存缓存
    memory_config = cache_config.get('memory', {})
    return MemoryCache(
        max_size=memory_config.get('max_size', 1000),
        cleanup_interval=memory_config.get('cleanup_interval', 300)
    )


# 全局缓存实例
cache_backend = create_cache_backend()
data_cache = DataCache(
    backend=cache_backend,
    default_ttl=getattr(settings, 'CACHE', {}).get('default_ttl', 300)
)


if __name__ == '__main__':
    # 测试缓存模块
    print("=== 数据缓存模块测试 ===")
    
    # 测试内存缓存
    print("\n1. 测试内存缓存:")
    memory_cache = MemoryCache(max_size=5)
    
    # 设置缓存
    for i in range(3):
        memory_cache.set(f"key_{i}", f"value_{i}", ttl=60)
    
    # 获取缓存
    for i in range(3):
        value = memory_cache.get(f"key_{i}")
        print(f"key_{i}: {value}")
    
    # 统计信息
    stats = memory_cache.get_stats()
    print(f"缓存统计: {stats.to_dict()}")
    
    # 测试数据缓存管理器
    print("\n2. 测试数据缓存管理器:")
    cache_manager = DataCache(backend=memory_cache)
    
    # 创建测试市场数据
    test_market_data = {
        'symbol': '510500',
        'name': '中证500ETF',
        'timestamp': datetime.now(),
        'open_price': 100.0,
        'high_price': 102.0,
        'low_price': 99.0,
        'close_price': 101.5,
        'volume': 1000000,
        'amount': 101500000.0,
        'change': 1.5,
        'change_pct': 1.5
    }
    
    # 测试市场数据缓存
    print("设置市场数据缓存...")
    cache_manager.set_market_data(test_market_data, ttl=60)
    
    print("获取市场数据缓存...")
    cached_data = cache_manager.get_market_data('510500', test_market_data['timestamp'])
    print(f"缓存数据: {cached_data.get('symbol') if cached_data else None}")
    
    # 测试技术指标缓存
    print("\n测试技术指标缓存...")
    cache_manager.set_indicator('510500', 'MA', {'period': 20}, 101.2)
    indicator_value = cache_manager.get_indicator('510500', 'MA', {'period': 20})
    print(f"指标缓存: {indicator_value}")
    
    # 健康检查
    print("\n3. 缓存健康检查:")
    health = cache_manager.health_check()
    print(f"健康状态: {health['status']}")
    print(f"操作测试: {health['operations']}")
    
    # 缓存统计
    print("\n4. 缓存统计信息:")
    cache_stats = cache_manager.get_cache_stats()
    print(f"后端类型: {cache_stats['backend_type']}")
    print(f"命中率: {cache_stats['stats']['hit_rate']}")
    print(f"总条目: {cache_stats['stats']['total_entries']}")