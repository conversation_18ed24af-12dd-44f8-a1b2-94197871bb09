#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统 - 数据验证模块

本模块负责数据质量检查和验证，包括：
- 数据完整性检查
- 数据逻辑性验证
- 异常数据检测
- 数据质量评分
- 数据修复建议

Author: Gamma Shock Team
Version: 1.0.0
Date: 2024-12-19
"""

import pandas as pd
import numpy as np
import datetime
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from enum import Enum

# 导入配置和数据结构
from config.settings import settings


class ValidationLevel(Enum):
    """验证级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ValidationRule(Enum):
    """验证规则"""
    PRICE_POSITIVE = "price_positive"
    PRICE_LOGIC = "price_logic"
    VOLUME_POSITIVE = "volume_positive"
    CHANGE_REASONABLE = "change_reasonable"
    TIMESTAMP_VALID = "timestamp_valid"
    DATA_COMPLETENESS = "data_completeness"
    DUPLICATE_CHECK = "duplicate_check"
    OUTLIER_DETECTION = "outlier_detection"


@dataclass
class ValidationResult:
    """验证结果"""
    rule: ValidationRule
    level: ValidationLevel
    passed: bool
    message: str
    details: Dict[str, Any] = None
    suggestion: str = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'rule': self.rule.value,
            'level': self.level.value,
            'passed': self.passed,
            'message': self.message,
            'details': self.details or {},
            'suggestion': self.suggestion
        }


@dataclass
class DataQualityReport:
    """数据质量报告"""
    symbol: str
    timestamp: datetime.datetime
    overall_score: float
    validation_results: List[ValidationResult]
    summary: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'overall_score': self.overall_score,
            'validation_results': [result.to_dict() for result in self.validation_results],
            'summary': self.summary
        }


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 验证配置
        self.max_daily_change_pct = 20.0  # 最大日涨跌幅
        self.max_intraday_change_pct = 10.0  # 最大盘中涨跌幅
        self.min_price = 0.01  # 最小价格
        self.max_price = 10000.0  # 最大价格
        self.outlier_threshold = 3.0  # 异常值检测阈值（标准差倍数）
        
        # 验证规则权重
        self.rule_weights = {
            ValidationRule.PRICE_POSITIVE: 0.2,
            ValidationRule.PRICE_LOGIC: 0.25,
            ValidationRule.VOLUME_POSITIVE: 0.1,
            ValidationRule.CHANGE_REASONABLE: 0.2,
            ValidationRule.TIMESTAMP_VALID: 0.1,
            ValidationRule.DATA_COMPLETENESS: 0.1,
            ValidationRule.DUPLICATE_CHECK: 0.05
        }
    
    def validate_market_data(self, market_data: Dict[str, Any]) -> DataQualityReport:
        """验证单个市场数据"""
        validation_results = []
        
        try:
            # 执行各项验证规则
            validation_results.append(self._validate_price_positive(market_data))
            validation_results.append(self._validate_price_logic(market_data))
            validation_results.append(self._validate_volume_positive(market_data))
            validation_results.append(self._validate_change_reasonable(market_data))
            validation_results.append(self._validate_timestamp(market_data))
            validation_results.append(self._validate_data_completeness(market_data))
            
            # 计算总体质量分数
            overall_score = self._calculate_quality_score(validation_results)
            
            # 生成摘要
            summary = self._generate_summary(validation_results)
            
            return DataQualityReport(
                symbol=market_data.get('symbol'),
                timestamp=market_data.get('timestamp'),
                overall_score=overall_score,
                validation_results=validation_results,
                summary=summary
            )
        
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return DataQualityReport(
                symbol=market_data.get('symbol'),
                timestamp=market_data.get('timestamp'),
                overall_score=0.0,
                validation_results=[],
                summary={'error': str(e)}
            )
    
    def validate_historical_data(self, symbol: str, df: pd.DataFrame) -> DataQualityReport:
        """验证历史数据"""
        validation_results = []
        
        try:
            if df.empty:
                return DataQualityReport(
                    symbol=symbol,
                    timestamp=datetime.datetime.now(),
                    overall_score=0.0,
                    validation_results=[],
                    summary={'error': '数据为空'}
                )
            
            # 批量验证规则
            validation_results.append(self._validate_df_price_positive(df))
            validation_results.append(self._validate_df_price_logic(df))
            validation_results.append(self._validate_df_volume_positive(df))
            validation_results.append(self._validate_df_change_reasonable(df))
            validation_results.append(self._validate_df_completeness(df))
            validation_results.append(self._validate_df_duplicates(df))
            validation_results.append(self._validate_df_outliers(df))
            
            # 计算总体质量分数
            overall_score = self._calculate_quality_score(validation_results)
            
            # 生成摘要
            summary = self._generate_df_summary(df, validation_results)
            
            return DataQualityReport(
                symbol=symbol,
                timestamp=datetime.datetime.now(),
                overall_score=overall_score,
                validation_results=validation_results,
                summary=summary
            )
        
        except Exception as e:
            self.logger.error(f"历史数据验证失败: {e}")
            return DataQualityReport(
                symbol=symbol,
                timestamp=datetime.datetime.now(),
                overall_score=0.0,
                validation_results=[],
                summary={'error': str(e)}
            )
    
    def _validate_price_positive(self, market_data: Dict[str, Any]) -> ValidationResult:
        """验证价格为正数"""
        prices = [market_data.get('open_price'), market_data.get('high_price'), 
                 market_data.get('low_price'), market_data.get('close_price')]
        
        negative_prices = [p for p in prices if p <= 0]
        
        if negative_prices:
            return ValidationResult(
                rule=ValidationRule.PRICE_POSITIVE,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"发现非正数价格: {negative_prices}",
                details={'negative_prices': negative_prices},
                suggestion="检查数据源，确保价格数据正确"
            )
        
        # 检查价格范围
        out_of_range = [p for p in prices if p < self.min_price or p > self.max_price]
        if out_of_range:
            return ValidationResult(
                rule=ValidationRule.PRICE_POSITIVE,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"价格超出合理范围: {out_of_range}",
                details={'out_of_range_prices': out_of_range},
                suggestion="检查价格是否异常"
            )
        
        return ValidationResult(
            rule=ValidationRule.PRICE_POSITIVE,
            level=ValidationLevel.INFO,
            passed=True,
            message="价格数据正常"
        )
    
    def _validate_price_logic(self, market_data: Dict[str, Any]) -> ValidationResult:
        """验证价格逻辑关系"""
        issues = []
        
        # 最高价应该是最高的
        if (market_data.get('high_price') < market_data.get('open_price') or
            market_data.get('high_price') < market_data.get('close_price') or
            market_data.get('high_price') < market_data.get('low_price')):
            issues.append("最高价不是最高值")
        
        # 最低价应该是最低的
        if (market_data.get('low_price') > market_data.get('open_price') or
            market_data.get('low_price') > market_data.get('close_price') or
            market_data.get('low_price') > market_data.get('high_price')):
            issues.append("最低价不是最低值")
        
        if issues:
            return ValidationResult(
                rule=ValidationRule.PRICE_LOGIC,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"价格逻辑错误: {', '.join(issues)}",
                details={'issues': issues},
                suggestion="检查OHLC数据的逻辑关系"
            )
        
        return ValidationResult(
            rule=ValidationRule.PRICE_LOGIC,
            level=ValidationLevel.INFO,
            passed=True,
            message="价格逻辑正常"
        )
    
    def _validate_volume_positive(self, market_data: Dict[str, Any]) -> ValidationResult:
        """验证成交量为正数"""
        volume = market_data.get('volume', 0)
        if volume < 0:
            return ValidationResult(
                rule=ValidationRule.VOLUME_POSITIVE,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"成交量为负数: {volume}",
                details={'volume': volume},
                suggestion="检查成交量数据源"
            )
        
        if volume == 0:
            return ValidationResult(
                rule=ValidationRule.VOLUME_POSITIVE,
                level=ValidationLevel.WARNING,
                passed=True,
                message="成交量为零，可能是停牌或数据缺失",
                details={'volume': volume},
                suggestion="确认是否为停牌期间"
            )
        
        return ValidationResult(
            rule=ValidationRule.VOLUME_POSITIVE,
            level=ValidationLevel.INFO,
            passed=True,
            message="成交量数据正常"
        )
    
    def _validate_change_reasonable(self, market_data: Dict[str, Any]) -> ValidationResult:
        """验证涨跌幅合理性"""
        change_pct = abs(market_data.get('change_pct', 0))
        
        if change_pct > self.max_daily_change_pct:
            return ValidationResult(
                rule=ValidationRule.CHANGE_REASONABLE,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"涨跌幅异常: {market_data.get('change_pct')}%",
                details={'change_pct': market_data.get('change_pct')},
                suggestion="检查是否有重大事件或数据错误"
            )
        
        # 检查涨跌额与涨跌幅的一致性
        close_price = market_data.get('close_price', 0)
        if close_price > 0:
            expected_change_pct = (market_data.get('change', 0) / close_price) * 100
            if abs(expected_change_pct - market_data.get('change_pct', 0)) > 0.1:
                return ValidationResult(
                    rule=ValidationRule.CHANGE_REASONABLE,
                    level=ValidationLevel.WARNING,
                    passed=True,
                    message="涨跌额与涨跌幅不一致",
                    details={
                        'reported_change_pct': market_data.get('change_pct'),
                        'calculated_change_pct': round(expected_change_pct, 2)
                    },
                    suggestion="检查涨跌额和涨跌幅计算"
                )
        
        return ValidationResult(
            rule=ValidationRule.CHANGE_REASONABLE,
            level=ValidationLevel.INFO,
            passed=True,
            message="涨跌幅数据正常"
        )
    
    def _validate_timestamp(self, market_data: Dict[str, Any]) -> ValidationResult:
        """验证时间戳"""
        now = datetime.datetime.now()
        timestamp = market_data.get('timestamp')
        
        if not timestamp:
            return ValidationResult(
                rule=ValidationRule.TIMESTAMP_VALID,
                level=ValidationLevel.ERROR,
                passed=False,
                message="缺少时间戳",
                details={},
                suggestion="添加时间戳字段"
            )
        
        # 检查时间是否在未来
        if timestamp > now + datetime.timedelta(minutes=5):
            return ValidationResult(
                rule=ValidationRule.TIMESTAMP_VALID,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"时间戳在未来: {timestamp}",
                details={'timestamp': timestamp.isoformat() if hasattr(timestamp, 'isoformat') else str(timestamp)},
                suggestion="检查系统时间或数据源时间"
            )
        
        # 检查时间是否过于久远
        if timestamp < now - datetime.timedelta(days=7):
            return ValidationResult(
                rule=ValidationRule.TIMESTAMP_VALID,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"时间戳较旧: {timestamp}",
                details={'timestamp': timestamp.isoformat() if hasattr(timestamp, 'isoformat') else str(timestamp)},
                suggestion="确认是否为历史数据"
            )
        
        return ValidationResult(
            rule=ValidationRule.TIMESTAMP_VALID,
            level=ValidationLevel.INFO,
            passed=True,
            message="时间戳正常"
        )
    
    def _validate_data_completeness(self, market_data: Dict[str, Any]) -> ValidationResult:
        """验证数据完整性"""
        missing_fields = []
        
        # 检查必要字段
        if not market_data.get('symbol'):
            missing_fields.append('symbol')
        if not market_data.get('name'):
            missing_fields.append('name')
        if market_data.get('open_price') is None:
            missing_fields.append('open_price')
        if market_data.get('close_price') is None:
            missing_fields.append('close_price')
        
        if missing_fields:
            return ValidationResult(
                rule=ValidationRule.DATA_COMPLETENESS,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"缺少必要字段: {missing_fields}",
                details={'missing_fields': missing_fields},
                suggestion="补充缺失的数据字段"
            )
        
        return ValidationResult(
            rule=ValidationRule.DATA_COMPLETENESS,
            level=ValidationLevel.INFO,
            passed=True,
            message="数据完整"
        )
    
    def _validate_df_price_positive(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame中价格为正数"""
        price_columns = ['open', 'high', 'low', 'close']
        negative_count = 0
        
        for col in price_columns:
            if col in df.columns:
                negative_count += (df[col] <= 0).sum()
        
        if negative_count > 0:
            return ValidationResult(
                rule=ValidationRule.PRICE_POSITIVE,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"发现 {negative_count} 个非正数价格",
                details={'negative_count': negative_count},
                suggestion="清理或修复非正数价格数据"
            )
        
        return ValidationResult(
            rule=ValidationRule.PRICE_POSITIVE,
            level=ValidationLevel.INFO,
            passed=True,
            message="所有价格数据为正数"
        )
    
    def _validate_df_price_logic(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame中价格逻辑"""
        if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            return ValidationResult(
                rule=ValidationRule.PRICE_LOGIC,
                level=ValidationLevel.WARNING,
                passed=True,
                message="缺少OHLC列，跳过价格逻辑检查"
            )
        
        # 检查价格逻辑
        logic_errors = (
            (df['high'] < df['low']) |
            (df['high'] < df['open']) |
            (df['high'] < df['close']) |
            (df['low'] > df['open']) |
            (df['low'] > df['close'])
        ).sum()
        
        if logic_errors > 0:
            return ValidationResult(
                rule=ValidationRule.PRICE_LOGIC,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"发现 {logic_errors} 个价格逻辑错误",
                details={'logic_errors': logic_errors},
                suggestion="检查并修复OHLC数据的逻辑关系"
            )
        
        return ValidationResult(
            rule=ValidationRule.PRICE_LOGIC,
            level=ValidationLevel.INFO,
            passed=True,
            message="价格逻辑正常"
        )
    
    def _validate_df_volume_positive(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame中成交量"""
        if 'volume' not in df.columns:
            return ValidationResult(
                rule=ValidationRule.VOLUME_POSITIVE,
                level=ValidationLevel.WARNING,
                passed=True,
                message="缺少成交量列"
            )
        
        negative_volume = (df['volume'] < 0).sum()
        zero_volume = (df['volume'] == 0).sum()
        
        if negative_volume > 0:
            return ValidationResult(
                rule=ValidationRule.VOLUME_POSITIVE,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"发现 {negative_volume} 个负成交量",
                details={'negative_volume': negative_volume},
                suggestion="修复负成交量数据"
            )
        
        if zero_volume > len(df) * 0.1:  # 超过10%的零成交量
            return ValidationResult(
                rule=ValidationRule.VOLUME_POSITIVE,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"零成交量比例较高: {zero_volume}/{len(df)}",
                details={'zero_volume_count': zero_volume, 'total_count': len(df)},
                suggestion="检查是否有大量停牌数据"
            )
        
        return ValidationResult(
            rule=ValidationRule.VOLUME_POSITIVE,
            level=ValidationLevel.INFO,
            passed=True,
            message="成交量数据正常"
        )
    
    def _validate_df_change_reasonable(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame中涨跌幅合理性"""
        if 'change_pct' not in df.columns:
            return ValidationResult(
                rule=ValidationRule.CHANGE_REASONABLE,
                level=ValidationLevel.WARNING,
                passed=True,
                message="缺少涨跌幅列"
            )
        
        extreme_changes = (abs(df['change_pct']) > self.max_daily_change_pct).sum()
        
        if extreme_changes > 0:
            return ValidationResult(
                rule=ValidationRule.CHANGE_REASONABLE,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"发现 {extreme_changes} 个极端涨跌幅",
                details={'extreme_changes': extreme_changes},
                suggestion="检查极端涨跌幅是否合理"
            )
        
        return ValidationResult(
            rule=ValidationRule.CHANGE_REASONABLE,
            level=ValidationLevel.INFO,
            passed=True,
            message="涨跌幅数据正常"
        )
    
    def _validate_df_completeness(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame数据完整性"""
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            return ValidationResult(
                rule=ValidationRule.DATA_COMPLETENESS,
                level=ValidationLevel.ERROR,
                passed=False,
                message=f"缺少必要列: {missing_columns}",
                details={'missing_columns': missing_columns},
                suggestion="补充缺失的数据列"
            )
        
        # 检查空值
        null_counts = df[required_columns].isnull().sum()
        total_nulls = null_counts.sum()
        
        if total_nulls > 0:
            return ValidationResult(
                rule=ValidationRule.DATA_COMPLETENESS,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"发现 {total_nulls} 个空值",
                details={'null_counts': null_counts.to_dict()},
                suggestion="处理空值数据"
            )
        
        return ValidationResult(
            rule=ValidationRule.DATA_COMPLETENESS,
            level=ValidationLevel.INFO,
            passed=True,
            message="数据完整"
        )
    
    def _validate_df_duplicates(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame重复数据"""
        duplicate_count = df.duplicated().sum()
        
        if duplicate_count > 0:
            return ValidationResult(
                rule=ValidationRule.DUPLICATE_CHECK,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"发现 {duplicate_count} 个重复行",
                details={'duplicate_count': duplicate_count},
                suggestion="删除重复数据"
            )
        
        return ValidationResult(
            rule=ValidationRule.DUPLICATE_CHECK,
            level=ValidationLevel.INFO,
            passed=True,
            message="无重复数据"
        )
    
    def _validate_df_outliers(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame异常值"""
        if 'close' not in df.columns or len(df) < 10:
            return ValidationResult(
                rule=ValidationRule.OUTLIER_DETECTION,
                level=ValidationLevel.INFO,
                passed=True,
                message="数据不足，跳过异常值检测"
            )
        
        # 使用Z-score检测异常值
        z_scores = np.abs((df['close'] - df['close'].mean()) / df['close'].std())
        outliers = (z_scores > self.outlier_threshold).sum()
        
        if outliers > 0:
            return ValidationResult(
                rule=ValidationRule.OUTLIER_DETECTION,
                level=ValidationLevel.WARNING,
                passed=True,
                message=f"发现 {outliers} 个可能的异常值",
                details={'outlier_count': outliers},
                suggestion="检查异常值是否为真实市场数据"
            )
        
        return ValidationResult(
            rule=ValidationRule.OUTLIER_DETECTION,
            level=ValidationLevel.INFO,
            passed=True,
            message="未发现明显异常值"
        )
    
    def _calculate_quality_score(self, validation_results: List[ValidationResult]) -> float:
        """计算数据质量分数"""
        total_score = 0.0
        total_weight = 0.0
        
        for result in validation_results:
            weight = self.rule_weights.get(result.rule, 0.1)
            total_weight += weight
            
            if result.passed:
                if result.level == ValidationLevel.INFO:
                    score = 1.0
                elif result.level == ValidationLevel.WARNING:
                    score = 0.7
                else:
                    score = 0.5
            else:
                if result.level == ValidationLevel.ERROR:
                    score = 0.0
                elif result.level == ValidationLevel.CRITICAL:
                    score = 0.0
                else:
                    score = 0.3
            
            total_score += score * weight
        
        return round(total_score / total_weight * 100, 2) if total_weight > 0 else 0.0
    
    def _generate_summary(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """生成验证摘要"""
        summary = {
            'total_rules': len(validation_results),
            'passed_rules': sum(1 for r in validation_results if r.passed),
            'failed_rules': sum(1 for r in validation_results if not r.passed),
            'warnings': sum(1 for r in validation_results if r.level == ValidationLevel.WARNING),
            'errors': sum(1 for r in validation_results if r.level == ValidationLevel.ERROR),
            'critical': sum(1 for r in validation_results if r.level == ValidationLevel.CRITICAL)
        }
        
        # 添加建议
        suggestions = [r.suggestion for r in validation_results if r.suggestion]
        if suggestions:
            summary['suggestions'] = suggestions
        
        return summary
    
    def _generate_df_summary(self, df: pd.DataFrame, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """生成DataFrame验证摘要"""
        summary = self._generate_summary(validation_results)
        
        # 添加数据统计
        summary.update({
            'total_rows': len(df),
            'date_range': {
                'start': df.index.min().isoformat() if not df.empty else None,
                'end': df.index.max().isoformat() if not df.empty else None
            },
            'columns': list(df.columns)
        })
        
        return summary


# 全局数据验证器实例
data_validator = DataValidator()


if __name__ == '__main__':
    # 测试数据验证模块
    print("=== 数据验证模块测试 ===")
    
    # 创建测试数据
    test_data = {
        'symbol': '510500',
        'name': '中证500ETF',
        'timestamp': datetime.datetime.now(),
        'open_price': 100.0,
        'high_price': 102.0,
        'low_price': 99.0,
        'close_price': 101.5,
        'volume': 1000000,
        'amount': 101500000.0,
        'change': 1.5,
        'change_pct': 1.5
    }
    
    # 测试单个数据验证
    print("\n1. 测试单个数据验证:")
    report = data_validator.validate_market_data(test_data)
    print(f"质量分数: {report.overall_score}")
    print(f"验证规则: {len(report.validation_results)} 个")
    print(f"通过: {report.summary['passed_rules']}, 失败: {report.summary['failed_rules']}")
    
    # 创建测试DataFrame
    test_df = pd.DataFrame({
        'open': [100.0, 101.0, 102.0],
        'high': [102.0, 103.0, 104.0],
        'low': [99.0, 100.0, 101.0],
        'close': [101.0, 102.0, 103.0],
        'volume': [1000000, 1100000, 1200000],
        'change_pct': [1.0, 1.0, 1.0]
    }, index=pd.date_range('2024-01-01', periods=3, freq='1min'))
    
    # 测试历史数据验证
    print("\n2. 测试历史数据验证:")
    df_report = data_validator.validate_historical_data('510500', test_df)
    print(f"质量分数: {df_report.overall_score}")
    print(f"数据行数: {df_report.summary['total_rows']}")
    print(f"验证结果: 通过 {df_report.summary['passed_rules']}, 失败 {df_report.summary['failed_rules']}")
    
    # 测试异常数据
    print("\n3. 测试异常数据验证:")
    bad_data = {
        'symbol': 'TEST',
        'name': '测试',
        'timestamp': datetime.datetime.now() + datetime.timedelta(days=1),  # 未来时间
        'open_price': -100.0,  # 负价格
        'high_price': 50.0,    # 逻辑错误
        'low_price': 200.0,    # 逻辑错误
        'close_price': 0.0,    # 零价格
        'volume': -1000,       # 负成交量
        'amount': 0.0,
        'change': 0.0,
        'change_pct': 50.0     # 极端涨跌幅
    }
    
    bad_report = data_validator.validate_market_data(bad_data)
    print(f"异常数据质量分数: {bad_report.overall_score}")
    print(f"错误数量: {bad_report.summary['errors']}")
    print(f"警告数量: {bad_report.summary['warnings']}")