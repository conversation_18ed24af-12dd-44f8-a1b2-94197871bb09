import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from dataclasses import dataclass
from utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    change_pct: float = 0.0
    turnover: float = 0.0
    
    def __post_init__(self):
        """计算涨跌幅"""
        if self.open_price > 0:
            self.change_pct = ((self.close_price - self.open_price) / self.open_price) * 100

class DataFetcher:
    """
    数据获取器类
    负责从akshare获取各种金融数据，包括日线、分钟线、波动率等数据
    """
    
    def __init__(self):
        """
        初始化数据获取器
        """
        # 标的映射关系
        self.symbol_mapping = {
            'zz1000': {
                'daily_symbol': 'sz399852',
                'minute_symbol': '399852',
                'name': '中证1000'
            },
            'zz500': {
                'daily_symbol': 'sh510500', 
                'minute_symbol': '399905',
                'name': '中证500指数'
            },
            'cyb': {
                'daily_symbol': 'sz159915',
                'minute_symbol': '159915', 
                'name': '创业板ETF'
            }
        }
    
    def _get_date_range(self, days_back: int = 60) -> tuple:
        """
        获取日期范围
        
        Args:
            days_back: 向前推多少天
            
        Returns:
            tuple: (start_date, end_date)
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        # 格式化日期 - 使用交易时间格式
        start_date_str = start_date.strftime("%Y-%m-%d 09:30:00")
        end_date_str = end_date.strftime("%Y-%m-%d 15:00:00")
        
        return start_date_str, end_date_str
    
    def get_daily_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        获取日线数据
        
        Args:
            symbol: 标的代码 ('zz1000', 'zz500', 'cyb')
            
        Returns:
            pd.DataFrame: 日线数据
        """
        try:
            if symbol not in self.symbol_mapping:
                logger.error(f"不支持的标的: {symbol}")
                return None
                
            symbol_info = self.symbol_mapping[symbol]
            daily_symbol = symbol_info['daily_symbol']
            
            logger.info(f"获取{symbol_info['name']}日线数据: {daily_symbol}")
            
            data = None
            if symbol == 'zz1000':
                # 中证1000指数的日线数据 - 使用腾讯接口
                data = ak.stock_zh_index_daily_tx(symbol=daily_symbol)
            elif symbol == 'zz500':
                # 中证500ETF的日线数据
                data = ak.stock_zh_index_daily_tx(symbol=daily_symbol)
            elif symbol == 'cyb':
                # 创业板ETF的日线数据
                data = ak.stock_zh_index_daily_tx(symbol=daily_symbol)
            else:
                logger.error(f"不支持的标的: {symbol}")
                return None
            
            logger.info(f"成功获取{symbol_info['name']}日线数据，共{len(data)}条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取{symbol}日线数据失败: {str(e)}")
            return None
    
    def get_minute_data(self, symbol: str, period: str = "5", days_back: int = 30) -> Optional[pd.DataFrame]:
        """
        获取分钟线数据
        
        Args:
            symbol: 标的代码 ('zz1000', 'zz500', 'cyb')
            period: 周期 ("5", "60")
            days_back: 向前推多少天
            
        Returns:
            pd.DataFrame: 分钟线数据
        """
        try:
            if symbol not in self.symbol_mapping:
                logger.error(f"不支持的标的: {symbol}")
                return None
                
            symbol_info = self.symbol_mapping[symbol]
            minute_symbol = symbol_info['minute_symbol']
            start_date, end_date = self._get_date_range(days_back)
            
            logger.info(f"获取{symbol_info['name']} {period}分钟线数据: {minute_symbol}")
            
            data = None
            if symbol == 'zz1000':
                # 中证1000 分钟线数据
                data = ak.index_zh_a_hist_min_em(
                    symbol=minute_symbol, 
                    period=period, 
                    start_date=start_date, 
                    end_date=end_date
                )
            elif symbol == 'zz500':
                # 中证500指数 分钟线数据
                data = ak.index_zh_a_hist_min_em(
                    symbol=minute_symbol, 
                    period=period, 
                    start_date=start_date, 
                    end_date=end_date
                )
            elif symbol == 'cyb':
                # 创业板ETF 分钟线数据
                data = ak.index_zh_a_hist_min_em(
                    symbol=minute_symbol, 
                    period=period, 
                    start_date=start_date, 
                    end_date=end_date
                )
            else:
                logger.error(f"不支持的标的: {symbol}")
                return None
            
            logger.info(f"成功获取{symbol_info['name']} {period}分钟线数据，共{len(data)}条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取{symbol} {period}分钟线数据失败: {str(e)}")
            return None
    
    def get_volatility_data(self, symbol: str, data_type: str = "historical") -> Optional[pd.DataFrame]:
        """
        获取波动率数据
        
        Args:
            symbol: 标的代码 ('zz1000', 'zz500', 'cyb')
            data_type: 数据类型 ('historical': 历史波动率, 'intraday': 分时波动率)
            
        Returns:
            pd.DataFrame: 波动率数据
        """
        try:
            if symbol not in self.symbol_mapping:
                logger.error(f"不支持的标的: {symbol}")
                return None
                
            symbol_info = self.symbol_mapping[symbol]
            logger.info(f"获取{symbol_info['name']}{data_type}波动率数据")
            
            data = None
            if symbol == 'zz1000':
                if data_type == "historical":
                    # 中证1000历史波动率
                    data = ak.index_option_1000index_qvix()
                else:
                    # 中证1000分时波动率
                    data = ak.index_option_1000index_min_qvix()
            elif symbol == 'zz500':
                if data_type == "historical":
                    # 中证500etf历史波动率
                    data = ak.index_option_500etf_qvix()
                else:
                    # 中证500etf分时波动率
                    data = ak.index_option_500etf_min_qvix()
            elif symbol == 'cyb':
                if data_type == "historical":
                    # 创业板历史波动率
                    data = ak.index_option_cyb_qvix()
                else:
                    # 创业板分时波动率
                    data = ak.index_option_cyb_min_qvix()
            else:
                logger.error(f"不支持的标的: {symbol}")
                return None
            
            logger.info(f"成功获取{symbol_info['name']}{data_type}波动率数据，共{len(data)}条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取{symbol} {data_type}波动率数据失败: {str(e)}")
            return None
    
    def get_option_stats_data(self, exchange: str = "both", date: str = None) -> Dict[str, Optional[pd.DataFrame]]:
        """
        获取期权统计数据
        
        Args:
            exchange: 交易所 ('sse': 上交所, 'szse': 深交所, 'both': 两个交易所)
            date: 日期，格式为"20250711"，默认为当前日期
            
        Returns:
            Dict[str, pd.DataFrame]: 期权统计数据
        """
        try:
            if date is None:
                date = datetime.now().strftime("%Y%m%d")
            
            result = {}
            
            if exchange in ['sse', 'both']:
                logger.info(f"获取上交所期权统计数据: {date}")
                try:
                    # 每日统计-上海证券交易所
                    sse_data = ak.option_daily_stats_sse(date=date)
                    result['sse'] = sse_data
                    logger.info(f"成功获取上交所期权统计数据，共{len(sse_data)}条记录")
                except Exception as e:
                    logger.error(f"获取上交所期权统计数据失败: {str(e)}")
                    result['sse'] = None
            
            if exchange in ['szse', 'both']:
                logger.info(f"获取深交所期权统计数据: {date}")
                try:
                    # 每日统计-深圳证券交易所
                    szse_data = ak.option_daily_stats_szse(date=date)
                    result['szse'] = szse_data
                    logger.info(f"成功获取深交所期权统计数据，共{len(szse_data)}条记录")
                except Exception as e:
                    logger.error(f"获取深交所期权统计数据失败: {str(e)}")
                    result['szse'] = None
            
            return result
            
        except Exception as e:
            logger.error(f"获取期权统计数据失败: {str(e)}")
            return {}
    
    def get_option_finance_board(self, symbol: str = "中证1000股指期权", end_month: str = None) -> Optional[pd.DataFrame]:
        """
        获取期权看板数据
        
        Args:
            symbol: 期权品种，默认为"中证1000股指期权"
            end_month: 到期月份，格式为"2507"，默认为当前月份后两个月
            
        Returns:
            pd.DataFrame: 期权看板数据
        """
        try:
            if end_month is None:
                # 默认获取当前月份后两个月的数据
                current_date = datetime.now()
                target_date = current_date + timedelta(days=60)
                end_month = target_date.strftime("%y%m")
            
            logger.info(f"获取{symbol}期权看板数据: {end_month}")
            
            # 获取期权看板数据
            data = ak.option_finance_board(symbol=symbol, end_month=end_month)
            
            logger.info(f"成功获取{symbol}期权看板数据，共{len(data)}条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取{symbol}期权看板数据失败: {str(e)}")
            return None
    
    def get_realtime_data(self, symbol: str) -> Optional[MarketData]:
        """
        获取实时市场数据
        
        Args:
            symbol: 标的代码 ('zz1000', 'zz500', 'cyb')
            
        Returns:
            MarketData: 实时市场数据
        """
        try:
            # 获取映射信息
            mapping = self.symbol_mapping.get(symbol)
            if not mapping:
                logger.error(f"未找到 {symbol} 的映射配置")
                return None
            
            # 使用日线数据接口获取最新数据
            daily_symbol = mapping['daily_symbol']
            
            # 获取最近1天的数据
            if symbol in ['zz1000', 'zz500']:
                # 指数数据
                data = ak.stock_zh_index_daily_tx(symbol=daily_symbol)
            else:
                # ETF数据
                data = ak.stock_zh_index_daily_tx(symbol=daily_symbol)
            
            if data.empty:
                logger.warning(f"未获取到 {symbol} 的实时数据")
                return None
            
            # 取最新一条数据
            latest = data.iloc[-1]
            
            # 创建MarketData对象
            market_data = MarketData(
                symbol=symbol,
                timestamp=pd.to_datetime(latest['date']) if 'date' in latest else datetime.now(),
                open_price=float(latest['open']),
                high_price=float(latest['high']),
                low_price=float(latest['low']),
                close_price=float(latest['close']),
                volume=int(latest['volume']) if 'volume' in latest else 0,
                turnover=float(latest.get('amount', 0))
            )
            
            logger.info(f"获取 {symbol} 实时数据成功: {market_data.close_price}")
            return market_data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 实时数据失败: {e}")
            return None
    
    def get_all_data(self, symbol: str, include_minute: bool = True, minute_periods: list = None) -> Dict[str, Any]:
        """
        获取指定标的的所有数据
        
        Args:
            symbol: 标的代码 ('zz1000', 'zz500', 'cyb')
            include_minute: 是否包含分钟线数据
            minute_periods: 分钟线周期列表，默认为["5", "60"]
            
        Returns:
            Dict[str, Any]: 包含所有数据的字典
        """
        if minute_periods is None:
            minute_periods = ["5", "60"]
            
        result = {
            'symbol': symbol,
            'daily_data': None,
            'minute_data': {},
            'volatility_data': {
                'historical': None,
                'intraday': None
            }
        }
        
        # 获取日线数据
        result['daily_data'] = self.get_daily_data(symbol)
        
        # 获取分钟线数据
        if include_minute:
            for period in minute_periods:
                result['minute_data'][f'{period}min'] = self.get_minute_data(symbol, period)
        
        # 获取波动率数据
        result['volatility_data']['historical'] = self.get_volatility_data(symbol, "historical")
        result['volatility_data']['intraday'] = self.get_volatility_data(symbol, "intraday")
        
        return result


# 创建全局数据获取器实例
data_fetcher = DataFetcher()


def get_data_fetcher() -> DataFetcher:
    """
    获取数据获取器实例
    
    Returns:
        DataFetcher: 数据获取器实例
    """
    return data_fetcher
 