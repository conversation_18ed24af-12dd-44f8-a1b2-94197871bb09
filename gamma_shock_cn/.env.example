# Gamma Shock CN 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# =============================================================================
# 基础配置
# =============================================================================

# 应用环境 (development/production/testing)
ENVIRONMENT=development

# 应用名称
APP_NAME=gamma-shock-cn

# 应用版本
APP_VERSION=1.0.0

# 调试模式 (true/false)
DEBUG=true

# 日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_LEVEL=INFO

# 时区设置
TIMEZONE=Asia/Shanghai

# =============================================================================
# AI 配置
# =============================================================================

# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_MAX_RETRIES=3
DEEPSEEK_TIMEOUT=30

# OpenAI API配置 (备用)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# AI分析配置
AI_ENABLED=true
AI_CONFIDENCE_THRESHOLD=0.7
AI_MAX_ANALYSIS_TIME=60

# =============================================================================
# 数据源配置
# =============================================================================

# AKShare配置
AKSHARE_TIMEOUT=30
AKSHARE_MAX_RETRIES=3
AKSHARE_RETRY_DELAY=1

# 数据更新间隔 (秒)
DATA_UPDATE_INTERVAL=300

# 数据缓存时间 (秒)
DATA_CACHE_TTL=3600

# 历史数据天数
HISTORY_DAYS=250

# =============================================================================
# 数据库配置
# =============================================================================

# SQLite数据库路径
DATABASE_URL=sqlite:///data/gamma_shock.db

# PostgreSQL配置 (可选)
# DATABASE_URL=postgresql://username:password@localhost:5432/gamma_shock
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=gamma_shock
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password

# 数据库连接池配置
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30

# =============================================================================
# Redis缓存配置
# =============================================================================

# Redis连接配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password
REDIS_TIMEOUT=5

# Redis连接池配置
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=20

# 缓存键前缀
REDIS_KEY_PREFIX=gamma_shock:

# =============================================================================
# 邮件通知配置
# =============================================================================

# 邮件服务配置
EMAIL_ENABLED=true
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USE_SSL=false

# 邮件账户配置
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Gamma Shock CN

# 收件人配置
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
EMAIL_CC=
EMAIL_BCC=

# 邮件模板配置
EMAIL_TEMPLATE_DIR=templates/email
EMAIL_CHARSET=utf-8

# =============================================================================
# 企业微信通知配置
# =============================================================================

# 企业微信机器人配置
WECHAT_ENABLED=false
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_webhook_key
WECHAT_TIMEOUT=10
WECHAT_MAX_RETRIES=3

# =============================================================================
# 交易配置
# =============================================================================

# 监控标的配置
MONITOR_SYMBOLS=zz1000,zz500_etf,cyb_etf

# 交易时间配置
TRADING_START_TIME=09:30
TRADING_END_TIME=15:00
TRADING_LUNCH_START=11:30
TRADING_LUNCH_END=13:00

# 信号配置
SIGNAL_THRESHOLD=2
SIGNAL_COOLDOWN=1800  # 30分钟冷却期

# 风险管理配置
MAX_POSITION_SIZE=1.0
STOP_LOSS_RATIO=0.02  # 2%止损
TAKE_PROFIT_RATIO=0.05  # 5%止盈

# =============================================================================
# 技术指标配置
# =============================================================================

# EMA配置
EMA_PERIODS=5,10,20,60

# KDJ配置
KDJ_PERIOD=14
KDJ_SMOOTH_K=3
KDJ_SMOOTH_D=3

# Williams %R配置
WILLIAMS_PERIODS=14,28

# 布林带配置
BOLLINGER_PERIOD=20
BOLLINGER_STD=2.0

# RSI配置
RSI_PERIOD=14
RSI_OVERBOUGHT=70
RSI_OVERSOLD=30

# MACD配置
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9

# =============================================================================
# 监控配置
# =============================================================================

# 系统监控配置
MONITOR_ENABLED=true
MONITOR_INTERVAL=60  # 监控间隔(秒)
HEALTH_CHECK_INTERVAL=300  # 健康检查间隔(秒)

# 性能监控配置
PERFORMANCE_MONITOR_ENABLED=true
MEMORY_THRESHOLD=80  # 内存使用率阈值(%)
CPU_THRESHOLD=80     # CPU使用率阈值(%)
DISK_THRESHOLD=90    # 磁盘使用率阈值(%)

# 错误监控配置
ERROR_ALERT_ENABLED=true
ERROR_THRESHOLD=5    # 错误次数阈值
ERROR_TIME_WINDOW=300  # 错误统计时间窗口(秒)

# =============================================================================
# 日志配置
# =============================================================================

# 日志文件配置
LOG_DIR=logs
LOG_FILE=gamma_shock.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 日志格式配置
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_DATE_FORMAT=%Y-%m-%d %H:%M:%S

# 控制台日志配置
CONSOLE_LOG_ENABLED=true
CONSOLE_LOG_LEVEL=INFO

# 文件日志配置
FILE_LOG_ENABLED=true
FILE_LOG_LEVEL=DEBUG

# =============================================================================
# 安全配置
# =============================================================================

# API安全配置
API_KEY_ENCRYPTION=true
API_RATE_LIMIT=100  # 每分钟请求限制

# 数据加密配置
DATA_ENCRYPTION_KEY=your_32_character_encryption_key
DATA_ENCRYPTION_ENABLED=false

# 访问控制配置
ALLOWED_IPS=127.0.0.1,localhost
API_TOKEN_EXPIRY=3600  # API令牌过期时间(秒)

# =============================================================================
# 备份配置
# =============================================================================

# 自动备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400  # 备份间隔(秒) - 24小时
BACKUP_RETENTION_DAYS=30  # 备份保留天数
BACKUP_COMPRESSION=true

# 备份存储配置
BACKUP_DIR=backups
BACKUP_REMOTE_ENABLED=false
BACKUP_REMOTE_PATH=/path/to/remote/backup

# =============================================================================
# 开发配置
# =============================================================================

# 开发模式配置
DEV_MODE=true
DEV_AUTO_RELOAD=true
DEV_MOCK_DATA=false

# 测试配置
TEST_DATABASE_URL=sqlite:///test_gamma_shock.db
TEST_REDIS_DB=1
TEST_EMAIL_ENABLED=false

# 调试配置
DEBUG_SQL=false
DEBUG_REDIS=false
DEBUG_API_CALLS=false

# =============================================================================
# Docker配置
# =============================================================================

# Docker容器配置
DOCKER_IMAGE_TAG=latest
DOCKER_CONTAINER_NAME=gamma-shock-cn
DOCKER_NETWORK=gamma-shock-network

# Docker Compose配置
COMPOSE_PROJECT_NAME=gamma-shock
COMPOSE_FILE=docker-compose.yml

# 容器资源限制
CONTAINER_MEMORY_LIMIT=512m
CONTAINER_CPU_LIMIT=1.0

# =============================================================================
# 生产环境配置
# =============================================================================

# 生产环境特定配置
PRODUCTION_MODE=false
PRODUCTION_WORKERS=4
PRODUCTION_MAX_REQUESTS=1000

# SSL/TLS配置
SSL_ENABLED=false
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# 负载均衡配置
LOAD_BALANCER_ENABLED=false
LOAD_BALANCER_ALGORITHM=round_robin

# =============================================================================
# 第三方服务配置
# =============================================================================

# Prometheus监控配置
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090
PROMETHEUS_METRICS_PATH=/metrics

# Grafana配置
GRAFANA_ENABLED=false
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin

# ELK Stack配置
ELASTICSEARCH_ENABLED=false
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200

KIBANA_ENABLED=false
KIBANA_PORT=5601

LOGSTASH_ENABLED=false
LOGSTASH_PORT=5044

# =============================================================================
# 自定义配置
# =============================================================================

# 用户自定义配置项
# 在此添加项目特定的配置变量

# 示例：
# CUSTOM_FEATURE_ENABLED=true
# CUSTOM_API_ENDPOINT=https://api.example.com
# CUSTOM_TIMEOUT=30